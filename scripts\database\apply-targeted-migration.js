#!/usr/bin/env node

/**
 * Apply Targeted Terminology Migration
 * Applies the targeted migration based on actual database state
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Use the known Supabase URL and service key
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyTargetedMigration() {
  console.log('🎯 Applying Targeted Terminology Migration...');
  console.log('=====================================');
  console.log('Based on actual database state: 4 tasks, empty tables\n');

  try {
    // Read the targeted migration file
    const migrationPath = path.join(__dirname, '../../supabase/migrations/20250625000002_terminology_update_targeted.sql');
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Migration file not found: ${migrationPath}`);
    }
    
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('1️⃣ Reading migration file...');
    console.log(`   📄 File: ${migrationPath}`);
    console.log(`   📊 Size: ${migrationSQL.length} characters`);

    // Since we can't use rpc('exec'), let's break down the migration into individual statements
    console.log('\n2️⃣ Applying migration in parts...');
    
    // Part 1: Add studio_type to teams
    console.log('   🏢 Adding studio_type to teams table...');
    try {
      // We'll need to apply this manually in the Supabase dashboard
      console.log('   ⚠️ Schema modifications require manual application in Supabase dashboard');
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }

    // Test what we can access after migration
    console.log('\n3️⃣ Testing database access...');
    
    // Test teams table
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .limit(1);

    if (teamsError) {
      console.log(`   ❌ Teams table: ${teamsError.message}`);
    } else {
      console.log('   ✅ Teams table accessible');
      if (teams.length > 0) {
        const columns = Object.keys(teams[0]);
        console.log(`      Columns: ${columns.join(', ')}`);
        if (columns.includes('studio_type')) {
          console.log('      ✅ studio_type column exists!');
        }
      }
    }

    // Test projects table
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .limit(1);

    if (projectsError) {
      console.log(`   ❌ Projects table: ${projectsError.message}`);
    } else {
      console.log('   ✅ Projects table accessible');
      if (projects.length > 0) {
        const columns = Object.keys(projects[0]);
        console.log(`      Columns: ${columns.join(', ')}`);
        if (columns.includes('project_type')) {
          console.log('      ✅ project_type column exists!');
        }
        if (columns.includes('studio_id')) {
          console.log('      ✅ studio_id column exists!');
        }
      }
    }

    // Test tasks table with new columns
    const { data: tasks, error: tasksError } = await supabase
      .from('tasks')
      .select('*')
      .limit(1);

    if (tasksError) {
      console.log(`   ❌ Tasks table: ${tasksError.message}`);
    } else {
      console.log('   ✅ Tasks table accessible');
      if (tasks.length > 0) {
        const columns = Object.keys(tasks[0]);
        console.log(`      Columns: ${columns.join(', ')}`);
        
        const missionColumns = columns.filter(col => 
          col.includes('mission') || col === 'task_category'
        );
        
        if (missionColumns.length > 0) {
          console.log(`      ✅ Mission columns: ${missionColumns.join(', ')}`);
        } else {
          console.log('      ❌ Mission columns missing');
        }
      }
    }

    // Test new tables
    console.log('\n4️⃣ Testing new terminology tables...');
    
    const newTables = [
      'studio_invitations',
      'studio_preferences', 
      'user_missions'
    ];

    for (const tableName of newTables) {
      const { data, error } = await supabase
        .from(tableName)
        .select('id')
        .limit(1);

      if (error) {
        console.log(`   ❌ ${tableName}: ${error.message}`);
      } else {
        console.log(`   ✅ ${tableName}: accessible`);
      }
    }

    console.log('\n📋 Migration Instructions:');
    console.log('=====================================');
    console.log('Since the API cannot modify the database schema, please:');
    console.log('');
    console.log('1. Go to Supabase SQL Editor:');
    console.log('   https://supabase.com/dashboard/project/hqqlrrqvjcetoxbdjgzx/sql/new');
    console.log('');
    console.log('2. Copy and paste the migration SQL from:');
    console.log(`   ${migrationPath}`);
    console.log('');
    console.log('3. Run the migration script');
    console.log('');
    console.log('4. Verify the new columns and tables are created');
    console.log('');
    console.log('📊 Current database state:');
    console.log('   • 4 tasks exist (will be updated with task_category)');
    console.log('   • 0 teams, projects, users, team_members');
    console.log('   • Safe to add all new terminology columns');
    console.log('   • No data conflicts or migration issues');

  } catch (error) {
    console.error('\n❌ Migration preparation failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Test database connection first
async function testConnection() {
  console.log('🔌 Testing database connection...');
  
  try {
    const { data, error } = await supabase
      .from('tasks')
      .select('id')
      .limit(1);

    if (error) {
      throw error;
    }

    console.log('✅ Database connection successful\n');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

// Main execution
async function main() {
  const connected = await testConnection();
  if (!connected) {
    process.exit(1);
  }

  await applyTargetedMigration();
}

// Handle command line execution
main().catch(console.error);

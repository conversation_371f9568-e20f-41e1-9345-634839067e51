import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>ody, <PERSON>dal<PERSON>ooter, Button, Input, Select, SelectItem, Card, CardBody, Chip, Avatar, Tabs, Tab } from '@heroui/react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';

/**
 * Member Management Component - Studio Member Management Interface
 * 
 * Features:
 * - Comprehensive member management with role assignment
 * - Member invitation system with email notifications
 * - Real-time member status and activity tracking
 * - Permission management and role-based access control
 * - Integration with studio management APIs
 */
const MemberManagement = ({ isOpen, onClose, studio, members, invitations, currentUser, onUpdate }) => {
  const [activeTab, setActiveTab] = useState('members');
  const [loading, setLoading] = useState(false);
  const [showInviteForm, setShowInviteForm] = useState(false);
  
  // Invitation form state
  const [inviteData, setInviteData] = useState({
    email: '',
    role: 'member',
    message: ''
  });

  // Role options
  const roles = [
    { key: 'founder', label: 'Founder', description: 'Full studio control', icon: '👑' },
    { key: 'owner', label: 'Owner', description: 'Administrative privileges', icon: '🛡️' },
    { key: 'admin', label: 'Admin', description: 'Member and project management', icon: '⚙️' },
    { key: 'member', label: 'Member', description: 'Standard participation', icon: '👤' },
    { key: 'contributor', label: 'Contributor', description: 'Limited participation', icon: '🤝' }
  ];

  // Get role color
  const getRoleColor = (role) => {
    const colors = {
      'founder': 'warning',
      'owner': 'primary',
      'admin': 'secondary',
      'member': 'default',
      'contributor': 'success'
    };
    return colors[role] || 'default';
  };

  // Get role icon
  const getRoleIcon = (role) => {
    const roleData = roles.find(r => r.key === role);
    return roleData?.icon || '👤';
  };

  // Format date
  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle member invitation
  const handleInviteMember = async () => {
    try {
      setLoading(true);
      
      if (!inviteData.email.trim()) {
        toast.error('Please enter an email address');
        return;
      }

      const response = await fetch(`/.netlify/functions/studio-management`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'invite_member',
          studio_id: studio.id,
          email: inviteData.email,
          role: inviteData.role,
          message: inviteData.message
        })
      });

      if (response.ok) {
        toast.success('Invitation sent successfully!');
        setInviteData({ email: '', role: 'member', message: '' });
        setShowInviteForm(false);
        onUpdate(); // Refresh data
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to send invitation');
      }
      
    } catch (error) {
      console.error('Error inviting member:', error);
      toast.error(error.message || 'Failed to send invitation');
    } finally {
      setLoading(false);
    }
  };

  // Handle role change
  const handleRoleChange = async (memberId, newRole) => {
    try {
      setLoading(true);
      
      const response = await fetch(`/.netlify/functions/studio-management`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'update_member',
          studio_id: studio.id,
          member_id: memberId,
          role: newRole
        })
      });

      if (response.ok) {
        toast.success('Member role updated successfully!');
        onUpdate(); // Refresh data
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update member role');
      }
      
    } catch (error) {
      console.error('Error updating member role:', error);
      toast.error(error.message || 'Failed to update member role');
    } finally {
      setLoading(false);
    }
  };

  // Handle member removal
  const handleRemoveMember = async (memberId) => {
    try {
      if (!confirm('Are you sure you want to remove this member?')) {
        return;
      }

      setLoading(true);
      
      const response = await fetch(`/.netlify/functions/studio-management`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'remove_member',
          studio_id: studio.id,
          member_id: memberId
        })
      });

      if (response.ok) {
        toast.success('Member removed successfully!');
        onUpdate(); // Refresh data
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to remove member');
      }
      
    } catch (error) {
      console.error('Error removing member:', error);
      toast.error(error.message || 'Failed to remove member');
    } finally {
      setLoading(false);
    }
  };

  // Handle invitation resend
  const handleResendInvitation = async (invitationId) => {
    try {
      setLoading(true);
      
      const response = await fetch(`/.netlify/functions/studio-management`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'resend_invitation',
          invitation_id: invitationId
        })
      });

      if (response.ok) {
        toast.success('Invitation resent successfully!');
        onUpdate(); // Refresh data
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to resend invitation');
      }
      
    } catch (error) {
      console.error('Error resending invitation:', error);
      toast.error(error.message || 'Failed to resend invitation');
    } finally {
      setLoading(false);
    }
  };

  // Render members tab
  const renderMembersTab = () => (
    <div className="space-y-4">
      {/* Add Member Button */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Studio Members ({members.length})</h3>
        <Button
          color="primary"
          onClick={() => setShowInviteForm(true)}
          startContent={<span>➕</span>}
        >
          Invite Member
        </Button>
      </div>

      {/* Members List */}
      <div className="space-y-3">
        {members.map((member, index) => (
          <motion.div
            key={member.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card className="hover:shadow-md transition-shadow">
              <CardBody className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Avatar
                      src={member.user?.avatar_url}
                      name={member.user?.display_name || member.user?.email}
                      size="md"
                    />
                    <div>
                      <div className="font-semibold">
                        {member.user?.display_name || 'Unknown User'}
                      </div>
                      <div className="text-sm text-default-600">
                        {member.user?.email}
                      </div>
                      <div className="text-xs text-default-500">
                        Joined {formatDate(member.joined_at)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Chip
                      color={getRoleColor(member.role)}
                      variant="flat"
                      startContent={<span>{getRoleIcon(member.role)}</span>}
                    >
                      {member.role}
                    </Chip>
                    
                    {member.user_id !== currentUser?.id && (
                      <div className="flex gap-2">
                        <Select
                          size="sm"
                          selectedKeys={[member.role]}
                          onSelectionChange={(keys) => 
                            handleRoleChange(member.id, Array.from(keys)[0])
                          }
                          className="min-w-32"
                          disabled={loading}
                        >
                          {roles.map((role) => (
                            <SelectItem key={role.key} value={role.key}>
                              {role.icon} {role.label}
                            </SelectItem>
                          ))}
                        </Select>
                        
                        <Button
                          size="sm"
                          color="danger"
                          variant="flat"
                          onClick={() => handleRemoveMember(member.id)}
                          disabled={loading}
                        >
                          Remove
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>

      {members.length === 0 && (
        <div className="text-center py-8">
          <div className="text-4xl mb-4">👥</div>
          <h3 className="text-lg font-semibold mb-2">No Members Yet</h3>
          <p className="text-default-600 mb-4">
            Start building your studio by inviting members
          </p>
          <Button
            color="primary"
            onClick={() => setShowInviteForm(true)}
          >
            Invite First Member
          </Button>
        </div>
      )}
    </div>
  );

  // Render invitations tab
  const renderInvitationsTab = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Pending Invitations ({invitations.length})</h3>

      <div className="space-y-3">
        {invitations.map((invitation, index) => (
          <motion.div
            key={invitation.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card className="hover:shadow-md transition-shadow">
              <CardBody className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-semibold">{invitation.email}</div>
                    <div className="text-sm text-default-600">
                      Role: {invitation.role} • Sent {formatDate(invitation.created_at)}
                    </div>
                    {invitation.message && (
                      <div className="text-sm text-default-500 mt-1">
                        "{invitation.message}"
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Chip
                      color={invitation.status === 'pending' ? 'warning' : 'default'}
                      variant="flat"
                    >
                      {invitation.status}
                    </Chip>
                    
                    {invitation.status === 'pending' && (
                      <Button
                        size="sm"
                        color="primary"
                        variant="flat"
                        onClick={() => handleResendInvitation(invitation.id)}
                        disabled={loading}
                      >
                        Resend
                      </Button>
                    )}
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>

      {invitations.length === 0 && (
        <div className="text-center py-8">
          <div className="text-4xl mb-4">📧</div>
          <h3 className="text-lg font-semibold mb-2">No Pending Invitations</h3>
          <p className="text-default-600">
            All invitations have been processed
          </p>
        </div>
      )}
    </div>
  );

  // Render invite form
  const renderInviteForm = () => (
    <Card className="mb-6">
      <CardBody className="p-6">
        <h4 className="text-lg font-semibold mb-4">Invite New Member</h4>
        
        <div className="space-y-4">
          <Input
            label="Email Address"
            placeholder="Enter email address"
            value={inviteData.email}
            onChange={(e) => setInviteData(prev => ({ ...prev, email: e.target.value }))}
            variant="bordered"
            isRequired
          />
          
          <Select
            label="Role"
            selectedKeys={[inviteData.role]}
            onSelectionChange={(keys) => 
              setInviteData(prev => ({ ...prev, role: Array.from(keys)[0] }))
            }
            variant="bordered"
          >
            {roles.filter(role => role.key !== 'founder').map((role) => (
              <SelectItem key={role.key} value={role.key}>
                <div className="flex items-center gap-2">
                  <span>{role.icon}</span>
                  <div>
                    <div>{role.label}</div>
                    <div className="text-xs text-default-500">{role.description}</div>
                  </div>
                </div>
              </SelectItem>
            ))}
          </Select>
          
          <Input
            label="Personal Message (Optional)"
            placeholder="Add a personal message to the invitation"
            value={inviteData.message}
            onChange={(e) => setInviteData(prev => ({ ...prev, message: e.target.value }))}
            variant="bordered"
          />
          
          <div className="flex gap-2">
            <Button
              color="primary"
              onClick={handleInviteMember}
              disabled={!inviteData.email.trim() || loading}
              isLoading={loading}
            >
              Send Invitation
            </Button>
            <Button
              color="default"
              variant="bordered"
              onClick={() => setShowInviteForm(false)}
            >
              Cancel
            </Button>
          </div>
        </div>
      </CardBody>
    </Card>
  );

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="4xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h2 className="text-2xl font-bold">Member Management</h2>
          <p className="text-default-600 font-normal">
            Manage studio members, roles, and invitations
          </p>
        </ModalHeader>
        
        <ModalBody>
          {showInviteForm && renderInviteForm()}
          
          <Tabs 
            selectedKey={activeTab} 
            onSelectionChange={setActiveTab}
            className="mb-6"
          >
            <Tab key="members" title={`Members (${members.length})`}>
              {renderMembersTab()}
            </Tab>
            <Tab key="invitations" title={`Invitations (${invitations.length})`}>
              {renderInvitationsTab()}
            </Tab>
          </Tabs>
        </ModalBody>
        
        <ModalFooter>
          <Button color="danger" variant="flat" onPress={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default MemberManagement;

/**
 * Multiple Business Scenarios Test
 * 
 * Tests the variable-based agreement generation system with various
 * business types to ensure proper variable substitution and no
 * cross-contamination between different projects.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🎯 MULTIPLE BUSINESS SCENARIOS TEST');
console.log('=' .repeat(50));
console.log('Testing variable-based generation for various business types');
console.log('=' .repeat(50));

/**
 * Test scenarios for different business types
 */
const TEST_SCENARIOS = {
  VOTA_GAME: {
    name: 'Village of The Ages',
    description: 'A village simulation game where players guide communities through historical progressions and manage resource-based challenges',
    project_type: 'game',
    company_name: 'City of Gamers Inc.',
    address: '1205 43rd Street, Suite B, Orlando, Florida 32839',
    contact_email: '<EMAIL>',
    features: 'The game features dynamic challenges based on resource scarcity and natural disasters, creating an engaging gameplay loop.',
    coreFeatures: `1. **Village Building & Management**
   - Resource gathering and management
   - Building placement and construction
   - Population growth and management
   - Cultural evolution systems

2. **Historical Progression**
   - Players guide their community through multiple historical eras
   - Technology tree advancement
   - Cultural and social development
   - Architectural evolution`,
    technicalRequirements: `- Platform: PC (Steam, Epic Games Store)
- Engine: Unreal Engine 5
- Minimum Specs: Standard hardware requirements for the target platforms
- Art Style: Stylized, readable visuals with distinctive aesthetics
- Audio: Atmospheric soundtrack with appropriate sound design`,
    roadmapPhases: `**Phase 1: Core Gameplay Development (Months 1-2)**
- Basic village layout and building system
- Core resource gathering mechanics
- Initial AI for villagers
- Basic UI framework
- First playable prototype with one historical era`,
    milestones: [
      {
        title: 'Core Gameplay Development',
        description: 'Basic village layout and building system',
        dueDate: 'Months 1-2'
      }
    ]
  },

  SOFTWARE_PROJECT: {
    name: 'TaskFlow Pro',
    description: 'A comprehensive project management platform designed for remote teams',
    project_type: 'software',
    company_name: 'Productivity Solutions LLC',
    address: '456 Innovation Drive, Austin, TX 78701',
    contact_email: '<EMAIL>',
    features: 'Advanced collaboration tools with real-time synchronization and AI-powered insights.',
    coreFeatures: `1. **Task Management**
   - Kanban boards and Gantt charts
   - Priority-based task assignment
   - Deadline tracking and notifications
   - Progress visualization

2. **Team Collaboration**
   - Real-time chat and video calls
   - File sharing and version control
   - Comment threads on tasks
   - Team performance analytics`,
    technicalRequirements: `- Platform: Web-based (React/Node.js)
- Database: PostgreSQL with Redis caching
- AI/ML: TensorFlow integration
- Hosting: AWS cloud infrastructure
- Mobile: React Native apps for iOS/Android`,
    roadmapPhases: `**Phase 1: Core Platform (Months 1-3)**
- User authentication and workspace setup
- Basic task management functionality
- Team invitation and role management
- File upload and basic collaboration`,
    milestones: [
      {
        title: 'MVP Launch',
        description: 'Core task management and team collaboration features',
        dueDate: 'Month 3'
      }
    ]
  },

  MUSIC_PROJECT: {
    name: 'Harmony Symphony',
    description: 'A collaborative music production project creating original orchestral compositions',
    project_type: 'music',
    company_name: 'Melody Studios Inc.',
    address: '789 Music Row, Nashville, TN 37203',
    contact_email: '<EMAIL>',
    features: 'Professional orchestral recordings with modern production techniques.',
    coreFeatures: `1. **Composition**
   - Original orchestral arrangements
   - Modern classical fusion elements
   - Thematic musical storytelling

2. **Recording**
   - Professional studio sessions
   - High-quality audio capture
   - Multi-track recording techniques`,
    technicalRequirements: `- Studio: Professional recording facility
- Equipment: High-end microphones and mixing boards
- Software: Pro Tools, Logic Pro
- Format: 48kHz/24-bit WAV, MP3 distribution
- Distribution: Streaming platforms and physical media`,
    roadmapPhases: `**Phase 1: Pre-Production (Month 1)**
- Composition finalization
- Musician recruitment
- Studio booking and setup`,
    milestones: [
      {
        title: 'Composition Complete',
        description: 'All musical arrangements finalized and approved',
        dueDate: 'Month 1'
      }
    ]
  },

  FILM_PROJECT: {
    name: 'Independent Vision',
    description: 'An independent film production focusing on contemporary social themes',
    project_type: 'film',
    company_name: 'Visionary Films LLC',
    address: '321 Hollywood Blvd, Los Angeles, CA 90028',
    contact_email: '<EMAIL>',
    features: 'Character-driven narrative with innovative cinematography and sound design.',
    coreFeatures: `1. **Screenplay Development**
   - Character-driven storytelling
   - Contemporary social themes
   - Innovative narrative structure

2. **Production**
   - Professional cinematography
   - Location shooting
   - Actor direction and performance`,
    technicalRequirements: `- Camera: RED Digital Cinema cameras
- Audio: Professional boom and wireless systems
- Editing: Avid Media Composer, DaVinci Resolve
- Format: 4K production, multiple distribution formats
- Distribution: Film festivals, streaming platforms`,
    roadmapPhases: `**Phase 1: Pre-Production (Months 1-2)**
- Script finalization
- Casting and crew hiring
- Location scouting and permits`,
    milestones: [
      {
        title: 'Pre-Production Complete',
        description: 'All planning, casting, and preparation finished',
        dueDate: 'Month 2'
      }
    ]
  }
};

/**
 * Simple template processor
 */
function processTemplate(template, data) {
  let processed = template;
  
  // Replace basic placeholders
  processed = processed.replace(/\[Project Name\]/g, data.name || '[Project Name]');
  processed = processed.replace(/\[Project Description\]/g, data.description || '[Project Description]');
  processed = processed.replace(/\[Project Features\]/g, data.features || '[Project Features]');
  processed = processed.replace(/\[Project Core Features\]/g, data.coreFeatures || '[Project Core Features]');
  processed = processed.replace(/\[Project Technical Requirements\]/g, data.technicalRequirements || '[Project Technical Requirements]');
  processed = processed.replace(/\[Project Roadmap Phases\]/g, data.roadmapPhases || '[Project Roadmap Phases]');
  
  // Format milestones
  if (data.milestones && data.milestones.length > 0) {
    const formattedMilestones = data.milestones.map((milestone, index) => {
      const title = milestone.title || `Milestone ${index + 1}`;
      const description = milestone.description || '';
      const dueDate = milestone.dueDate || '';

      let formatted = `${index + 1}. **${title}**`;
      if (dueDate) formatted += ` (${dueDate})`;
      if (description) formatted += `\n   - ${description}`;

      return formatted;
    }).join('\n\n');
    
    processed = processed.replace(/\[Project Milestones\]/g, formattedMilestones);
  }
  
  // Replace company information only if different from default
  if (data.company_name && data.company_name !== 'City of Gamers Inc.') {
    processed = processed.replace(/City of Gamers Inc\./g, data.company_name);
  }
  
  if (data.address && data.address !== '1205 43rd Street, Suite B, Orlando, Florida 32839') {
    processed = processed.replace(/1205 43rd Street, Suite B, Orlando, Florida 32839/g, data.address);
  }
  
  if (data.contact_email && data.contact_email !== '<EMAIL>') {
    processed = processed.replace(/billing@cogfuture\.com/g, data.contact_email);
  }
  
  return processed;
}

/**
 * Check for cross-contamination between agreements
 */
function checkCrossContamination(agreements) {
  const contaminationDetails = [];
  
  // Define unique identifiers for each scenario
  const uniqueIdentifiers = {
    VOTA_GAME: ['Village of The Ages', 'village simulation', 'historical progressions', 'architectural evolution'],
    SOFTWARE_PROJECT: ['TaskFlow Pro', 'project management platform', 'remote teams', 'AI-powered insights'],
    MUSIC_PROJECT: ['Harmony Symphony', 'orchestral compositions', 'Melody Studios', 'Music Row'],
    FILM_PROJECT: ['Independent Vision', 'film production', 'Visionary Films', 'Hollywood Blvd']
  };
  
  // Check each agreement against others
  for (const [sourceScenario, sourceAgreement] of Object.entries(agreements)) {
    for (const [targetScenario, targetIdentifiers] of Object.entries(uniqueIdentifiers)) {
      if (sourceScenario !== targetScenario) {
        for (const identifier of targetIdentifiers) {
          if (sourceAgreement.includes(identifier)) {
            contaminationDetails.push({
              source: sourceScenario,
              target: targetScenario,
              content: identifier
            });
          }
        }
      }
    }
  }
  
  return {
    totalContamination: contaminationDetails.length,
    details: contaminationDetails
  };
}

/**
 * Validate that expected content is present
 */
function validateContent(agreement, scenario, scenarioName) {
  const expectedElements = [
    scenario.name,
    scenario.description,
    scenario.company_name,
    scenario.features
  ];
  
  // Extract key phrases from core features
  const coreFeatureLines = scenario.coreFeatures ? scenario.coreFeatures.split('\n') : [];
  const keyPhrases = coreFeatureLines
    .filter(line => line.includes('**') || line.includes('-'))
    .map(line => line.replace(/[*\-]/g, '').trim())
    .filter(phrase => phrase.length > 5)
    .slice(0, 3); // Take first 3 key phrases
  
  const allElements = [...expectedElements.filter(e => e), ...keyPhrases];
  
  let found = 0;
  const missing = [];
  
  for (const element of allElements) {
    if (agreement.includes(element)) {
      found++;
    } else {
      missing.push(element);
    }
  }
  
  return {
    accuracy: Math.round((found / allElements.length) * 100),
    found,
    total: allElements.length,
    missing
  };
}

/**
 * Main test function
 */
async function runMultipleBusinessTest() {
  try {
    console.log('\n🚀 Starting multiple business scenarios test...\n');
    
    // Load template
    const templatePath = path.join(__dirname, '../client/public/contributor-agreement-template.md');
    const template = fs.readFileSync(templatePath, 'utf8');
    
    const agreements = {};
    const validationResults = {};
    
    // Generate agreements for all scenarios
    console.log('📋 Generating agreements for all business scenarios...');
    for (const [scenarioName, scenarioData] of Object.entries(TEST_SCENARIOS)) {
      console.log(`   Processing: ${scenarioName}`);
      
      const agreement = processTemplate(template, scenarioData);
      agreements[scenarioName] = agreement;
      
      // Save the agreement
      const outputPath = path.join(__dirname, `business-${scenarioName.toLowerCase()}.md`);
      fs.writeFileSync(outputPath, agreement);
      
      // Validate content
      const validation = validateContent(agreement, scenarioData, scenarioName);
      validationResults[scenarioName] = {
        ...validation,
        outputFile: outputPath
      };
      
      console.log(`     Content accuracy: ${validation.accuracy}%`);
    }
    
    // Check for cross-contamination
    console.log('\n🚫 Checking for cross-contamination...');
    const contaminationResults = checkCrossContamination(agreements);
    
    // Calculate overall results
    const totalScenarios = Object.keys(TEST_SCENARIOS).length;
    const passedScenarios = Object.values(validationResults).filter(r => r.accuracy >= 95).length;
    const overallAccuracy = Math.round((passedScenarios / totalScenarios) * 100);
    
    // Display results
    console.log('\n📊 MULTIPLE BUSINESS SCENARIOS RESULTS');
    console.log('=' .repeat(50));
    
    for (const [scenarioName, result] of Object.entries(validationResults)) {
      console.log(`${result.accuracy >= 95 ? '✅' : '❌'} ${scenarioName}: ${result.accuracy}% accuracy`);
      if (result.missing.length > 0) {
        console.log(`   Missing: ${result.missing.slice(0, 2).join(', ')}${result.missing.length > 2 ? '...' : ''}`);
      }
    }
    
    console.log(`\n🚫 Cross-contamination: ${contaminationResults.totalContamination} instances`);
    if (contaminationResults.totalContamination > 0) {
      contaminationResults.details.slice(0, 3).forEach(detail => {
        console.log(`   ${detail.source} → ${detail.target}: "${detail.content}"`);
      });
    }
    
    console.log(`\n📈 Overall Results:`);
    console.log(`   Scenarios passed: ${passedScenarios}/${totalScenarios}`);
    console.log(`   Overall accuracy: ${overallAccuracy}%`);
    console.log(`   Cross-contamination: ${contaminationResults.totalContamination === 0 ? 'NONE' : contaminationResults.totalContamination + ' instances'}`);
    console.log(`   ${overallAccuracy >= 95 && contaminationResults.totalContamination === 0 ? '✅ PASSED' : '❌ FAILED'}`);
    
    // Save comprehensive report
    const report = {
      timestamp: new Date().toISOString(),
      overallAccuracy,
      passedScenarios,
      totalScenarios,
      crossContamination: contaminationResults.totalContamination,
      passed: overallAccuracy >= 95 && contaminationResults.totalContamination === 0,
      scenarios: validationResults,
      contaminationDetails: contaminationResults.details
    };
    
    const reportPath = path.join(__dirname, 'multiple-business-scenarios-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📊 Report saved to: ${reportPath}`);
    
    return report;
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    throw error;
  }
}

// Run the test
runMultipleBusinessTest()
  .then(report => {
    console.log(`\n✅ Test completed with ${report.overallAccuracy}% overall accuracy`);
    process.exit(report.passed ? 0 : 1);
  })
  .catch(error => {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  });

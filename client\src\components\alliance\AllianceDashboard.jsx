import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Avatar, AvatarGroup } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';
import AllianceCreationWizard from './AllianceCreationWizard';
import MemberManagement from './MemberManagement';
import BusinessModelConfig from './BusinessModelConfig';
import AllianceTreasury from './AllianceTreasury';
import AllianceVentures from './AllianceVentures';
import AllianceMembers from './AllianceMembers';
import AllianceAnalytics from './AllianceAnalytics';

/**
 * Studio Dashboard Component - Main Studio Management Interface
 * 
 * Features:
 * - Bento grid layout following exact wireframe specifications
 * - Real-time studio data with automatic updates
 * - Integration with studio management APIs
 * - Member management and invitation system
 * - Business model configuration and revenue tracking
 * - Project integration and project management
 */
const AllianceDashboard = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [studioData, setStudioData] = useState({
    currentAlliance: null,
    userAlliances: [],
    members: [],
    projects: [],
    invitations: [],
    statistics: {
      totalMembers: 0,
      activeVentures: 0,
      monthlyRevenue: 0,
      completedTasks: 0
    }
  });
  
  const [loading, setLoading] = useState(true);
  const [showCreationWizard, setShowCreationWizard] = useState(false);
  const [showMemberManagement, setShowMemberManagement] = useState(false);
  const [showBusinessConfig, setShowBusinessConfig] = useState(false);

  // Load studio data from backend APIs
  const loadAllianceData = async () => {
    try {
      setLoading(true);
      
      // Fetch user's studios
      const alliancesResponse = await fetch('/.netlify/functions/studios', {
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (alliancesResponse.ok) {
        const alliancesData = await alliancesResponse.json();
        const userAlliances = alliancesData.studios || [];
        
        // Set current studio (first one or user's primary)
        const currentAlliance = userAlliances.find(a => a.user_role === 'founder') || userAlliances[0] || null;
        
        if (currentAlliance) {
          // Fetch detailed studio data
          const detailResponse = await fetch(`/.netlify/functions/studios/${currentAlliance.id}`, {
            headers: {
              'Authorization': `Bearer ${currentUser?.access_token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (detailResponse.ok) {
            const detailData = await detailResponse.json();
            
            setStudioData({
              currentAlliance: detailData,
              userAlliances,
              members: detailData.members || [],
              projects: detailData.projects || [],
              invitations: detailData.invitations || [],
              statistics: {
                totalMembers: detailData.members?.length || 0,
                activeVentures: detailData.projects?.filter(v => v.status === 'active').length || 0,
                monthlyRevenue: detailData.monthly_revenue || 0,
                completedTasks: detailData.completed_tasks || 0
              }
            });
          }
        } else {
          setStudioData(prev => ({ ...prev, userAlliances }));
        }
      }
      
    } catch (error) {
      console.error('Error loading studio data:', error);
      toast.error('Failed to load studio data');
    } finally {
      setLoading(false);
    }
  };

  // Format currency display
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Get role color
  const getRoleColor = (role) => {
    const colors = {
      'founder': 'warning',
      'owner': 'primary',
      'admin': 'secondary',
      'member': 'default',
      'contributor': 'success'
    };
    return colors[role] || 'default';
  };

  // Get role icon
  const getRoleIcon = (role) => {
    const icons = {
      'founder': '👑',
      'owner': '🛡️',
      'admin': '⚙️',
      'member': '👤',
      'contributor': '🤝'
    };
    return icons[role] || '👤';
  };

  // Initialize component
  useEffect(() => {
    if (currentUser) {
      loadAllianceData();
    }
  }, [currentUser]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-default-600">Loading studio dashboard...</p>
        </div>
      </div>
    );
  }

  // No studio state
  if (!studioData.currentAlliance && studioData.userAlliances.length === 0) {
    return (
      <div className={`studio-dashboard ${className}`}>
        <div className="text-center py-12">
          <div className="text-6xl mb-6">🏰</div>
          <h2 className="text-2xl font-bold mb-4">Welcome to Studios</h2>
          <p className="text-default-600 mb-8 max-w-md mx-auto">
            Create or join an studio to collaborate with other professionals, 
            manage projects, and build your network.
          </p>
          <div className="flex gap-4 justify-center">
            <Button
              color="primary"
              size="lg"
              onClick={() => setShowCreationWizard(true)}
              startContent={<span>🏰</span>}
            >
              Create Studio
            </Button>
            <Button
              color="default"
              variant="bordered"
              size="lg"
              onClick={() => {
                // Navigate to studio discovery
                toast.info('Studio discovery coming soon');
              }}
              startContent={<span>🔍</span>}
            >
              Discover Studios
            </Button>
          </div>
        </div>
        
        {/* Creation Wizard Modal */}
        {showCreationWizard && (
          <AllianceCreationWizard
            isOpen={showCreationWizard}
            onClose={() => setShowCreationWizard(false)}
            onSuccess={() => {
              setShowCreationWizard(false);
              loadAllianceData(); // Refresh data
            }}
            currentUser={currentUser}
          />
        )}
      </div>
    );
  }

  const { currentAlliance, statistics, members } = studioData;

  return (
    <div className={`studio-dashboard ${className}`}>
      {/* Dashboard Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-2">
              🏰 {currentAlliance?.name || 'Studio Dashboard'}
            </h1>
            <p className="text-default-600">
              {currentAlliance?.description || 'Manage your studio and collaborate with members'}
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              color="primary"
              variant="flat"
              onClick={() => setShowMemberManagement(true)}
              startContent={<span>👥</span>}
            >
              Manage Members
            </Button>
            <Button
              color="secondary"
              variant="flat"
              onClick={() => setShowBusinessConfig(true)}
              startContent={<span>💼</span>}
            >
              Business Model
            </Button>
          </div>
        </div>
      </div>

      {/* Bento Grid Layout - Exact Wireframe Specifications */}
      <div className="grid grid-cols-12 gap-6">
        {/* Top Row: Studio Status (4×2) + Treasury (2×2) */}
        <div className="col-span-12 lg:col-span-8">
          {/* Studio Status - Large Card (4×2) */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="mb-6"
          >
            <Card className="bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-800/20 hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-2">
                    <span className="text-3xl">🏰</span>
                    <h2 className="text-xl font-bold">Studio Status</h2>
                  </div>
                  <Chip color="primary" variant="flat" size="sm">
                    4×2
                  </Chip>
                </div>
              </CardHeader>
              <CardBody className="space-y-4">
                {/* Studio Header */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-2xl font-bold text-purple-600">
                      🏰 {currentAlliance?.name || 'Crimson Phoenix Studio'}
                    </h3>
                    <Chip color="warning" variant="flat">
                      Level 7
                    </Chip>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-default-600">
                    <span>👑 Leader: {currentUser?.display_name || 'Alex Chen'}</span>
                    <span>📅 Founded: March 2024</span>
                    <span>🎯 Rank: #3 of 47 studios</span>
                  </div>
                </div>

                {/* Studio Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm text-default-600">🎯 Focus:</span>
                      <span className="ml-2 font-medium">Web Development & Innovation</span>
                    </div>
                    <div>
                      <span className="text-sm text-default-600">💡 Motto:</span>
                      <span className="ml-2 font-medium">"Innovation through collaboration"</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-default-600">Active Members</div>
                        <div className="font-semibold">{statistics.totalMembers}</div>
                      </div>
                      <div>
                        <div className="text-default-600">Active Projects</div>
                        <div className="font-semibold">{statistics.activeVentures}</div>
                      </div>
                      <div>
                        <div className="text-default-600">Success Rate</div>
                        <div className="font-semibold">92%</div>
                      </div>
                      <div>
                        <div className="text-default-600">Quality Score</div>
                        <div className="font-semibold">4.7/5</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Recent Achievements */}
                <div>
                  <div className="text-sm text-default-600 mb-2">🏆 Recent Achievements:</div>
                  <div className="flex gap-2 flex-wrap">
                    <Chip color="warning" size="sm" variant="flat">Top 3 Studio</Chip>
                    <Chip color="success" size="sm" variant="flat">Revenue Master</Chip>
                    <Chip color="primary" size="sm" variant="flat">Team Builder</Chip>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 pt-2">
                  <Button color="primary" variant="flat" size="sm">
                    Edit Studio
                  </Button>
                  <Button color="default" variant="flat" size="sm">
                    View History
                  </Button>
                  <Button color="secondary" variant="flat" size="sm">
                    Settings
                  </Button>
                </div>
              </CardBody>
            </Card>
          </motion.div>

        </div>

        {/* Right Side: Treasury (2×2) */}
        <div className="col-span-12 lg:col-span-4">
          <AllianceTreasury
            studioId={currentAlliance?.id}
            className="mb-6"
          />
        </div>

        {/* Second Row: Active Projects (6×2) */}
        <div className="col-span-12">
          <AllianceVentures
            studioId={currentAlliance?.id}
            className="mb-6"
          />
        </div>

        {/* Third Row: Members (2×1) + Performance (2×1) + Level (1×1) + Quick Actions (1×1) */}
        <div className="col-span-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
          {/* Members (2×1) */}
          <div className="lg:col-span-2">
            <AllianceMembers
              studioId={currentAlliance?.id}
              members={members}
              onManageMembers={() => setShowMemberManagement(true)}
            />
          </div>

          {/* Performance (2×1) */}
          <div className="lg:col-span-2">
            <AllianceAnalytics
              studioId={currentAlliance?.id}
            />
          </div>

          {/* Level (1×1) */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
            >
              <Card className="bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-800/20 hover:shadow-lg transition-shadow h-full">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                      <span className="text-2xl">🏆</span>
                      <h3 className="text-lg font-semibold">Level</h3>
                    </div>
                    <Chip color="warning" variant="flat" size="sm">
                      1×1
                    </Chip>
                  </div>
                </CardHeader>
                <CardBody className="text-center space-y-2">
                  <div className="text-3xl font-bold text-yellow-600">
                    Level 7
                  </div>
                  <div className="text-sm text-default-600">
                    Rank #3
                  </div>
                  <div className="text-xs text-default-500">
                    47 Total
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          </div>

          {/* Quick Actions (1×1) */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.5 }}
            >
              <Card className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-800/20 hover:shadow-lg transition-shadow h-full">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                      <span className="text-2xl">⚡</span>
                      <h3 className="text-lg font-semibold">Quick</h3>
                    </div>
                    <Chip color="primary" variant="flat" size="sm">
                      1×1
                    </Chip>
                  </div>
                </CardHeader>
                <CardBody className="space-y-2">
                  <Button
                    color="primary"
                    variant="flat"
                    size="sm"
                    className="w-full"
                    onClick={() => setShowMemberManagement(true)}
                  >
                    Invite Member
                  </Button>
                  <Button
                    color="success"
                    variant="flat"
                    size="sm"
                    className="w-full"
                    onClick={() => {
                      toast.info('Create project coming soon');
                    }}
                  >
                    Create Project
                  </Button>
                  <Button
                    color="secondary"
                    variant="flat"
                    size="sm"
                    className="w-full"
                    onClick={() => {
                      toast.info('Studio settings coming soon');
                    }}
                  >
                    Settings
                  </Button>
                </CardBody>
              </Card>
            </motion.div>
          </div>
        </div>


      </div>

      {/* Modals */}
      {showCreationWizard && (
        <AllianceCreationWizard
          isOpen={showCreationWizard}
          onClose={() => setShowCreationWizard(false)}
          onSuccess={() => {
            setShowCreationWizard(false);
            loadAllianceData(); // Refresh data
          }}
          currentUser={currentUser}
        />
      )}

      {showMemberManagement && (
        <MemberManagement
          isOpen={showMemberManagement}
          onClose={() => setShowMemberManagement(false)}
          studio={currentAlliance}
          members={members}
          invitations={studioData.invitations}
          currentUser={currentUser}
          onUpdate={() => loadAllianceData()}
        />
      )}

      {showBusinessConfig && (
        <BusinessModelConfig
          isOpen={showBusinessConfig}
          onClose={() => setShowBusinessConfig(false)}
          studio={currentAlliance}
          currentUser={currentUser}
          onUpdate={() => loadAllianceData()}
        />
      )}
    </div>
  );
};

export default AllianceDashboard;

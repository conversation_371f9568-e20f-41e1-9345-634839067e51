import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Divider } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * AllianceTreasury Component - Financial Overview and Distribution
 * 
 * Features:
 * - Real-time revenue tracking and growth metrics
 * - ORB points balance and studio pool management
 * - Monthly revenue distribution and per-member calculations
 * - Bonus pool management and distribution controls
 * - Financial analytics and performance indicators
 */
const AllianceTreasury = ({ allianceId, className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [treasuryData, setTreasuryData] = useState({
    revenue: 47200,
    growth: 18,
    orbPoints: 8400,
    bonusPool: 2500,
    monthlyRevenue: 8400,
    perMemberRevenue: 700,
    memberCount: 12
  });

  // Format currency display
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Format ORB points
  const formatORB = (amount) => {
    return new Intl.NumberFormat('en-US').format(amount);
  };

  // Load treasury data
  const loadTreasuryData = async () => {
    try {
      setLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      const response = await fetch(`/.netlify/functions/studio-management/${allianceId}/treasury`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTreasuryData(data);
      } else {
        // Use mock data for now
        console.log('Using mock treasury data');
      }
      
    } catch (error) {
      console.error('Error loading treasury data:', error);
      // Continue with mock data
    } finally {
      setLoading(false);
    }
  };

  // Handle revenue distribution
  const handleDistribution = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      const response = await fetch(`/.netlify/functions/studio-management/${allianceId}/distribute-revenue`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        toast.success('Revenue distributed successfully!');
        loadTreasuryData(); // Refresh data
      } else {
        throw new Error('Failed to distribute revenue');
      }
      
    } catch (error) {
      console.error('Error distributing revenue:', error);
      toast.error('Failed to distribute revenue');
    }
  };

  useEffect(() => {
    if (allianceId && currentUser) {
      loadTreasuryData();
    }
  }, [allianceId, currentUser]);

  if (loading) {
    return (
      <Card className={`h-full ${className}`}>
        <CardBody className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardBody>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Card className="bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-800/20 hover:shadow-lg transition-shadow h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">💰</span>
              <h3 className="text-lg font-semibold">Treasury</h3>
            </div>
            <Chip color="warning" variant="flat" size="sm">
              2×2
            </Chip>
          </div>
        </CardHeader>
        <CardBody className="space-y-4">
          {/* Revenue Overview */}
          <div className="space-y-3">
            <div>
              <div className="text-sm text-default-600">Revenue</div>
              <div className="text-2xl font-bold text-yellow-600">
                {formatCurrency(treasuryData.revenue)}
              </div>
              <div className="flex items-center gap-1 text-sm">
                <span className="text-green-600">Growth: +{treasuryData.growth}%</span>
                <span className="text-green-600">↗️</span>
              </div>
            </div>

            <Divider />

            {/* ORB Points */}
            <div>
              <div className="text-sm text-default-600">💎 Studio Pool</div>
              <div className="text-lg font-semibold text-purple-600">
                {formatORB(treasuryData.orbPoints)} ORB Points
              </div>
            </div>

            {/* Bonus Pool */}
            <div>
              <div className="text-sm text-default-600">🏆 Bonus Pool</div>
              <div className="text-lg font-semibold text-orange-600">
                {formatCurrency(treasuryData.bonusPool)} available
              </div>
            </div>

            <Divider />

            {/* Monthly Distribution */}
            <div className="space-y-2">
              <div>
                <div className="text-sm text-default-600">Monthly</div>
                <div className="text-lg font-semibold">
                  {formatCurrency(treasuryData.monthlyRevenue)}
                </div>
              </div>
              <div>
                <div className="text-sm text-default-600">Per Member</div>
                <div className="text-lg font-semibold">
                  {formatCurrency(treasuryData.perMemberRevenue)}
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-2 pt-2">
            <Button
              color="warning"
              variant="flat"
              className="w-full"
              onClick={handleDistribution}
            >
              Distribute
            </Button>
            <Button
              color="default"
              variant="bordered"
              className="w-full"
              onClick={() => {
                toast.info('Treasury details coming soon');
              }}
            >
              View Details
            </Button>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default AllianceTreasury;

#!/usr/bin/env node

/**
 * Update Component Terminology Script
 * Updates component names, variable names, and internal references
 * to use new terminology while preserving functionality
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Component-level terminology mappings
const COMPONENT_MAPPINGS = {
  // Component names and references
  'QuestBoard': 'MissionBoard',
  'QuestCard': 'MissionCard', 
  'QuestCreator': 'MissionCreator',
  'questData': 'missionData',
  'availableQuests': 'availableMissions',
  'activeQuests': 'activeMissions',
  'completedQuests': 'completedMissions',
  'questId': 'missionId',
  'questType': 'missionType',
  'questStatus': 'missionStatus',
  'questProgress': 'missionProgress',
  'questRewards': 'missionRewards',
  'questRequirements': 'missionRequirements',
  'setQuestData': 'setMissionData',
  'loadQuests': 'loadMissions',
  'createQuest': 'createMission',
  'updateQuest': 'updateMission',
  'deleteQuest': 'deleteMission',
  'startQuest': 'startMission',
  'completeQuest': 'completeMission',
  
  // Alliance/Studio component mappings
  'AllianceBoard': 'StudioBoard',
  'AllianceCard': 'StudioCard',
  'AllianceCreator': 'StudioCreator',
  'allianceData': 'studioData',
  'allianceId': 'studioId',
  'allianceType': 'studioType',
  'allianceStatus': 'studioStatus',
  'setAllianceData': 'setStudioData',
  'loadAlliances': 'loadStudios',
  'createAlliance': 'createStudio',
  'updateAlliance': 'updateStudio',
  'deleteAlliance': 'deleteStudio',
  
  // Venture/Project component mappings
  'VentureBoard': 'ProjectBoard',
  'VentureCard': 'ProjectCard',
  'VentureCreator': 'ProjectCreator',
  'ventureData': 'projectData',
  'ventureId': 'projectId',
  'ventureType': 'projectType',
  'ventureStatus': 'projectStatus',
  'setVentureData': 'setProjectData',
  'loadVentures': 'loadProjects',
  'createVenture': 'createProject',
  'updateVenture': 'updateProject',
  'deleteVenture': 'deleteProject',

  // Comments and documentation
  'Mission Board Component - Gamified Mission Discovery': 'Mission Board Component - Gamified Mission Discovery',
  'mission system APIs': 'mission system APIs',
  'mission discovery': 'mission discovery',
  'mission management': 'mission management',
  'mission recommendations': 'mission recommendations',
  'Studio Management Interface': 'Studio Management Interface',
  'studio system APIs': 'studio system APIs',
  'studio discovery': 'studio discovery',
  'studio management': 'studio management',
  'Project Management Interface': 'Project Management Interface',
  'project system APIs': 'project system APIs',
  'project discovery': 'project discovery',
  'project management': 'project management'
};

// Files that need component-level updates
const COMPONENT_FILES = [
  'client/src/components/quests/QuestBoard.jsx',
  'client/src/components/quests/QuestCard.jsx',
  'client/src/components/quests/QuestCreator.jsx',
  'client/src/components/alliance/AllianceBoard.jsx',
  'client/src/components/alliance/AllianceCard.jsx',
  'client/src/components/alliance/AllianceCreator.jsx',
  'client/src/components/alliance/AllianceDashboard.jsx',
  'client/src/components/alliance/AllianceManage.jsx',
  'client/src/components/alliance/AllianceList.jsx',
  'client/src/components/venture/VentureBoard.jsx',
  'client/src/components/venture/VentureCard.jsx',
  'client/src/components/venture/VentureCreator.jsx'
];

function updateComponentContent(content) {
  let updatedContent = content;
  
  // Apply component-level mappings
  for (const [oldTerm, newTerm] of Object.entries(COMPONENT_MAPPINGS)) {
    // Use word boundaries for variable names and exact matches for component names
    const regex = new RegExp(`\\b${oldTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g');
    updatedContent = updatedContent.replace(regex, newTerm);
  }
  
  return updatedContent;
}

async function updateComponentTerminology() {
  console.log('🔧 Updating Component-Level Terminology...');
  console.log('=====================================\n');

  const projectRoot = path.join(__dirname, '..');
  let updatedFiles = 0;
  let totalFiles = 0;

  console.log('1️⃣ Updating component files...');
  
  for (const filePath of COMPONENT_FILES) {
    const fullPath = path.join(projectRoot, filePath);
    
    if (fs.existsSync(fullPath)) {
      try {
        totalFiles++;
        const relativePath = path.relative(projectRoot, fullPath);
        
        // Read current content
        const originalContent = fs.readFileSync(fullPath, 'utf8');
        
        // Apply component-level updates
        const updatedContent = updateComponentContent(originalContent);
        
        // Check if content actually changed
        if (originalContent !== updatedContent) {
          // Write updated content
          fs.writeFileSync(fullPath, updatedContent, 'utf8');
          console.log(`   ✅ Updated: ${relativePath}`);
          updatedFiles++;
        } else {
          console.log(`   ⏭️ No changes: ${relativePath}`);
        }
        
      } catch (error) {
        console.error(`   ❌ Error updating ${fullPath}: ${error.message}`);
      }
    } else {
      console.log(`   ⚠️ File not found: ${filePath}`);
    }
  }

  console.log('\n📊 Component Update Summary:');
  console.log('=====================================');
  console.log(`   Total files processed: ${totalFiles}`);
  console.log(`   Files updated: ${updatedFiles}`);
  console.log(`   Files unchanged: ${totalFiles - updatedFiles}`);

  console.log('\n🎯 Component Updates Applied:');
  console.log('=====================================');
  console.log('   ✅ QuestBoard → MissionBoard');
  console.log('   ✅ QuestCard → MissionCard');
  console.log('   ✅ QuestCreator → MissionCreator');
  console.log('   ✅ Variable names updated (questData → missionData)');
  console.log('   ✅ Function names updated (loadQuests → loadMissions)');
  console.log('   ✅ Component references updated');

  console.log('\n🔍 Next Steps:');
  console.log('=====================================');
  console.log('1. Test components to ensure functionality is preserved');
  console.log('2. Update any remaining import statements');
  console.log('3. Check for any TypeScript type definitions that need updating');
  console.log('4. Verify all component exports and imports are correct');
  console.log('5. Run the application to test the updated components');
}

// Main execution
updateComponentTerminology().catch(console.error);

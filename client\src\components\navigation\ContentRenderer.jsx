import React, { Suspense, lazy, useState } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button } from '@heroui/react';
import SimpleLoading from '../layout/SimpleLoading';
import SectionNavigator from './SectionNavigator';
import SectionRenderer from './SectionRenderer';
import useCanvasDefinitions from '../../hooks/useCanvasDefinitions';

/**
 * ContentRenderer Component
 *
 * Dynamically renders content based on the active canvas and current route.
 * Provides smooth transitions between different canvas areas while preserving
 * all existing page functionality.
 */

// Lazy load all page components for better performance
const ModernDashboard = lazy(() => import('../../pages/user/ModernDashboard'));
const SimpleLogin = lazy(() => import('../../pages/user/SimpleLogin'));
const ProfilePage = lazy(() => import('../../pages/user/ProfilePage'));
const PublicProfile = lazy(() => import('../../pages/user/PublicProfile'));
const RetroProfilePage = lazy(() => import('../../pages/user/RetroProfilePage'));
const PasswordResetPage = lazy(() => import('../../pages/user/PasswordResetPage'));
const UpdatePasswordPage = lazy(() => import('../../pages/user/UpdatePasswordPage'));
const SettingsPage = lazy(() => import('../../pages/user/SettingsPage'));
const AuthCallback = lazy(() => import('../../pages/auth/Callback'));
const RoadmapManagerPage = lazy(() => import('../../pages/admin/RoadmapManager'));
const ProfileMigration = lazy(() => import('../../pages/admin/ProfileMigration'));
const AdminDashboard = lazy(() => import('../../pages/admin/AdminDashboard'));
const ComponentPlayground = lazy(() => import('../../pages/admin/ComponentPlayground'));
const BugReportPage = lazy(() => import('../../pages/bug/BugReportPage'));
const ProjectWizard = lazy(() => import('../../pages/project/ProjectWizard'));
const ProjectDetail = lazy(() => import('../../pages/project/ProjectDetail'));
const ProjectsList = lazy(() => import('../../pages/project/ProjectsList'));
const ContributionTrackerPage = lazy(() => import('../../pages/contribution/ContributionTrackerPage'));
const ContributionValidatePage = lazy(() => import('../../pages/contribution/validate'));
const ContributionsPage = lazy(() => import('../../pages/contribution/ContributionsPage'));
const ValidationMetricsPage = lazy(() => import('../../pages/validation/ValidationMetricsPage'));
const ContributionAnalyticsPage = lazy(() => import('../../pages/analytics/ContributionAnalyticsPage'));
const RevenuePage = lazy(() => import('../../pages/revenue/RevenuePage'));
const RoyaltyCalculatorPage = lazy(() => import('../../pages/royalty/RoyaltyCalculatorPage'));
const KanbanPage = lazy(() => import('../../pages/kanban/KanbanPage'));
const ProjectAgreements = lazy(() => import('../../pages/project/ProjectAgreements'));
const AuthDebugPage = lazy(() => import('../../pages/debug/AuthDebugPage'));
const NotFound = lazy(() => import('../../pages/NotFound'));
const TrackPage = lazy(() => import('../../pages/track/TrackPage'));
const StartPage = lazy(() => import('../../pages/start/StartPage'));
const TrackCanvas = lazy(() => import('../canvas/TrackCanvas'));
const EarnCanvas = lazy(() => import('../canvas/EarnCanvas'));
const LearnPage = lazy(() => import('../../pages/learn/LearnPage'));
const NotificationsPage = lazy(() => import('../../pages/notification/NotificationsPage'));
const InvitationsPage = lazy(() => import('../../pages/invitation/InvitationsPage'));
const SocialPage = lazy(() => import('../../pages/social/SocialPage'));
const HelpPage = lazy(() => import('../../pages/help/HelpPage'));
const EscrowPage = lazy(() => import('../../pages/escrow/EscrowPage'));
const InsightsPage = lazy(() => import('../../pages/analytics/InsightsPage'));
const SystemPage = lazy(() => import('../../pages/admin/SystemPage'));
// Studio components (enhanced team system with business compliance)
const TeamList = lazy(() => import('../team/TeamList'));
const TeamDetail = lazy(() => import('../team/TeamDetail'));
const TeamManage = lazy(() => import('../team/TeamManage'));
const TeamInvitations = lazy(() => import('../team/TeamInvitations'));
// Enhanced Studio Components (New Terminology)
const StudioManage = lazy(() => import('../studio/StudioManage'));
const StudioDashboard = lazy(() => import('../studio/StudioDashboard'));
const StudioCreationWizard = lazy(() => import('../studio/StudioCreationWizard'));
const StudioCreationPage = lazy(() => import('../../pages/alliance/AllianceCreationPage'));
const MyTeams = lazy(() => import('../../sections/teams/MyTeams'));
const StudioList = lazy(() => import('../studio/StudioList'));

// Legacy Alliance Components (for backward compatibility)
const AllianceManage = lazy(() => import('../alliance/AllianceManage'));
const AllianceDashboard = lazy(() => import('../alliance/AllianceDashboard'));
const AllianceCreationWizard = lazy(() => import('../alliance/AllianceCreationWizard'));
const AllianceCreationPage = lazy(() => import('../../pages/alliance/AllianceCreationPage'));
const AllianceList = lazy(() => import('../alliance/AllianceList'));
const AlliancePage = lazy(() => import('../../pages/alliance/AlliancePage'));
// Company Management Components
const CompanyManagePage = lazy(() => import('../../pages/company/CompanyManagePage'));
// Enhanced Analytics Components
const EnhancedAnalyticsDashboard = lazy(() => import('../analytics/AnalyticsDashboard'));
// Enhanced Revenue Components
const EnhancedRevenuePage = lazy(() => import('../../pages/revenue/EnhancedRevenuePage'));
const AuthRoute = lazy(() => import('../auth/AuthRoute'));
const TestUI = lazy(() => import('../ui/test-ui'));
const ButtonTest = lazy(() => import('../../pages/test/ButtonTest'));
const UIPolishTestSuite = lazy(() => import('../testing/UIPolishTestSuite'));
const NavigationEnhancementTest = lazy(() => import('../testing/NavigationEnhancementTest'));
const EnvironmentTest = lazy(() => import('../../pages/test/EnvironmentTest'));
const EnvironmentTestPage = lazy(() => import('../../pages/test/EnvironmentTestPage'));
const MissionBoardIntegrationTest = lazy(() => import('../../pages/test/MissionBoardIntegrationTest'));
const TrackPageIntegrationTest = lazy(() => import('../../pages/test/TrackPageIntegrationTest'));
const QuickRouteIntegrationTest = lazy(() => import('../../pages/test/QuickRouteIntegrationTest'));
const ProjectPageIntegrationTest = lazy(() => import('../../pages/test/VenturePageIntegrationTest'));
// Mission and Vetting Components (New Terminology)
const MissionBoard = lazy(() => import('../missions/MissionBoard'));
const MissionCreator = lazy(() => import('../missions/MissionCreator'));
const MissionDetail = lazy(() => import('../missions/MissionDetail'));
const MissionPage = lazy(() => import('../../pages/missions/MissionBoardPage'));
const BountyBoard = lazy(() => import('../bounty/BountyBoard'));
const BountyPage = lazy(() => import('../../pages/bounty/BountyPage'));
const SkillVerificationDashboard = lazy(() => import('../vetting/SkillVerificationDashboard'));

// Project Components (New Terminology)
const ProjectPage = lazy(() => import('../../pages/ventures/VenturePage'));

// Legacy Components (for backward compatibility)
const VenturePageIntegrationTest = lazy(() => import('../../pages/test/VenturePageIntegrationTest'));
const QuestBoard = lazy(() => import('../quests/QuestBoard'));
const QuestPage = lazy(() => import('../../pages/quests/QuestPage'));
const VenturePage = lazy(() => import('../../pages/ventures/VenturePage'));
// Missing page components
const MissingPage = lazy(() => import('../../pages/MissingPage'));
const TeamsPage = lazy(() => import('../../pages/teams/TeamsPage'));
const AnalyticsPage = lazy(() => import('../../pages/analytics/AnalyticsPage'));
const MissionBoardPage = lazy(() => import('../../pages/missions/MissionBoardPage'));

const ContentRenderer = ({ currentUser, currentCanvas }) => {
  const location = useLocation();
  const [viewMode, setViewMode] = useState('sections'); // 'sections' or 'fullpage'
  const canvasDefinitions = useCanvasDefinitions(currentUser, false);

  // Routes that should bypass the canvas section system and go directly to route components
  const directRoutes = [
    '/teams', '/studios', '/studios/create', '/studios', '/studios/create',
    '/teams/', '/studios/', '/studios/', // with trailing slash
    '/projects', '/project/', '/missions', '/missions', '/bounties', '/vetting', '/projects'
  ];

  // Check if current path should bypass canvas sections
  const shouldBypassSections = directRoutes.some(route =>
    location.pathname === route ||
    location.pathname.startsWith(route + '/') ||
    location.pathname.startsWith(route.slice(0, -1) + '/') // handle trailing slash variants
  );

  // Canvas-specific background styling
  const getCanvasBackground = (canvas) => {
    const backgrounds = {
      home: 'from-blue-500/5 via-purple-500/5 to-slate-900',
      projects: 'from-green-500/5 via-emerald-500/5 to-slate-900',
      wizard: 'from-violet-500/5 via-purple-500/5 to-slate-900',
      kanban: 'from-cyan-500/5 via-blue-500/5 to-slate-900',
      missions: 'from-purple-500/5 via-indigo-500/5 to-slate-900',
      contributions: 'from-orange-500/5 via-red-500/5 to-slate-900',
      validation: 'from-emerald-500/5 via-green-500/5 to-slate-900',
      earn: 'from-yellow-500/5 via-orange-500/5 to-slate-900',
      revenue: 'from-green-500/5 via-emerald-500/5 to-slate-900',
      royalty: 'from-pink-500/5 via-rose-500/5 to-slate-900',
      analytics: 'from-blue-500/5 via-cyan-500/5 to-slate-900',
      profile: 'from-purple-500/5 via-pink-500/5 to-slate-900',
      settings: 'from-gray-500/5 via-slate-500/5 to-slate-900',
      learn: 'from-indigo-500/5 via-blue-500/5 to-slate-900',
      admin: 'from-red-500/5 via-rose-500/5 to-slate-900',
      bugs: 'from-red-500/5 via-orange-500/5 to-slate-900',
    };
    return backgrounds[canvas] || 'from-slate-500/5 to-slate-900';
  };

  // Loading component with canvas theming
  const CanvasLoading = ({ canvas }) => (
    <div className={`min-h-screen bg-gradient-to-br ${getCanvasBackground(canvas)} flex items-center justify-center`}>
      <Card className="bg-white/10 backdrop-blur-md">
        <CardBody className="p-8 text-center">
          <SimpleLoading text={`Loading ${canvas} canvas...`} />
        </CardBody>
      </Card>
    </div>
  );

  // Check if current canvas supports sections
  const currentCanvasData = canvasDefinitions[currentCanvas];
  const hasSections = currentCanvasData && currentCanvasData.sections && Object.keys(currentCanvasData.sections).length > 0;

  // Define canvases that should ALWAYS use full-page mode (bypass sections)
  const fullPageCanvases = [
    'start', 'track', 'earn', 'contributions', 'analytics', 'insights',
    'profile', 'social', 'settings', 'notifications', 'bugs', 'learn', 'help'
  ];

  // Check if current canvas should use full-page mode
  const shouldUseFullPage = fullPageCanvases.includes(currentCanvas) || !hasSections;

  // Render section-based content
  const renderSectionContent = () => {
    // Special canvas components for enhanced experiences
    if (currentCanvas === 'contributions' || currentCanvas === 'track') {
      return <TrackCanvas currentUser={currentUser} />;
    }

    if (currentCanvas === 'earn') {
      return <EarnCanvas currentUser={currentUser} />;
    }

    if (currentCanvas === 'revenue') {
      return <RevenuePage />;
    }

    // Force full-page mode for canvases that should not use sections
    // Instead of showing a placeholder, directly switch to full-page mode
    if (shouldUseFullPage) {
      // Automatically switch to full-page mode without user interaction
      setViewMode('fullpage');
      return null; // This will trigger the Routes rendering below
    }

    return (
      <SectionNavigator
        canvasId={currentCanvas}
        sections={currentCanvasData.sections}
        defaultSection={Object.keys(currentCanvasData.sections)[0]}
      />
    );
  };

  return (
    <motion.div
      key={currentCanvas}
      className={`w-full h-full bg-gradient-to-br ${getCanvasBackground(currentCanvas)} transition-all duration-500`}
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.3 }}
    >
      {/* View Mode Toggle - REMOVED per user request */}

      <Suspense fallback={<CanvasLoading canvas={currentCanvas} />}>
        {viewMode === 'sections' && hasSections && !shouldBypassSections && !shouldUseFullPage ? (
          renderSectionContent()
        ) : (
          <div className="canvas-content h-full w-full overflow-y-auto">
            <Routes>
          {/* Authentication Routes */}
          <Route path="/" element={currentUser ? <ModernDashboard /> : <SimpleLogin />} />
          <Route path="/login" element={currentUser ? <Navigate to="/" /> : <SimpleLogin />} />
          <Route path="/auth/callback" element={<AuthCallback />} />
          <Route path="/password-reset" element={<PasswordResetPage />} />
          <Route path="/reset-password" element={<UpdatePasswordPage />} />

          {/* Core Navigation Routes */}
          <Route path="/start" element={currentUser ? <StartPage /> : <Navigate to="/login" />} />
          <Route path="/track" element={currentUser ? <TrackPage /> : <Navigate to="/login" />} />
          <Route path="/earn" element={currentUser ? <EarnCanvas currentUser={currentUser} /> : <Navigate to="/login" />} />
          <Route path="/learn" element={currentUser ? <LearnPage /> : <Navigate to="/login" />} />
          <Route path="/social" element={currentUser ? <SocialPage /> : <Navigate to="/login" />} />
          <Route path="/help" element={<HelpPage />} />
          <Route path="/escrow" element={currentUser ? <EscrowPage /> : <Navigate to="/login" />} />

          {/* User & Profile Routes */}
          <Route path="/profile" element={<ProfilePage />} />
          <Route path="/profile/:userId" element={<PublicProfile />} />
          <Route path="/retro-profile" element={<RetroProfilePage />} />
          <Route path="/retro-profile/:userId" element={<RetroProfilePage />} />
          <Route path="/settings" element={currentUser ? <SettingsPage /> : <Navigate to="/login" />} />
          <Route path="/notifications" element={currentUser ? <NotificationsPage /> : <Navigate to="/login" />} />
          <Route path="/invitations" element={currentUser ? <InvitationsPage /> : <Navigate to="/login" />} />

          {/* Studio Routes (enhanced team system with business compliance) */}
          <Route path="/studios" element={currentUser ? <StudioList /> : <Navigate to="/login" />} />
          <Route path="/studios/create" element={currentUser ? <StudioCreationWizard /> : <Navigate to="/login" />} />
          <Route path="/studios/:id" element={currentUser ? <StudioDashboard /> : <Navigate to="/login" />} />
          <Route path="/studios/:id/manage" element={currentUser ? <StudioManage /> : <Navigate to="/login" />} />
          <Route path="/studios/:id/invitations" element={currentUser ? <StudioInvitations /> : <Navigate to="/login" />} />

          {/* Team Routes (legacy support) */}
          <Route path="/teams" element={currentUser ? <MyTeams /> : <Navigate to="/login" />} />
          <Route path="/teams/:teamId" element={currentUser ? <TeamDetail /> : <Navigate to="/login" />} />
          <Route path="/teams/:teamId/manage" element={currentUser ? <TeamManage /> : <Navigate to="/login" />} />
          <Route path="/teams/invitations" element={currentUser ? <TeamInvitations /> : <Navigate to="/login" />} />

          {/* Backward compatibility redirects */}
          <Route path="/studios" element={<Navigate to="/studios" replace />} />
          <Route path="/studios/create" element={<Navigate to="/studios/create" replace />} />
          <Route path="/studios/:allianceId" element={<Navigate to="/studios/:allianceId" replace />} />
          <Route path="/studios/:teamId/manage" element={<Navigate to="/studios/:teamId/manage" replace />} />
          <Route path="/studios/invitations" element={<Navigate to="/studios/invitations" replace />} />

          {/* Company Management Routes */}
          <Route path="/companies/:id/manage" element={currentUser ? <CompanyManagePage /> : <Navigate to="/login" />} />

          {/* Project Routes */}
          <Route path="/projects" element={<AuthRoute><ProjectsList /></AuthRoute>} />
          <Route path="/project/create" element={<AuthRoute><ProjectWizard /></AuthRoute>} />
          <Route path="/project/wizard" element={<AuthRoute><ProjectWizard /></AuthRoute>} />
          <Route path="/project/wizard/:id" element={<AuthRoute><ProjectWizard /></AuthRoute>} />
          <Route path="/project/:id" element={<AuthRoute><ProjectDetail /></AuthRoute>} />
          <Route path="/project/:id/edit" element={<AuthRoute><ProjectWizard /></AuthRoute>} />

          {/* Kanban Routes */}
          <Route path="/project/:id/tasks" element={currentUser ? <KanbanPage /> : <Navigate to="/login" />} />

          {/* Mission Board Routes (New Terminology) */}
          <Route path="/missions" element={currentUser ? <MissionBoard /> : <Navigate to="/login" />} />
          <Route path="/missions/create" element={currentUser ? <MissionCreator /> : <Navigate to="/login" />} />
          <Route path="/missions/:id" element={currentUser ? <MissionDetail /> : <Navigate to="/login" />} />

          {/* Backward compatibility for mission routes */}
          <Route path="/missions" element={<Navigate to="/missions" replace />} />
          <Route path="/missions/:id" element={<Navigate to="/missions/:id" replace />} />
          <Route path="/bounties" element={<Navigate to="/missions" replace />} />

          {/* Skill Verification Routes */}
          <Route path="/vetting" element={currentUser ? <SkillVerificationDashboard /> : <Navigate to="/login" />} />

          {/* Project Management Routes */}
          <Route path="/projects" element={currentUser ? <ProjectPage /> : <Navigate to="/login" />} />

          {/* Contribution Routes - Handled by canvas system */}
          <Route path="/contributions" element={currentUser ? <ContributionsPage /> : <Navigate to="/login" />} />
          <Route path="/project/:projectId/contributions" element={currentUser ? <ContributionTrackerPage /> : <Navigate to="/login" />} />
          <Route path="/contribution/:id/validate" element={currentUser ? <ContributionValidatePage /> : <Navigate to="/login" />} />

          {/* Validation Routes */}
          <Route path="/validation/metrics" element={currentUser ? <ValidationMetricsPage /> : <Navigate to="/login" />} />
          <Route path="/validation/metrics/:projectId" element={currentUser ? <ValidationMetricsPage /> : <Navigate to="/login" />} />

          {/* Analytics Routes */}
          <Route path="/analytics" element={currentUser ? <AnalyticsPage /> : <Navigate to="/login" />} />
          <Route path="/analytics/dashboard" element={currentUser ? <EnhancedAnalyticsDashboard /> : <Navigate to="/login" />} />
          <Route path="/analytics/contributions" element={currentUser ? <ContributionAnalyticsPage /> : <Navigate to="/login" />} />
          <Route path="/analytics/contributions/:projectId" element={currentUser ? <ContributionAnalyticsPage /> : <Navigate to="/login" />} />
          <Route path="/analytics/insights" element={currentUser ? <InsightsPage /> : <Navigate to="/login" />} />

          {/* Revenue Routes */}
          <Route path="/revenue" element={currentUser ? <RevenuePage /> : <Navigate to="/login" />} />
          <Route path="/revenue/dashboard" element={currentUser ? <EnhancedRevenuePage /> : <Navigate to="/login" />} />
          <Route path="/project/:projectId/revenue" element={currentUser ? <RevenuePage /> : <Navigate to="/login" />} />
          <Route path="/project/:projectId/royalty-calculator" element={currentUser ? <RoyaltyCalculatorPage /> : <Navigate to="/login" />} />
          <Route path="/project/:projectId/royalty-calculator/:revenueId" element={currentUser ? <RoyaltyCalculatorPage /> : <Navigate to="/login" />} />

          {/* Agreement Routes */}
          <Route path="/project/:id/agreements" element={currentUser ? <ProjectAgreements /> : <Navigate to="/login" />} />

          {/* Admin Routes */}
          <Route path="/admin" element={<AdminDashboard />} />
          <Route path="/admin/system" element={<SystemPage />} />
          <Route path="/admin/roadmap" element={<Navigate to="/admin" />} />
          <Route path="/admin/bugs" element={<Navigate to="/admin" />} />
          <Route path="/admin/roadmap-manager" element={<RoadmapManagerPage />} />
          <Route path="/admin/profile-migration" element={<ProfileMigration />} />
          <Route path="/admin/components" element={<ComponentPlayground />} />

          {/* Bug & Debug Routes */}
          <Route path="/bugs" element={<BugReportPage />} />
          <Route path="/debug/auth" element={<AuthDebugPage />} />

          {/* Test Routes */}
          <Route path="/ui-test" element={<TestUI />} />
          <Route path="/button-test" element={<ButtonTest />} />
          <Route path="/test/ui-polish" element={<UIPolishTestSuite />} />
          <Route path="/test/navigation-enhancement" element={<NavigationEnhancementTest />} />
          <Route path="/test/environment" element={<EnvironmentTestPage />} />
          <Route path="/test/mission-board-integration" element={<MissionBoardIntegrationTest />} />
          <Route path="/test/track-page-integration" element={<TrackPageIntegrationTest />} />
          <Route path="/test/quick-route-integration" element={<QuickRouteIntegrationTest />} />
          <Route path="/test/project-page-integration" element={<ProjectPageIntegrationTest />} />

          {/* Catch-all Route */}
          <Route path="*" element={<NotFound />} />
            </Routes>
          </div>
        )}
      </Suspense>
    </motion.div>
  );
};

export default ContentRenderer;

/**
 * Enhanced Project Creation Component
 *
 * Comprehensive project creation flow with automatic agreement generation:
 * - Project specification and requirements
 * - Contributor role definition
 * - Revenue sharing configuration
 * - IP rights assignment
 * - Milestone and deliverable planning
 * - Automatic agreement template selection and generation
 */

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Rocket,
  Users,
  Target,
  DollarSign,
  Shield,
  Calendar,
  FileText,
  CheckCircle,
  AlertCircle,
  Info,
  Plus,
  Trash2
} from 'lucide-react';

const VENTURE_STEPS = [
  { id: 'project', title: 'Project Details', icon: Rocket },
  { id: 'contributors', title: 'Contributors & Roles', icon: Users },
  { id: 'milestones', title: 'Milestones & Timeline', icon: Target },
  { id: 'revenue', title: 'Revenue Sharing', icon: DollarSign },
  { id: 'ip', title: 'IP Rights', icon: Shield },
  { id: 'agreement', title: 'Agreement Generation', icon: FileText },
  { id: 'review', title: 'Review & Launch', icon: CheckCircle }
];

export default function EnhancedVentureCreation() {
  const { allianceId } = useParams();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [studio, setAlliance] = useState(null);
  const [ventureData, setVentureData] = useState({
    // Project Details
    name: '',
    description: '',
    objectives: '',
    scope: '',
    deliverables: [],

    // Contributors & Roles
    contributors: [],
    roleDefinitions: [],

    // Milestones & Timeline
    milestones: [],
    startDate: '',
    endDate: '',

    // Revenue Sharing
    revenueModel: '',
    revenueShares: {},
    performanceMetrics: [],

    // IP Rights
    ipStrategy: '',
    ipOwnership: {},

    // Agreement
    agreementTemplate: '',
    customClauses: [],

    // Configuration
    isPublic: false,
    requiresApproval: true
  });

  const [validationErrors, setValidationErrors] = useState({});
  const [isCreating, setIsCreating] = useState(false);
  const [generatedAgreement, setGeneratedAgreement] = useState(null);

  useEffect(() => {
    fetchAlliance();
  }, [allianceId]);

  const fetchAlliance = async () => {
    try {
      const response = await fetch(`/api/studios/${allianceId}`);
      if (response.ok) {
        const allianceData = await response.json();
        setAlliance(allianceData);

        // Pre-populate project data with studio defaults
        setVentureData(prev => ({
          ...prev,
          revenueModel: allianceData.revenueModel,
          ipStrategy: allianceData.ipOwnershipModel
        }));
      }
    } catch (error) {
      console.error('Error fetching studio:', error);
    }
  };

  const currentStepData = VENTURE_STEPS[currentStep];
  const progress = ((currentStep + 1) / VENTURE_STEPS.length) * 100;

  const updateVentureData = (field, value) => {
    setVentureData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const addDeliverable = () => {
    const newDeliverable = {
      id: Date.now(),
      title: '',
      description: '',
      type: 'document',
      dueDate: ''
    };

    setVentureData(prev => ({
      ...prev,
      deliverables: [...prev.deliverables, newDeliverable]
    }));
  };

  const updateDeliverable = (id, field, value) => {
    setVentureData(prev => ({
      ...prev,
      deliverables: prev.deliverables.map(deliverable =>
        deliverable.id === id ? { ...deliverable, [field]: value } : deliverable
      )
    }));
  };

  const removeDeliverable = (id) => {
    setVentureData(prev => ({
      ...prev,
      deliverables: prev.deliverables.filter(deliverable => deliverable.id !== id)
    }));
  };

  const addContributor = () => {
    const newContributor = {
      id: Date.now(),
      email: '',
      role: '',
      responsibilities: '',
      revenueShare: 0,
      ipRights: 'contributor'
    };

    setVentureData(prev => ({
      ...prev,
      contributors: [...prev.contributors, newContributor]
    }));
  };

  const updateContributor = (id, field, value) => {
    setVentureData(prev => ({
      ...prev,
      contributors: prev.contributors.map(contributor =>
        contributor.id === id ? { ...contributor, [field]: value } : contributor
      )
    }));
  };

  const removeContributor = (id) => {
    setVentureData(prev => ({
      ...prev,
      contributors: prev.contributors.filter(contributor => contributor.id !== id)
    }));
  };

  const addMilestone = () => {
    const newMilestone = {
      id: Date.now(),
      title: '',
      description: '',
      dueDate: '',
      deliverables: [],
      paymentPercentage: 0
    };

    setVentureData(prev => ({
      ...prev,
      milestones: [...prev.milestones, newMilestone]
    }));
  };

  const updateMilestone = (id, field, value) => {
    setVentureData(prev => ({
      ...prev,
      milestones: prev.milestones.map(milestone =>
        milestone.id === id ? { ...milestone, [field]: value } : milestone
      )
    }));
  };

  const removeMilestone = (id) => {
    setVentureData(prev => ({
      ...prev,
      milestones: prev.milestones.filter(milestone => milestone.id !== id)
    }));
  };

  const validateCurrentStep = () => {
    const errors = {};

    switch (currentStepData.id) {
      case 'project':
        if (!ventureData.name.trim()) errors.name = 'Project name is required';
        if (!ventureData.description.trim()) errors.description = 'Description is required';
        if (!ventureData.objectives.trim()) errors.objectives = 'Objectives are required';
        break;

      case 'contributors':
        if (ventureData.contributors.length === 0) {
          errors.contributors = 'At least one contributor is required';
        }
        break;

      case 'milestones':
        if (ventureData.milestones.length === 0) {
          errors.milestones = 'At least one milestone is required';
        }
        if (!ventureData.startDate) errors.startDate = 'Start date is required';
        break;

      case 'revenue':
        const totalShare = ventureData.contributors.reduce((sum, c) => sum + (c.revenueShare || 0), 0);
        if (Math.abs(totalShare - 100) > 0.01) {
          errors.revenueShares = 'Revenue shares must total 100%';
        }
        break;
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      if (currentStepData.id === 'ip') {
        generateAgreement();
      }
      setCurrentStep(prev => Math.min(prev + 1, VENTURE_STEPS.length - 1));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
  };

  const generateAgreement = async () => {
    try {
      // Generate agreement based on project configuration
      const agreementData = {
        allianceId,
        ventureData,
        templateType: studio?.collaborationType,
        industry: studio?.industry
      };

      const response = await fetch('/api/agreements/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(agreementData),
      });

      if (response.ok) {
        const agreement = await response.json();
        setGeneratedAgreement(agreement);
      }
    } catch (error) {
      console.error('Error generating agreement:', error);
    }
  };

  const handleCreateVenture = async () => {
    if (!validateCurrentStep()) return;

    setIsCreating(true);
    try {
      const response = await fetch(`/api/studios/${allianceId}/projects`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...ventureData,
          agreementId: generatedAgreement?.id,
          createdAt: new Date().toISOString()
        }),
      });

      if (response.ok) {
        const project = await response.json();
        navigate(`/studios/${allianceId}/projects/${project.id}`);
      } else {
        throw new Error('Failed to create project');
      }
    } catch (error) {
      console.error('Error creating project:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStepData.id) {
      case 'project':
        return <ProjectDetailsStep />;
      case 'contributors':
        return <ContributorsStep />;
      case 'milestones':
        return <MilestonesStep />;
      case 'revenue':
        return <RevenueStep />;
      case 'ip':
        return <IPRightsStep />;
      case 'agreement':
        return <AgreementStep />;
      case 'review':
        return <ReviewStep />;
      default:
        return null;
    }
  };

  const ProjectDetailsStep = () => (
    <div className="space-y-6">
      <div>
        <Label htmlFor="name">Project Name *</Label>
        <Input
          id="name"
          value={ventureData.name}
          onChange={(e) => updateVentureData('name', e.target.value)}
          placeholder="Enter project name"
          className={validationErrors.name ? 'border-red-500' : ''}
        />
        {validationErrors.name && (
          <p className="text-sm text-red-500 mt-1">{validationErrors.name}</p>
        )}
      </div>

      <div>
        <Label htmlFor="description">Description *</Label>
        <Textarea
          id="description"
          value={ventureData.description}
          onChange={(e) => updateVentureData('description', e.target.value)}
          placeholder="Describe what this project will accomplish"
          rows={4}
          className={validationErrors.description ? 'border-red-500' : ''}
        />
        {validationErrors.description && (
          <p className="text-sm text-red-500 mt-1">{validationErrors.description}</p>
        )}
      </div>

      <div>
        <Label htmlFor="objectives">Objectives & Goals *</Label>
        <Textarea
          id="objectives"
          value={ventureData.objectives}
          onChange={(e) => updateVentureData('objectives', e.target.value)}
          placeholder="What are the specific objectives and success criteria?"
          rows={3}
          className={validationErrors.objectives ? 'border-red-500' : ''}
        />
        {validationErrors.objectives && (
          <p className="text-sm text-red-500 mt-1">{validationErrors.objectives}</p>
        )}
      </div>

      <div>
        <Label htmlFor="scope">Project Scope</Label>
        <Textarea
          id="scope"
          value={ventureData.scope}
          onChange={(e) => updateVentureData('scope', e.target.value)}
          placeholder="Define what is included and excluded from this project"
          rows={3}
        />
      </div>

      <div>
        <div className="flex justify-between items-center mb-4">
          <Label>Deliverables</Label>
          <Button type="button" onClick={addDeliverable} size="sm">
            <Plus className="w-4 h-4 mr-2" />
            Add Deliverable
          </Button>
        </div>

        {ventureData.deliverables.map((deliverable) => (
          <Card key={deliverable.id} className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label>Title</Label>
                <Input
                  value={deliverable.title}
                  onChange={(e) => updateDeliverable(deliverable.id, 'title', e.target.value)}
                  placeholder="Deliverable title"
                />
              </div>
              <div>
                <Label>Type</Label>
                <Select
                  value={deliverable.type}
                  onValueChange={(value) => updateDeliverable(deliverable.id, 'type', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="document">Document</SelectItem>
                    <SelectItem value="software">Software</SelectItem>
                    <SelectItem value="design">Design</SelectItem>
                    <SelectItem value="content">Content</SelectItem>
                    <SelectItem value="service">Service</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-end space-x-2">
                <div className="flex-1">
                  <Label>Due Date</Label>
                  <Input
                    type="date"
                    value={deliverable.dueDate}
                    onChange={(e) => updateDeliverable(deliverable.id, 'dueDate', e.target.value)}
                  />
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeDeliverable(deliverable.id)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <div className="mt-4">
              <Label>Description</Label>
              <Textarea
                value={deliverable.description}
                onChange={(e) => updateDeliverable(deliverable.id, 'description', e.target.value)}
                placeholder="Describe this deliverable"
                rows={2}
              />
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const ContributorsStep = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">Contributors & Roles</h3>
          <p className="text-sm text-gray-500">Define who will work on this project and their responsibilities</p>
        </div>
        <Button type="button" onClick={addContributor}>
          <Plus className="w-4 h-4 mr-2" />
          Add Contributor
        </Button>
      </div>

      {validationErrors.contributors && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{validationErrors.contributors}</AlertDescription>
        </Alert>
      )}

      {ventureData.contributors.map((contributor) => (
        <Card key={contributor.id} className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label>Email Address</Label>
              <Input
                type="email"
                value={contributor.email}
                onChange={(e) => updateContributor(contributor.id, 'email', e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <Label>Role/Title</Label>
              <Input
                value={contributor.role}
                onChange={(e) => updateContributor(contributor.id, 'role', e.target.value)}
                placeholder="e.g., Developer, Designer, Manager"
              />
            </div>
          </div>

          <div className="mt-4">
            <Label>Responsibilities</Label>
            <Textarea
              value={contributor.responsibilities}
              onChange={(e) => updateContributor(contributor.id, 'responsibilities', e.target.value)}
              placeholder="Describe their specific responsibilities and tasks"
              rows={3}
            />
          </div>

          <div className="mt-4 flex justify-between items-center">
            <div className="grid grid-cols-2 gap-4 flex-1">
              <div>
                <Label>Revenue Share (%)</Label>
                <Input
                  type="number"
                  value={contributor.revenueShare}
                  onChange={(e) => updateContributor(contributor.id, 'revenueShare', parseFloat(e.target.value) || 0)}
                  placeholder="0"
                  min="0"
                  max="100"
                  step="0.1"
                />
              </div>
              <div>
                <Label>IP Rights</Label>
                <Select
                  value={contributor.ipRights}
                  onValueChange={(value) => updateContributor(contributor.id, 'ipRights', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select IP rights" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="contributor">Contributor Rights</SelectItem>
                    <SelectItem value="co_owner">Co-Owner</SelectItem>
                    <SelectItem value="work_for_hire">Work for Hire</SelectItem>
                    <SelectItem value="retained">Retained Rights</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => removeContributor(contributor.id)}
              className="ml-4"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </Card>
      ))}

      {ventureData.contributors.length > 0 && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Total Revenue Share: {ventureData.contributors.reduce((sum, c) => sum + (c.revenueShare || 0), 0).toFixed(1)}%
            {Math.abs(ventureData.contributors.reduce((sum, c) => sum + (c.revenueShare || 0), 0) - 100) > 0.01 && (
              <span className="text-red-500 ml-2">
                (Must total 100%)
              </span>
            )}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );

  const MilestonesStep = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="startDate">Start Date *</Label>
          <Input
            id="startDate"
            type="date"
            value={ventureData.startDate}
            onChange={(e) => updateVentureData('startDate', e.target.value)}
            className={validationErrors.startDate ? 'border-red-500' : ''}
          />
          {validationErrors.startDate && (
            <p className="text-sm text-red-500 mt-1">{validationErrors.startDate}</p>
          )}
        </div>
        <div>
          <Label htmlFor="endDate">End Date</Label>
          <Input
            id="endDate"
            type="date"
            value={ventureData.endDate}
            onChange={(e) => updateVentureData('endDate', e.target.value)}
          />
        </div>
      </div>

      <div>
        <div className="flex justify-between items-center mb-4">
          <Label>Milestones</Label>
          <Button type="button" onClick={addMilestone}>
            <Plus className="w-4 h-4 mr-2" />
            Add Milestone
          </Button>
        </div>

        {validationErrors.milestones && (
          <Alert className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{validationErrors.milestones}</AlertDescription>
          </Alert>
        )}

        {ventureData.milestones.map((milestone) => (
          <Card key={milestone.id} className="p-4 mb-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label>Milestone Title</Label>
                <Input
                  value={milestone.title}
                  onChange={(e) => updateMilestone(milestone.id, 'title', e.target.value)}
                  placeholder="Milestone name"
                />
              </div>
              <div>
                <Label>Due Date</Label>
                <Input
                  type="date"
                  value={milestone.dueDate}
                  onChange={(e) => updateMilestone(milestone.id, 'dueDate', e.target.value)}
                />
              </div>
              <div className="flex items-end space-x-2">
                <div className="flex-1">
                  <Label>Payment %</Label>
                  <Input
                    type="number"
                    value={milestone.paymentPercentage}
                    onChange={(e) => updateMilestone(milestone.id, 'paymentPercentage', parseFloat(e.target.value) || 0)}
                    placeholder="0"
                    min="0"
                    max="100"
                    step="0.1"
                  />
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeMilestone(milestone.id)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <div className="mt-4">
              <Label>Description</Label>
              <Textarea
                value={milestone.description}
                onChange={(e) => updateMilestone(milestone.id, 'description', e.target.value)}
                placeholder="Describe what needs to be accomplished for this milestone"
                rows={2}
              />
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const RevenueStep = () => (
    <div className="space-y-6">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          Revenue model inherited from studio: <strong>{studio?.revenueModel}</strong>
        </AlertDescription>
      </Alert>

      {validationErrors.revenueShares && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{validationErrors.revenueShares}</AlertDescription>
        </Alert>
      )}

      <div>
        <h3 className="text-lg font-medium mb-4">Revenue Distribution</h3>
        <div className="space-y-4">
          {ventureData.contributors.map((contributor) => (
            <div key={contributor.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <div className="font-medium">{contributor.email}</div>
                <div className="text-sm text-gray-500">{contributor.role}</div>
              </div>
              <div className="flex items-center space-x-2">
                <Input
                  type="number"
                  value={contributor.revenueShare}
                  onChange={(e) => updateContributor(contributor.id, 'revenueShare', parseFloat(e.target.value) || 0)}
                  className="w-20"
                  min="0"
                  max="100"
                  step="0.1"
                />
                <span className="text-sm">%</span>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <div className="flex justify-between items-center">
            <span className="font-medium">Total:</span>
            <span className={`font-medium ${
              Math.abs(ventureData.contributors.reduce((sum, c) => sum + (c.revenueShare || 0), 0) - 100) > 0.01
                ? 'text-red-500'
                : 'text-green-500'
            }`}>
              {ventureData.contributors.reduce((sum, c) => sum + (c.revenueShare || 0), 0).toFixed(1)}%
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  const IPRightsStep = () => (
    <div className="space-y-6">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          IP ownership model inherited from studio: <strong>{studio?.ipOwnershipModel?.replace(/_/g, ' ')}</strong>
        </AlertDescription>
      </Alert>

      <div>
        <h3 className="text-lg font-medium mb-4">IP Rights Assignment</h3>
        <div className="space-y-4">
          {ventureData.contributors.map((contributor) => (
            <div key={contributor.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <div className="font-medium">{contributor.email}</div>
                <div className="text-sm text-gray-500">{contributor.role}</div>
              </div>
              <div>
                <Select
                  value={contributor.ipRights}
                  onValueChange={(value) => updateContributor(contributor.id, 'ipRights', value)}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Select IP rights" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="contributor">Contributor Rights</SelectItem>
                    <SelectItem value="co_owner">Co-Owner</SelectItem>
                    <SelectItem value="work_for_hire">Work for Hire</SelectItem>
                    <SelectItem value="retained">Retained Rights</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const AgreementStep = () => (
    <div className="space-y-6">
      {generatedAgreement ? (
        <div>
          <Alert className="mb-4">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Agreement successfully generated based on your project configuration.
            </AlertDescription>
          </Alert>

          <Card>
            <CardHeader>
              <CardTitle>Generated Agreement</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <strong>Template:</strong> {generatedAgreement.templateName}
                </div>
                <div>
                  <strong>Industry:</strong> {generatedAgreement.industry}
                </div>
                <div>
                  <strong>Collaboration Type:</strong> {generatedAgreement.collaborationType}
                </div>
                <div className="mt-4">
                  <Button variant="outline" onClick={() => window.open(`/agreements/${generatedAgreement.id}/preview`, '_blank')}>
                    <FileText className="w-4 h-4 mr-2" />
                    Preview Agreement
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Generating agreement...</p>
        </div>
      )}
    </div>
  );

  const ReviewStep = () => (
    <div className="space-y-6">
      <Alert>
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          Review your project configuration below. Once created, contributors will be invited to sign the agreement.
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Project Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><strong>Name:</strong> {ventureData.name}</div>
            <div><strong>Timeline:</strong> {ventureData.startDate} to {ventureData.endDate || 'Ongoing'}</div>
            <div><strong>Deliverables:</strong> {ventureData.deliverables.length}</div>
            <div><strong>Milestones:</strong> {ventureData.milestones.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Contributors</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {ventureData.contributors.map((contributor) => (
              <div key={contributor.id} className="flex justify-between">
                <span>{contributor.email}</span>
                <span>{contributor.revenueShare}%</span>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  if (!studio) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading studio information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Create New Project</h1>
        <p className="text-gray-600">
          Set up a new project in <strong>{studio.name}</strong> with automatic agreement generation
        </p>
      </div>

      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium">Step {currentStep + 1} of {VENTURE_STEPS.length}</span>
          <span className="text-sm text-gray-500">{Math.round(progress)}% Complete</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      {/* Step Navigation */}
      <div className="mb-8">
        <div className="flex justify-between">
          {VENTURE_STEPS.map((step, index) => {
            const Icon = step.icon;
            const isActive = index === currentStep;
            const isCompleted = index < currentStep;

            return (
              <div
                key={step.id}
                className={`flex flex-col items-center space-y-2 ${
                  isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-400'
                }`}
              >
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center border-2 ${
                    isActive
                      ? 'border-blue-600 bg-blue-50'
                      : isCompleted
                      ? 'border-green-600 bg-green-50'
                      : 'border-gray-300 bg-gray-50'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                </div>
                <span className="text-xs font-medium text-center">{step.title}</span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Content */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <currentStepData.icon className="w-5 h-5" />
            <span>{currentStepData.title}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {renderStepContent()}
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 0}
        >
          Previous
        </Button>

        {currentStep === VENTURE_STEPS.length - 1 ? (
          <Button
            onClick={handleCreateVenture}
            disabled={isCreating}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isCreating ? 'Creating Project...' : 'Create Project'}
          </Button>
        ) : (
          <Button
            onClick={handleNext}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Next
          </Button>
        )}
      </div>
    </div>
  );
}
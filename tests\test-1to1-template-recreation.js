/**
 * 1:1 Template Recreation Test
 * 
 * This test specifically validates the platform's ability to recreate
 * the exact lawyer-approved City of Gamers Inc. Contributor Agreement
 * with 99% minimum accuracy when provided with the exact VOTA project data.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Simplified Agreement Generator for Testing
 * This bypasses browser dependencies and focuses on core template processing
 */
class SimpleAgreementGenerator {
  constructor() {
    const today = new Date();
    this.currentDate = today.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  }

  /**
   * Generate agreement using simple template processing
   */
  async generateAgreement(templateText, project, options = {}) {
    if (!templateText || !project) {
      throw new Error('Template text and project data are required');
    }

    const {
      contributors = [],
      currentUser = null,
      fullName = '',
      agreementDate = null
    } = options;

    // Set the agreement date if provided
    if (agreementDate) {
      const date = new Date(agreementDate);
      this.currentDate = date.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });
    }

    // Process the template with basic replacements
    let processedAgreement = templateText;

    // Replace date placeholders
    processedAgreement = processedAgreement.replace(/\[ \], 20\[__\]/g, this.currentDate);
    processedAgreement = processedAgreement.replace(/\[Date\]/g, this.currentDate);

    // Replace contributor information
    const contributorName = fullName || '[_________________________]';
    processedAgreement = processedAgreement.replace(/\[_+\]/g, contributorName);

    // Replace project information using simple variable substitution
    processedAgreement = processedAgreement.replace(/\[Project Name\]/g, project.name || '[Project Name]');
    processedAgreement = processedAgreement.replace(/\[Project Description\]/g, project.description || '[Project Description]');
    processedAgreement = processedAgreement.replace(/\[Project Type\]/g, project.project_type || '[Project Type]');
    processedAgreement = processedAgreement.replace(/\[Project Features\]/g, project.features || '[Project Features]');
    processedAgreement = processedAgreement.replace(/\[Project Core Features\]/g, project.coreFeatures || '[Project Core Features]');
    processedAgreement = processedAgreement.replace(/\[Project Technical Requirements\]/g, project.technicalRequirements || '[Project Technical Requirements]');
    processedAgreement = processedAgreement.replace(/\[Project Roadmap Phases\]/g, project.roadmapPhases || '[Project Roadmap Phases]');

    // Format and replace milestones
    if (project.milestones && project.milestones.length > 0) {
      const formattedMilestones = project.milestones.map((milestone, index) => {
        const title = milestone.title || milestone.name || `Milestone ${index + 1}`;
        const description = milestone.description || '';
        const dueDate = milestone.dueDate || milestone.due_date || '';

        let formatted = `${index + 1}. **${title}**`;
        if (dueDate) formatted += ` (${dueDate})`;
        if (description) formatted += `\n   - ${description}`;

        return formatted;
      }).join('\n\n');

      processedAgreement = processedAgreement.replace(/\[Project Milestones\]/g, formattedMilestones);
    } else {
      processedAgreement = processedAgreement.replace(/\[Project Milestones\]/g, '[Project Milestones]');
    }

    // Replace company information
    if (project.company_name) {
      // Only replace if the company name is different from the default
      if (project.company_name !== 'City of Gamers Inc.') {
        processedAgreement = processedAgreement.replace(/City of Gamers Inc\./g, project.company_name);
      }
    }

    if (project.address) {
      processedAgreement = processedAgreement.replace(/1205 43rd Street, Suite B, Orlando, Florida 32839/g, project.address);
    }

    if (project.contact_email) {
      processedAgreement = processedAgreement.replace(/billing@cogfuture\.com/g, project.contact_email);
    }

    // Clean up any remaining placeholders
    processedAgreement = processedAgreement.replace(/\[.*?\]/g, '');

    return processedAgreement;
  }
}

console.log('🎯 1:1 TEMPLATE RECREATION TEST');
console.log('=' .repeat(50));
console.log('Testing exact recreation of lawyer-approved template');
console.log('Target: 99% minimum accuracy');
console.log('=' .repeat(50));

/**
 * Exact VOTA data extracted from lawyer-approved template
 */
const EXACT_VOTA_DATA = {
  name: 'Village of The Ages',
  description: 'A village simulation game where players guide communities through historical progressions and manage resource-based challenges',
  project_type: 'game',
  company_name: 'City of Gamers Inc.',
  address: '1205 43rd Street, Suite B, Orlando, Florida 32839',
  contact_email: '<EMAIL>',
  city: 'Orlando',
  state: 'Florida',
  zip: '32839',
  county: 'Orange',
  legal_entity_type: 'corporation',
  incorporation_state: 'Florida',
  
  // Exact features from lawyer template
  features: 'The game features dynamic challenges based on resource scarcity and natural disasters, creating an engaging gameplay loop.',
  
  // Exact core features from lawyer template
  coreFeatures: `1. **Village Building & Management**
   - Resource gathering and management
   - Building placement and construction
   - Population growth and management
   - Cultural evolution systems

2. **Historical Progression**
   - Players guide their community through multiple historical eras
   - Technology tree advancement
   - Cultural and social development
   - Architectural evolution

3. **Resource Management**
   - Dynamic weather systems affecting resources
   - Natural disasters and seasonal challenges
   - Trading systems with neighboring villages
   - Resource scarcity mechanics

4. **Interface Requirements**
   - Intuitive building placement system
   - Resource management dashboard
   - Population statistics and management panels
   - Technology and progression trackers`,
   
  // Exact technical requirements from lawyer template
  technicalRequirements: `- Platform: PC (Steam, Epic Games Store)
- Engine: Unreal Engine 5
- Minimum Specs: Standard hardware requirements for the target platforms
- Art Style: Stylized, readable visuals with distinctive aesthetics
- Audio: Atmospheric soundtrack with appropriate sound design
- Version Control: Git-based source control with proper branching strategy`,

  // Exact roadmap phases from lawyer template
  roadmapPhases: `**Phase 1: Core Gameplay Development (Months 1-2)**
- Basic village layout and building system
- Core resource gathering mechanics
- Initial AI for villagers
- Basic UI framework
- First playable prototype with one historical era

**Phase 2: Feature Expansion (Months 2-3)**
- Additional historical eras
- Enhanced resource management systems
- Weather and disaster systems
- Trading mechanics
- Technology progression system

**Phase 3: Polish and Enhancement (Month 4)**
- UI refinement
- Performance optimization
- Additional content (buildings, resources, etc.)
- Balancing and gameplay tuning
- Audio implementation`,

  // Exact milestones from lawyer template
  milestones: [
    {
      title: 'Core Gameplay Development',
      description: 'Basic village layout and building system',
      dueDate: 'Months 1-2'
    },
    {
      title: 'Resource Management System', 
      description: 'Implementation of resource scarcity mechanics',
      dueDate: 'Months 3-4'
    },
    {
      title: 'Historical Progression Features',
      description: 'Time-based progression and historical events, architectural evolution through eras',
      dueDate: 'Months 5-6'
    }
  ]
};

/**
 * Critical sections that must appear exactly in the generated agreement
 */
const CRITICAL_SECTIONS = [
  'CITY OF GAMERS INC.',
  'CONTRIBUTOR AGREEMENT',
  'Village of The Ages',
  'village simulation game',
  'historical progressions',
  'resource-based challenges',
  'Village Building & Management',
  'Historical Progression',
  'Resource Management',
  'Interface Requirements',
  'Technology tree advancement',
  'Architectural evolution',
  'Dynamic weather systems',
  'Natural disasters and seasonal challenges',
  'Trading systems with neighboring villages',
  'Resource scarcity mechanics',
  'Intuitive building placement system',
  'Resource management dashboard',
  'Population statistics and management panels',
  'Technology and progression trackers',
  'Core Gameplay Development',
  'Resource Management System',
  'Historical Progression Features'
];

/**
 * Structural elements that must be present
 */
const STRUCTURAL_ELEMENTS = [
  '## Recitals',
  '## 1. Definitions',
  '## 2. Treatment of Confidential Information',
  '## SCHEDULE A',
  '## EXHIBIT I',
  '## EXHIBIT II',
  'Background IP',
  'Work Product',
  'Confidential Information',
  'Contribution Points',
  'Revenue Tranch'
];

/**
 * Main test function
 */
async function test1to1Recreation() {
  console.log('\n🚀 Starting 1:1 template recreation test...\n');
  
  try {
    // Load the lawyer-approved template for comparison
    const lawyerTemplatePath = path.join(__dirname, '../client/public/example-cog-contributor-agreement.md');
    const lawyerTemplate = fs.readFileSync(lawyerTemplatePath, 'utf8');
    
    // Load the variable-based template
    const templatePath = path.join(__dirname, '../client/public/contributor-agreement-template.md');
    const templateText = fs.readFileSync(templatePath, 'utf8');
    
    // Set up test options to match lawyer template exactly
    const testOptions = {
      contributors: [{
        id: 'test_contributor',
        email: '[_________________________]',
        name: '[_________________________]',
        address: '_____________________'
      }],
      currentUser: {
        id: 'test_contributor',
        email: '[_________________________]',
        user_metadata: {
          full_name: '[_________________________]'
        }
      },
      fullName: '[_________________________]',
      agreementDate: new Date('2024-01-15')
    };
    
    console.log('📋 Generating agreement with exact VOTA data...');

    // Generate the agreement using the simplified system
    const generator = new SimpleAgreementGenerator();
    const generatedAgreement = await generator.generateAgreement(
      templateText,
      EXACT_VOTA_DATA,
      testOptions
    );
    
    // Save the generated agreement
    const outputPath = path.join(__dirname, '1to1-recreation-test-output.md');
    fs.writeFileSync(outputPath, generatedAgreement);
    console.log(`📄 Generated agreement saved to: ${outputPath}`);
    
    // Perform detailed accuracy analysis
    console.log('\n🔍 PERFORMING ACCURACY ANALYSIS');
    console.log('=' .repeat(40));
    
    const accuracyResults = performAccuracyAnalysis(lawyerTemplate, generatedAgreement);
    
    // Display results
    displayResults(accuracyResults, outputPath);
    
    // Save detailed report
    const reportPath = path.join(__dirname, '1to1-recreation-report.json');
    const report = {
      timestamp: new Date().toISOString(),
      testType: '1:1 Template Recreation',
      targetAccuracy: 99,
      actualAccuracy: accuracyResults.overallAccuracy,
      passed: accuracyResults.overallAccuracy >= 99,
      outputFile: outputPath,
      details: accuracyResults,
      exactVOTAData: EXACT_VOTA_DATA
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📊 Detailed report saved to: ${reportPath}`);
    
    return report;
    
  } catch (error) {
    console.error('\n❌ 1:1 Recreation test failed:', error);
    throw error;
  }
}

/**
 * Perform detailed accuracy analysis
 */
function performAccuracyAnalysis(lawyerTemplate, generatedAgreement) {
  const results = {
    criticalSections: { found: [], missing: [], accuracy: 0 },
    structuralElements: { found: [], missing: [], accuracy: 0 },
    contentMatching: { matches: 0, total: 0, accuracy: 0 },
    overallAccuracy: 0
  };
  
  // Check critical sections
  for (const section of CRITICAL_SECTIONS) {
    if (generatedAgreement.includes(section)) {
      results.criticalSections.found.push(section);
    } else {
      results.criticalSections.missing.push(section);
    }
  }
  
  results.criticalSections.accuracy = Math.round(
    (results.criticalSections.found.length / CRITICAL_SECTIONS.length) * 100
  );
  
  // Check structural elements
  for (const element of STRUCTURAL_ELEMENTS) {
    if (generatedAgreement.includes(element)) {
      results.structuralElements.found.push(element);
    } else {
      results.structuralElements.missing.push(element);
    }
  }
  
  results.structuralElements.accuracy = Math.round(
    (results.structuralElements.found.length / STRUCTURAL_ELEMENTS.length) * 100
  );
  
  // Calculate overall accuracy
  const totalElements = CRITICAL_SECTIONS.length + STRUCTURAL_ELEMENTS.length;
  const totalFound = results.criticalSections.found.length + results.structuralElements.found.length;
  
  results.overallAccuracy = Math.round((totalFound / totalElements) * 100);
  
  return results;
}

/**
 * Display test results
 */
function displayResults(results, outputPath) {
  console.log(`\n📊 CRITICAL SECTIONS: ${results.criticalSections.accuracy}%`);
  console.log(`   Found: ${results.criticalSections.found.length}/${CRITICAL_SECTIONS.length}`);
  
  if (results.criticalSections.missing.length > 0) {
    console.log('   Missing:');
    results.criticalSections.missing.slice(0, 5).forEach(section => {
      console.log(`   • "${section}"`);
    });
    if (results.criticalSections.missing.length > 5) {
      console.log(`   • ... and ${results.criticalSections.missing.length - 5} more`);
    }
  }
  
  console.log(`\n📊 STRUCTURAL ELEMENTS: ${results.structuralElements.accuracy}%`);
  console.log(`   Found: ${results.structuralElements.found.length}/${STRUCTURAL_ELEMENTS.length}`);
  
  if (results.structuralElements.missing.length > 0) {
    console.log('   Missing:');
    results.structuralElements.missing.forEach(element => {
      console.log(`   • "${element}"`);
    });
  }
  
  console.log(`\n🎯 OVERALL ACCURACY: ${results.overallAccuracy}%`);
  console.log(`🎯 REQUIRED ACCURACY: 99%`);
  console.log(`${results.overallAccuracy >= 99 ? '✅' : '❌'} Test ${results.overallAccuracy >= 99 ? 'PASSED' : 'FAILED'}`);
  
  if (results.overallAccuracy >= 99) {
    console.log('\n🎉 EXCELLENT: Platform can recreate lawyer-approved template!');
    console.log('✅ System meets 1:1 recreation requirements');
    console.log('✅ Ready for production use');
  } else {
    console.log(`\n🔧 IMPROVEMENT NEEDED: ${99 - results.overallAccuracy}% accuracy gap`);
    console.log('❌ System does not meet 1:1 recreation requirements');
    console.log('⚠️  Not ready for production use');
  }
}

// Run the test if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  test1to1Recreation()
    .then(report => {
      console.log(`\n✅ Test completed with ${report.actualAccuracy}% accuracy`);
      process.exit(report.passed ? 0 : 1);
    })
    .catch(error => {
      console.error('\n❌ Test failed:', error.message);
      process.exit(1);
    });
}

export { test1to1Recreation, EXACT_VOTA_DATA, CRITICAL_SECTIONS };

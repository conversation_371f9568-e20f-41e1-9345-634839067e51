/**
 * Generate Sample Agreement Script
 * 
 * This script generates sample agreements to demonstrate the alliance-venture separation
 */

import { NewAgreementGenerator } from '../../../client/src/utils/agreement/newAgreementGenerator.js';
import fs from 'fs';
import path from 'path';

// Mock template
const mockTemplate = `
# CITY OF GAMERS INC.
# CONTRIBUTOR AGREEMENT

This Contributor Agreement (this "Agreement") is effective as of [Date], by and between City of Gamers Inc., a Florida LLC with its principal place of business at 1205 43rd Street, Suite B, Orlando, Florida 32839 (the "Company") and [Contributor] (the "Contributor").

## Project Description

This project involves development work on "Village of The Ages," a village simulation game where players guide communities through historical progressions and manage resource-based challenges.

**COMPANY:**

City of Gamers Inc.

By: ______________________
Name: <PERSON><PERSON><PERSON>igan
Title: President
Date: ______________________

**CONTRIBUTOR:**

[If an individual]

Name: [Contributor]
Date: ________________________
Address: [Contributor Address]
`;

// Mock Supabase for alliance data
const mockSupabaseResponse = {
  data: {
    id: 'test-alliance-id',
    name: 'Green Tech Alliance',
    description: 'A collective focused on sustainable technology solutions',
    alliance_type: 'established',
    business_model: {
      revenue_sharing: {
        method: 'contribution_based',
        base_percentage: 70
      }
    },
    industry: 'Technology',
    team_members: [
      {
        user_id: 'user-1',
        role: 'founder',
        users: {
          id: 'user-1',
          email: '<EMAIL>',
          user_metadata: {
            full_name: 'Jane Smith'
          }
        }
      }
    ]
  },
  error: null
};

// Mock the supabase module
const originalImport = await import('../../../client/src/utils/supabase/supabase.utils.js');
const mockSupabase = {
  from: () => ({
    select: () => ({
      eq: () => ({
        single: () => Promise.resolve(mockSupabaseResponse)
      })
    })
  })
};

// Replace the supabase export temporarily
originalImport.supabase = mockSupabase;

async function generateSampleAgreements() {
  const generator = new NewAgreementGenerator();

  // Test 1: Agreement with alliance
  console.log('🔄 Generating agreement with alliance-venture separation...');
  
  const mockProject = {
    id: 'test-project-id',
    name: 'EcoTech Innovations',
    description: 'A platform connecting eco-conscious consumers with sustainable product alternatives using AI recommendations',
    project_type: 'software',
    alliance_id: 'test-alliance-id',
    team_id: 'test-alliance-id'
  };

  const mockContributors = [
    {
      id: 'contributor-1',
      permission_level: 'Owner',
      display_name: 'John Doe',
      email: '<EMAIL>'
    }
  ];

  const mockCurrentUser = {
    id: 'user-1',
    email: '<EMAIL>',
    user_metadata: {
      full_name: 'Test Contributor'
    }
  };

  try {
    const result = await generator.generateAgreement(mockTemplate, mockProject, {
      contributors: mockContributors,
      currentUser: mockCurrentUser,
      fullName: 'Test Contributor'
    });

    // Save the agreement
    const outputDir = path.join(process.cwd(), 'src/tests/venture/output/agreements/updated');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    const filename = `alliance-venture-separated-${new Date().toISOString().split('T')[0]}.md`;
    const filepath = path.join(outputDir, filename);

    const metadata = `<!-- UPDATED AGREEMENT WITH ALLIANCE-VENTURE SEPARATION
Generated: ${new Date().toISOString()}
Alliance: Green Tech Alliance
Venture: EcoTech Innovations
Signer: Jane Smith (Founder)

KEY CHANGES:
- Company is now the alliance (Green Tech Alliance), not the venture
- Agreement is between contributor and alliance
- Venture (EcoTech Innovations) is referenced as the project being worked on
- Alliance founder (Jane Smith) signs for the company
-->

`;

    fs.writeFileSync(filepath, metadata + result);
    
    console.log(`✅ Generated agreement saved to: ${filepath}`);
    console.log(`\n🏢 Alliance (Company): Green Tech Alliance`);
    console.log(`🚀 Venture (Project): EcoTech Innovations`);
    console.log(`✍️  Signer: Jane Smith (Founder)`);
    
    // Show key parts of the agreement
    const lines = result.split('\n');
    const companyLine = lines.find(line => line.includes('by and between'));
    const projectLine = lines.find(line => line.includes('development work on'));
    const signerLine = lines.find(line => line.includes('Name:') && !line.includes('['));
    
    console.log(`\n📄 Key Agreement Content:`);
    if (companyLine) console.log(`   Company: ${companyLine.trim()}`);
    if (projectLine) console.log(`   Project: ${projectLine.trim()}`);
    if (signerLine) console.log(`   Signer: ${signerLine.trim()}`);

  } catch (error) {
    console.error('❌ Error generating agreement:', error);
  }

  // Test 2: Fallback without alliance
  console.log('\n🔄 Generating fallback agreement without alliance...');
  
  const fallbackProject = {
    id: 'test-project-id-2',
    name: 'Solo Venture Project',
    description: 'A project without an alliance',
    project_type: 'software'
    // No alliance_id
  };

  // Mock no alliance response
  originalImport.supabase = {
    from: () => ({
      select: () => ({
        eq: () => ({
          single: () => Promise.resolve({ data: null, error: { message: 'Not found' } })
        })
      })
    })
  };

  try {
    const fallbackResult = await generator.generateAgreement(mockTemplate, fallbackProject, {
      contributors: [
        {
          id: 'contributor-1',
          permission_level: 'Owner',
          display_name: 'Solo Developer',
          email: '<EMAIL>'
        }
      ],
      currentUser: mockCurrentUser,
      fullName: 'Test Contributor'
    });

    const fallbackFilename = `fallback-no-alliance-${new Date().toISOString().split('T')[0]}.md`;
    const fallbackFilepath = path.join(outputDir, fallbackFilename);

    const fallbackMetadata = `<!-- FALLBACK AGREEMENT WITHOUT ALLIANCE
Generated: ${new Date().toISOString()}
Company: Solo Developer (fallback)
Venture: Solo Venture Project
Note: No alliance found, using contributor as company
-->

`;

    fs.writeFileSync(fallbackFilepath, fallbackMetadata + fallbackResult);
    
    console.log(`✅ Fallback agreement saved to: ${fallbackFilepath}`);
    console.log(`\n⚠️  No alliance found - using fallback mode`);
    console.log(`🏢 Company: Solo Developer (contributor name)`);
    console.log(`🚀 Venture: Solo Venture Project`);

  } catch (error) {
    console.error('❌ Error generating fallback agreement:', error);
  }

  console.log('\n🎉 Sample agreement generation complete!');
  console.log('\nYou can now compare:');
  console.log('- Old system: venture name used as company');
  console.log('- New system: alliance name used as company, venture as project');
}

generateSampleAgreements().catch(console.error);

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Input, Textarea, Select, SelectItem, Card, CardBody, Chip, Switch } from '@heroui/react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';

/**
 * Mission Creator Component - Mission Creation and Configuration Tools
 * 
 * Features:
 * - Multi-step mission creation with story elements
 * - Skill requirements and difficulty configuration
 * - Reward system setup and experience calculation
 * - Narrative system integration with chapter planning
 * - Integration with mission creation APIs
 */
const QuestCreator = ({ isOpen, onClose, onSuccess, currentUser }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  
  // Form data state
  const [missionData, setMissionData] = useState({
    // Step 1: Mission Basics
    title: '',
    description: '',
    type: 'skill_development',
    difficulty: 'intermediate',
    estimatedTime: '',
    
    // Step 2: Story Elements
    story: {
      theme: '',
      narrative: '',
      chapters: 1,
      hasNarrative: false
    },
    
    // Step 3: Requirements & Skills
    requirements: {
      skills: [],
      level: 1,
      studio: false
    },
    
    // Step 4: Rewards & Experience
    rewards: {
      experience: 500,
      orbs: 1000,
      achievements: [],
      skills: []
    },
    
    // Step 5: Configuration
    isPublic: true,
    maxParticipants: 0, // 0 = unlimited
    autoStart: false
  });

  const totalSteps = 5;

  // Mission type options
  const questTypes = [
    { key: 'skill_development', label: 'Skill Development', icon: '📚', description: 'Learn and master new skills' },
    { key: 'leadership', label: 'Leadership', icon: '👑', description: 'Lead teams and projects' },
    { key: 'development', label: 'Development', icon: '💻', description: 'Build and create solutions' },
    { key: 'design', label: 'Design', icon: '🎨', description: 'Create visual and user experiences' },
    { key: 'research', label: 'Research', icon: '🔬', description: 'Investigate and analyze' },
    { key: 'collaboration', label: 'Collaboration', icon: '🤝', description: 'Work with others effectively' }
  ];

  // Difficulty options
  const difficulties = [
    { key: 'beginner', label: 'Beginner', multiplier: 1.0, description: 'New to the skill area' },
    { key: 'intermediate', label: 'Intermediate', multiplier: 1.5, description: 'Some experience required' },
    { key: 'advanced', label: 'Advanced', multiplier: 2.0, description: 'Significant expertise needed' },
    { key: 'expert', label: 'Expert', multiplier: 3.0, description: 'Master-level challenge' }
  ];

  // Story themes
  const storyThemes = [
    'Ancient Code Temple',
    'Kingdom Building',
    'Digital Treasury',
    'Mystic Forest',
    'Space Station',
    'Underwater City',
    'Cyber Realm',
    'Mountain Fortress'
  ];

  // Common skills
  const commonSkills = [
    'JavaScript', 'React', 'TypeScript', 'Python', 'Node.js',
    'UI/UX Design', 'Project Management', 'Leadership', 'Communication',
    'Data Analysis', 'Machine Learning', 'DevOps', 'Testing'
  ];

  // Handle form field changes
  const handleFieldChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setMissionData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setMissionData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Handle skill addition
  const handleAddSkill = (skillType, skill) => {
    if (skillType === 'requirements') {
      setMissionData(prev => ({
        ...prev,
        requirements: {
          ...prev.requirements,
          skills: [...prev.requirements.skills, skill]
        }
      }));
    } else if (skillType === 'rewards') {
      setMissionData(prev => ({
        ...prev,
        rewards: {
          ...prev.rewards,
          skills: [...prev.rewards.skills, skill]
        }
      }));
    }
  };

  // Handle skill removal
  const handleRemoveSkill = (skillType, skillIndex) => {
    if (skillType === 'requirements') {
      setMissionData(prev => ({
        ...prev,
        requirements: {
          ...prev.requirements,
          skills: prev.requirements.skills.filter((_, index) => index !== skillIndex)
        }
      }));
    } else if (skillType === 'rewards') {
      setMissionData(prev => ({
        ...prev,
        rewards: {
          ...prev.rewards,
          skills: prev.rewards.skills.filter((_, index) => index !== skillIndex)
        }
      }));
    }
  };

  // Calculate experience based on difficulty
  const calculateExperience = () => {
    const baseXP = 500;
    const difficulty = difficulties.find(d => d.key === missionData.difficulty);
    return Math.round(baseXP * (difficulty?.multiplier || 1));
  };

  // Validate current step
  const validateStep = (step) => {
    switch (step) {
      case 1:
        return missionData.title.trim() && missionData.description.trim() && missionData.type && missionData.difficulty;
      case 2:
        return !missionData.story.hasNarrative || (missionData.story.theme && missionData.story.narrative);
      case 3:
        return missionData.requirements.level > 0;
      case 4:
        return missionData.rewards.experience > 0 && missionData.rewards.orbs > 0;
      case 5:
        return true; // Configuration step has defaults
      default:
        return false;
    }
  };

  // Handle next step
  const handleNext = () => {
    if (validateStep(currentStep)) {
      // Auto-calculate experience when moving from difficulty step
      if (currentStep === 1) {
        setMissionData(prev => ({
          ...prev,
          rewards: {
            ...prev.rewards,
            experience: calculateExperience()
          }
        }));
      }
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    } else {
      toast.error('Please fill in all required fields');
    }
  };

  // Handle previous step
  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // Handle mission creation
  const handleCreateQuest = async () => {
    try {
      setLoading(true);
      
      const questPayload = {
        title: missionData.title,
        description: missionData.description,
        task_category: 'mission',
        mission_type: missionData.type,
        difficulty_level: missionData.difficulty,
        estimated_hours: parseInt(missionData.estimatedTime) || 0,
        quest_requirements: {
          skills: missionData.requirements.skills,
          level: missionData.requirements.level,
          alliance_required: missionData.requirements.studio
        },
        quest_rewards: {
          experience: missionData.rewards.experience,
          orbs: missionData.rewards.orbs,
          achievements: missionData.rewards.achievements,
          skills: missionData.rewards.skills
        },
        quest_story: missionData.story.hasNarrative ? {
          theme: missionData.story.theme,
          narrative: missionData.story.narrative,
          chapters: missionData.story.chapters
        } : null,
        is_public: missionData.isPublic,
        max_participants: missionData.maxParticipants || null
      };

      const response = await fetch('/.netlify/functions/mission-system', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(questPayload)
      });

      if (response.ok) {
        const result = await response.json();
        toast.success('Mission created successfully!');
        onSuccess(result.mission);
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create mission');
      }
      
    } catch (error) {
      console.error('Error creating mission:', error);
      toast.error(error.message || 'Failed to create mission');
    } finally {
      setLoading(false);
    }
  };

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold mb-2">Mission Basics</h3>
              <p className="text-default-600">Define the core elements of your mission</p>
            </div>

            <Input
              label="Mission Title"
              placeholder="Enter an engaging mission title"
              value={missionData.title}
              onChange={(e) => handleFieldChange('title', e.target.value)}
              isRequired
              variant="bordered"
            />

            <Textarea
              label="Mission Description"
              placeholder="Describe the mission objectives and what participants will accomplish"
              value={missionData.description}
              onChange={(e) => handleFieldChange('description', e.target.value)}
              isRequired
              variant="bordered"
              minRows={3}
            />

            <div>
              <label className="block text-sm font-medium mb-3">Mission Type</label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {questTypes.map((type) => (
                  <Card
                    key={type.key}
                    className={`cursor-pointer transition-all ${
                      missionData.type === type.key
                        ? 'ring-2 ring-primary bg-primary-50 dark:bg-primary-900/20'
                        : 'hover:shadow-md'
                    }`}
                    onClick={() => handleFieldChange('type', type.key)}
                  >
                    <CardBody className="p-4">
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{type.icon}</span>
                        <div>
                          <div className="font-semibold">{type.label}</div>
                          <div className="text-sm text-default-600">{type.description}</div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Difficulty Level"
                selectedKeys={[missionData.difficulty]}
                onSelectionChange={(keys) => handleFieldChange('difficulty', Array.from(keys)[0])}
                variant="bordered"
              >
                {difficulties.map((difficulty) => (
                  <SelectItem key={difficulty.key} value={difficulty.key}>
                    <div>
                      <div className="font-medium">{difficulty.label}</div>
                      <div className="text-xs text-default-500">{difficulty.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </Select>

              <Input
                label="Estimated Time"
                placeholder="e.g., 2-3 weeks"
                value={missionData.estimatedTime}
                onChange={(e) => handleFieldChange('estimatedTime', e.target.value)}
                variant="bordered"
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold mb-2">Story Elements</h3>
              <p className="text-default-600">Add narrative elements to make your mission engaging</p>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">Enable Story Mode</h4>
                <p className="text-sm text-default-600">Add narrative elements and chapters to your mission</p>
              </div>
              <Switch
                isSelected={missionData.story.hasNarrative}
                onValueChange={(value) => handleFieldChange('story.hasNarrative', value)}
              />
            </div>

            {missionData.story.hasNarrative && (
              <>
                <Select
                  label="Story Theme"
                  placeholder="Choose a theme for your mission"
                  selectedKeys={missionData.story.theme ? [missionData.story.theme] : []}
                  onSelectionChange={(keys) => handleFieldChange('story.theme', Array.from(keys)[0])}
                  variant="bordered"
                >
                  {storyThemes.map((theme) => (
                    <SelectItem key={theme} value={theme}>
                      {theme}
                    </SelectItem>
                  ))}
                </Select>

                <Textarea
                  label="Opening Narrative"
                  placeholder="Write an engaging opening that sets the scene for your mission..."
                  value={missionData.story.narrative}
                  onChange={(e) => handleFieldChange('story.narrative', e.target.value)}
                  variant="bordered"
                  minRows={4}
                />

                <Input
                  type="number"
                  label="Number of Chapters"
                  placeholder="1"
                  value={missionData.story.chapters}
                  onChange={(e) => handleFieldChange('story.chapters', parseInt(e.target.value))}
                  variant="bordered"
                  min={1}
                  max={10}
                />
              </>
            )}

            {!missionData.story.hasNarrative && (
              <div className="text-center py-8 text-default-500">
                <div className="text-4xl mb-4">📖</div>
                <p>Enable story mode to add narrative elements to your mission</p>
              </div>
            )}
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold mb-2">Requirements & Skills</h3>
              <p className="text-default-600">Define what participants need to start this mission</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                type="number"
                label="Minimum Level"
                placeholder="1"
                value={missionData.requirements.level}
                onChange={(e) => handleFieldChange('requirements.level', parseInt(e.target.value))}
                variant="bordered"
                min={1}
                max={100}
              />

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Studio Required</h4>
                  <p className="text-sm text-default-600">Must be part of an studio</p>
                </div>
                <Switch
                  isSelected={missionData.requirements.studio}
                  onValueChange={(value) => handleFieldChange('requirements.studio', value)}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-3">Required Skills</label>
              <div className="space-y-3">
                <Select
                  placeholder="Add required skills"
                  onSelectionChange={(keys) => {
                    const skill = Array.from(keys)[0];
                    if (skill && !missionData.requirements.skills.includes(skill)) {
                      handleAddSkill('requirements', skill);
                    }
                  }}
                  variant="bordered"
                >
                  {commonSkills.filter(skill => !missionData.requirements.skills.includes(skill)).map((skill) => (
                    <SelectItem key={skill} value={skill}>
                      {skill}
                    </SelectItem>
                  ))}
                </Select>

                {missionData.requirements.skills.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {missionData.requirements.skills.map((skill, index) => (
                      <Chip
                        key={index}
                        onClose={() => handleRemoveSkill('requirements', index)}
                        variant="flat"
                        color="primary"
                      >
                        {skill}
                      </Chip>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold mb-2">Rewards & Experience</h3>
              <p className="text-default-600">Configure what participants will earn</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                type="number"
                label="Experience Points"
                value={missionData.rewards.experience}
                onChange={(e) => handleFieldChange('rewards.experience', parseInt(e.target.value))}
                variant="bordered"
                min={1}
                endContent={<span className="text-default-400">XP</span>}
              />

              <Input
                type="number"
                label="ORB Reward"
                value={missionData.rewards.orbs}
                onChange={(e) => handleFieldChange('rewards.orbs', parseInt(e.target.value))}
                variant="bordered"
                min={1}
                startContent={<span className="text-default-400">💎</span>}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-3">Skill Rewards</label>
              <Select
                placeholder="Add skills that will be developed"
                onSelectionChange={(keys) => {
                  const skill = Array.from(keys)[0];
                  if (skill && !missionData.rewards.skills.includes(skill)) {
                    handleAddSkill('rewards', skill);
                  }
                }}
                variant="bordered"
              >
                {commonSkills.filter(skill => !missionData.rewards.skills.includes(skill)).map((skill) => (
                  <SelectItem key={skill} value={skill}>
                    {skill}
                  </SelectItem>
                ))}
              </Select>

              {missionData.rewards.skills.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-3">
                  {missionData.rewards.skills.map((skill, index) => (
                    <Chip
                      key={index}
                      onClose={() => handleRemoveSkill('rewards', index)}
                      variant="flat"
                      color="success"
                    >
                      {skill}
                    </Chip>
                  ))}
                </div>
              )}
            </div>

            <Card className="bg-blue-50 dark:bg-blue-900/20">
              <CardBody className="p-4">
                <h4 className="font-semibold mb-2">Reward Preview</h4>
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">{missionData.rewards.experience}</div>
                    <div className="text-sm text-default-600">Experience Points</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">{missionData.rewards.orbs}</div>
                    <div className="text-sm text-default-600">ORBs</div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold mb-2">Mission Configuration</h3>
              <p className="text-default-600">Final settings for your mission</p>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Public Mission</h4>
                  <p className="text-sm text-default-600">Allow anyone to discover and start this mission</p>
                </div>
                <Switch
                  isSelected={missionData.isPublic}
                  onValueChange={(value) => handleFieldChange('isPublic', value)}
                />
              </div>

              <Input
                type="number"
                label="Maximum Participants"
                placeholder="0 for unlimited"
                value={missionData.maxParticipants}
                onChange={(e) => handleFieldChange('maxParticipants', parseInt(e.target.value))}
                variant="bordered"
                min={0}
                max={1000}
              />

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Auto-Start</h4>
                  <p className="text-sm text-default-600">Mission starts immediately when requirements are met</p>
                </div>
                <Switch
                  isSelected={missionData.autoStart}
                  onValueChange={(value) => handleFieldChange('autoStart', value)}
                />
              </div>
            </div>

            {/* Mission Summary */}
            <Card>
              <CardBody className="p-6">
                <h4 className="font-semibold text-lg mb-4">{missionData.title}</h4>
                <p className="text-default-600 mb-4">{missionData.description}</p>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-default-500">Type:</span>
                    <span className="ml-2 font-medium">{missionData.type}</span>
                  </div>
                  <div>
                    <span className="text-default-500">Difficulty:</span>
                    <span className="ml-2 font-medium">{missionData.difficulty}</span>
                  </div>
                  <div>
                    <span className="text-default-500">Experience:</span>
                    <span className="ml-2 font-medium">{missionData.rewards.experience} XP</span>
                  </div>
                  <div>
                    <span className="text-default-500">ORBs:</span>
                    <span className="ml-2 font-medium">{missionData.rewards.orbs}</span>
                  </div>
                </div>

                {missionData.story.hasNarrative && (
                  <div className="mt-4 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div className="font-medium text-purple-700 dark:text-purple-300">
                      Story: {missionData.story.theme}
                    </div>
                    <div className="text-sm text-purple-600 dark:text-purple-400">
                      {missionData.story.chapters} chapters
                    </div>
                  </div>
                )}
              </CardBody>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="2xl"
      isDismissable={!loading}
      hideCloseButton={loading}
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h2 className="text-2xl font-bold">Create New Mission</h2>
          <div className="flex items-center gap-2">
            <span className="text-default-600 font-normal">Step {currentStep} of {totalSteps}</span>
            <div className="flex-1 bg-default-200 rounded-full h-2 ml-4">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${(currentStep / totalSteps) * 100}%` }}
              />
            </div>
          </div>
        </ModalHeader>
        
        <ModalBody>
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {renderStepContent()}
          </motion.div>
        </ModalBody>
        
        <ModalFooter>
          <div className="flex justify-between w-full">
            <Button 
              color="danger" 
              variant="flat" 
              onPress={onClose}
              disabled={loading}
            >
              Cancel
            </Button>
            
            <div className="flex gap-2">
              {currentStep > 1 && (
                <Button 
                  color="default" 
                  variant="bordered"
                  onPress={handlePrevious}
                  disabled={loading}
                >
                  Previous
                </Button>
              )}
              
              {currentStep < totalSteps ? (
                <Button 
                  color="primary" 
                  onPress={handleNext}
                  disabled={!validateStep(currentStep) || loading}
                >
                  Next
                </Button>
              ) : (
                <Button 
                  color="success" 
                  onPress={handleCreateQuest}
                  disabled={!validateStep(currentStep) || loading}
                  isLoading={loading}
                >
                  Create Mission
                </Button>
              )}
            </div>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default QuestCreator;

#!/usr/bin/env node

/**
 * Template Accuracy Validation Script
 * 
 * Compares the rebuilt V2 template against the lawyer-approved template
 * to measure accuracy and identify any remaining issues.
 */

import fs from 'fs';
import path from 'path';

class TemplateAccuracyValidator {
  constructor() {
    this.lawyerTemplatePath = 'client/public/example-cog-contributor-agreement.md';
    this.v2TemplatePath = 'client/public/templates/v2/standard-contributor-agreement.md';
    this.results = {
      accuracyScore: 0,
      sectionsMatched: 0,
      totalSections: 0,
      definitionsMatched: 0,
      totalDefinitions: 0,
      structuralIssues: [],
      missingContent: [],
      criticalIssues: [],
      warnings: []
    };
  }

  async validateTemplate() {
    console.log('🔍 Template Accuracy Validation');
    console.log('===============================');

    try {
      // Load templates
      const lawyerTemplate = fs.readFileSync(this.lawyerTemplatePath, 'utf8');
      const v2Template = fs.readFileSync(this.v2TemplatePath, 'utf8');

      console.log(`📄 Lawyer template: ${lawyerTemplate.length} characters`);
      console.log(`📄 V2 template: ${v2Template.length} characters`);

      // Validate structure
      await this.validateStructure(lawyerTemplate, v2Template);
      
      // Validate sections
      await this.validateSections(lawyerTemplate, v2Template);
      
      // Validate definitions
      await this.validateDefinitions(lawyerTemplate, v2Template);
      
      // Validate critical content
      await this.validateCriticalContent(lawyerTemplate, v2Template);
      
      // Calculate accuracy score
      this.calculateAccuracyScore();
      
      // Generate report
      this.generateReport();

    } catch (error) {
      console.error('❌ Validation failed:', error.message);
      return false;
    }
  }

  validateStructure(lawyerTemplate, v2Template) {
    console.log('\n📋 Validating Structure...');

    // Check for main sections
    const requiredSections = [
      'CONTRIBUTOR AGREEMENT',
      'Recitals',
      '1. Definitions',
      '2. Treatment of Confidential Information',
      '3. Ownership of Work Product',
      'SCHEDULE A',
      'SCHEDULE B',
      'EXHIBIT I',
      'EXHIBIT II'
    ];

    requiredSections.forEach(section => {
      if (v2Template.includes(section)) {
        console.log(`  ✅ ${section}`);
        this.results.sectionsMatched++;
      } else {
        console.log(`  ❌ ${section} - MISSING`);
        this.results.missingContent.push(section);
      }
      this.results.totalSections++;
    });

    // Check section order
    const sectionOrder = ['SCHEDULE A', 'SCHEDULE B', 'EXHIBIT I', 'EXHIBIT II'];
    let lastIndex = -1;
    let orderCorrect = true;

    sectionOrder.forEach(section => {
      const index = v2Template.indexOf(section);
      if (index !== -1) {
        if (index < lastIndex) {
          orderCorrect = false;
          this.results.structuralIssues.push(`Section order incorrect: ${section}`);
        }
        lastIndex = index;
      }
    });

    if (orderCorrect) {
      console.log('  ✅ Section order correct');
    } else {
      console.log('  ❌ Section order incorrect');
    }
  }

  validateSections(lawyerTemplate, v2Template) {
    console.log('\n📝 Validating Section Content...');

    // Check for detailed subsections
    const criticalSubsections = [
      '(a)', '(b)', '(c)', '(d)', '(e)', '(f)', '(g)', '(h)', '(i)'
    ];

    // Count subsections in each template
    const lawyerSubsections = (lawyerTemplate.match(/\([a-z]\)/g) || []).length;
    const v2Subsections = (v2Template.match(/\([a-z]\)/g) || []).length;

    console.log(`  📊 Lawyer template subsections: ${lawyerSubsections}`);
    console.log(`  📊 V2 template subsections: ${v2Subsections}`);

    if (v2Subsections >= lawyerSubsections * 0.8) {
      console.log('  ✅ Adequate subsection coverage');
    } else {
      console.log('  ⚠️  Low subsection coverage');
      this.results.warnings.push('Low subsection coverage compared to lawyer template');
    }

    // Check for specific critical sections
    const criticalSections = [
      'Non-Solicitation',
      'Non-Competition', 
      'Restrictive Covenants',
      'Cybersecurity',
      'Indemnification'
    ];

    criticalSections.forEach(section => {
      if (v2Template.includes(section)) {
        console.log(`  ✅ ${section} section present`);
      } else {
        console.log(`  ❌ ${section} section missing`);
        this.results.criticalIssues.push(`Missing critical section: ${section}`);
      }
    });
  }

  validateDefinitions(lawyerTemplate, v2Template) {
    console.log('\n📚 Validating Definitions...');

    // Extract definitions from lawyer template
    const lawyerDefinitions = this.extractDefinitions(lawyerTemplate);
    const v2Definitions = this.extractDefinitions(v2Template);

    console.log(`  📊 Lawyer template definitions: ${lawyerDefinitions.length}`);
    console.log(`  📊 V2 template definitions: ${v2Definitions.length}`);

    // Check for critical definitions
    const criticalDefinitions = [
      'Background IP',
      'Confidential Documents',
      'Contribution',
      'Developed IP',
      'Work Product',
      'Milestones',
      'Programs',
      'Revenue Tranche',
      'Contribution Points'
    ];

    criticalDefinitions.forEach(def => {
      if (v2Definitions.some(d => d.includes(def))) {
        console.log(`  ✅ ${def}`);
        this.results.definitionsMatched++;
      } else {
        console.log(`  ❌ ${def} - MISSING`);
        this.results.missingContent.push(`Definition: ${def}`);
      }
      this.results.totalDefinitions++;
    });
  }

  extractDefinitions(template) {
    const definitionPattern = /\([a-z]\)\s*\*\*"([^"]+)"\*\*/g;
    const definitions = [];
    let match;

    while ((match = definitionPattern.exec(template)) !== null) {
      definitions.push(match[1]);
    }

    return definitions;
  }

  validateCriticalContent(lawyerTemplate, v2Template) {
    console.log('\n🔍 Validating Critical Content...');

    // Check for hardcoded content (should not be present)
    const forbiddenContent = [
      'City of Gamers Inc.',
      'Village of The Ages',
      'Gynell Journigan',
      '1205 43rd Street',
      'Orange County',
      'Florida corporation'
    ];

    let hardcodedFound = false;
    forbiddenContent.forEach(content => {
      if (v2Template.includes(content)) {
        console.log(`  ❌ Hardcoded content found: ${content}`);
        this.results.criticalIssues.push(`Hardcoded content: ${content}`);
        hardcodedFound = true;
      }
    });

    if (!hardcodedFound) {
      console.log('  ✅ No hardcoded content found');
    }

    // Check for proper variable usage
    const variablePattern = /\{\{[A-Z_]+\}\}/g;
    const variables = v2Template.match(variablePattern) || [];
    
    console.log(`  📊 Variables found: ${variables.length}`);
    
    if (variables.length > 10) {
      console.log('  ✅ Adequate variable usage');
    } else {
      console.log('  ⚠️  Low variable usage');
      this.results.warnings.push('Low variable usage - may indicate hardcoded content');
    }

    // Check for unreplaced placeholders
    const placeholderPatterns = [
      /\[[A-Z][^\]]*\]/g,
      /\[_+\]/g
    ];

    let placeholdersFound = false;
    placeholderPatterns.forEach(pattern => {
      const matches = v2Template.match(pattern);
      if (matches) {
        console.log(`  ❌ Unreplaced placeholders: ${matches.join(', ')}`);
        this.results.criticalIssues.push(`Unreplaced placeholders: ${matches.join(', ')}`);
        placeholdersFound = true;
      }
    });

    if (!placeholdersFound) {
      console.log('  ✅ No unreplaced placeholders found');
    }
  }

  calculateAccuracyScore() {
    // Calculate weighted accuracy score
    const sectionScore = (this.results.sectionsMatched / this.results.totalSections) * 40;
    const definitionScore = (this.results.definitionsMatched / this.results.totalDefinitions) * 30;
    const structuralScore = this.results.structuralIssues.length === 0 ? 20 : 10;
    const criticalScore = this.results.criticalIssues.length === 0 ? 10 : 0;

    this.results.accuracyScore = Math.round(sectionScore + definitionScore + structuralScore + criticalScore);
  }

  generateReport() {
    console.log('\n📊 ACCURACY VALIDATION RESULTS');
    console.log('==============================');
    console.log(`Overall Accuracy Score: ${this.results.accuracyScore}%`);
    console.log(`Sections Matched: ${this.results.sectionsMatched}/${this.results.totalSections}`);
    console.log(`Definitions Matched: ${this.results.definitionsMatched}/${this.results.totalDefinitions}`);
    console.log(`Structural Issues: ${this.results.structuralIssues.length}`);
    console.log(`Critical Issues: ${this.results.criticalIssues.length}`);
    console.log(`Warnings: ${this.results.warnings.length}`);

    if (this.results.criticalIssues.length > 0) {
      console.log('\n🚨 CRITICAL ISSUES:');
      this.results.criticalIssues.forEach(issue => {
        console.log(`   - ${issue}`);
      });
    }

    if (this.results.missingContent.length > 0) {
      console.log('\n⚠️  MISSING CONTENT:');
      this.results.missingContent.forEach(content => {
        console.log(`   - ${content}`);
      });
    }

    if (this.results.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      this.results.warnings.forEach(warning => {
        console.log(`   - ${warning}`);
      });
    }

    // Overall assessment
    console.log('\n🎯 ASSESSMENT:');
    if (this.results.accuracyScore >= 95) {
      console.log('✅ TEMPLATE READY FOR PRODUCTION');
      console.log('   - Meets 95%+ accuracy threshold');
      console.log('   - All critical sections present');
      console.log('   - Legal structure matches lawyer template');
    } else if (this.results.accuracyScore >= 85) {
      console.log('⚠️  TEMPLATE NEEDS MINOR IMPROVEMENTS');
      console.log(`   - Accuracy ${this.results.accuracyScore}% below 95% threshold`);
      console.log('   - Address missing content and warnings');
    } else {
      console.log('❌ TEMPLATE NEEDS MAJOR IMPROVEMENTS');
      console.log(`   - Accuracy ${this.results.accuracyScore}% significantly below threshold`);
      console.log('   - Critical issues must be resolved');
    }

    // Save detailed report
    const reportPath = path.join(process.cwd(), 'template-accuracy-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    console.log(`\n📄 Detailed report saved: ${reportPath}`);

    return this.results.accuracyScore >= 95;
  }
}

// Run validation if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new TemplateAccuracyValidator();
  validator.validateTemplate().catch(console.error);
}

export { TemplateAccuracyValidator };

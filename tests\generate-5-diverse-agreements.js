/**
 * Generate 5 Diverse Legal Contributor Agreements
 * 
 * This script generates 5 different agreements using the actual Royaltea platform
 * user flow and input methods to demonstrate system versatility and accuracy.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🎯 GENERATING 5 DIVERSE LEGAL CONTRIBUTOR AGREEMENTS');
console.log('=' .repeat(70));
console.log('Using actual Royaltea platform user flow and input methods');
console.log('Testing system versatility across different business scenarios');
console.log('=' .repeat(70));

/**
 * Test scenarios representing diverse business cases
 */
const DIVERSE_SCENARIOS = {
  // Scenario 1: Game Development Project (Baseline VOTA-style)
  GAME_DEVELOPMENT: {
    name: 'Mystic Realms Online',
    description: 'A fantasy MMORPG featuring dynamic world events, player-driven economy, and guild-based territorial control',
    project_type: 'game',
    estimated_duration: 18,
    platforms: 'PC, PlayStation 5, Xbox Series X/S',
    engine: 'Unreal Engine 5',
    
    // Company Information
    company_name: 'Ethereal Studios Inc.',
    legal_entity_type: 'corporation',
    incorporation_state: 'California',
    address: '2500 Innovation Drive, Suite 300, San Francisco, California 94107',
    city: 'San Francisco',
    state: 'California',
    county: 'San Francisco',
    zip: '94107',
    contact_email: '<EMAIL>',
    signer_name: 'Alexandra Chen',
    signer_title: 'Chief Executive Officer',
    
    // Financial Terms
    revenueShare: 35,
    payoutThreshold: 250000,
    maxPayment: 2500000,
    
    // Project Details
    features: 'Advanced guild system, dynamic world events, player housing, crafting economy, PvP territories',
    coreFeatures: `1. **Massive Multiplayer Systems**
   - Support for 10,000+ concurrent players per server
   - Advanced networking and server architecture
   - Real-time combat and interaction systems

2. **Dynamic World Events**
   - Procedurally generated world events
   - Player-influenced storylines
   - Seasonal content and updates

3. **Economic Systems**
   - Player-driven marketplace
   - Complex crafting and resource systems
   - Guild-based territorial control`,
    
    technicalRequirements: `- Platform: PC (Steam, Epic), PlayStation 5, Xbox Series X/S
- Engine: Unreal Engine 5 with custom networking layer
- MinSpecs: RTX 3060 / RX 6600 XT, 16GB RAM, SSD required
- Audio: 3D spatial audio with voice chat integration
- Networking: Dedicated servers with global infrastructure`,
    
    roadmapPhases: `**Pre-Alpha Development (Months 1-6)**
- Core networking infrastructure
- Basic character systems and combat
- Initial world design and asset pipeline

**Alpha Phase (Months 7-12)**
- Guild systems and territorial control
- Economic systems implementation
- First closed alpha testing

**Beta Phase (Months 13-18)**
- Content expansion and balancing
- Open beta testing and community feedback
- Performance optimization and launch preparation`
  },

  // Scenario 2: Software/SaaS Application
  SOFTWARE_SAAS: {
    name: 'DataFlow Analytics',
    description: 'An enterprise-grade business intelligence platform providing real-time analytics, automated reporting, and predictive insights for mid-market companies',
    project_type: 'software',
    estimated_duration: 12,
    platforms: 'Web Application (React/Node.js), Mobile Apps (iOS/Android)',
    framework: 'React, Node.js, PostgreSQL',
    
    // Company Information
    company_name: 'Insight Technologies Corporation',
    legal_entity_type: 'corporation',
    incorporation_state: 'Delaware',
    address: '1800 Business Park Drive, Austin, Texas 78759',
    city: 'Austin',
    state: 'Texas',
    county: 'Travis',
    zip: '78759',
    contact_email: '<EMAIL>',
    signer_name: 'Michael Rodriguez',
    signer_title: 'Chief Technology Officer',
    
    // Financial Terms
    revenueShare: 28,
    payoutThreshold: 500000,
    maxPayment: 3000000,
    
    // Project Details
    features: 'Real-time dashboards, automated report generation, predictive analytics, API integrations, white-label solutions',
    coreFeatures: `1. **Real-Time Analytics Engine**
   - Live data processing and visualization
   - Custom dashboard creation tools
   - Advanced filtering and drill-down capabilities

2. **Automated Reporting System**
   - Scheduled report generation
   - Custom template designer
   - Multi-format export (PDF, Excel, CSV)

3. **Predictive Analytics**
   - Machine learning-powered insights
   - Trend analysis and forecasting
   - Anomaly detection and alerts`,
    
    technicalRequirements: `- Platform: Web-based SaaS with mobile companion apps
- Frontend: React 18, TypeScript, Material-UI
- Backend: Node.js, Express, PostgreSQL, Redis
- Infrastructure: AWS with auto-scaling and load balancing
- Security: SOC 2 compliance, end-to-end encryption`,
    
    roadmapPhases: `**MVP Development (Months 1-4)**
- Core analytics engine and basic dashboards
- User authentication and basic reporting
- Initial API framework

**Feature Expansion (Months 5-8)**
- Advanced visualization tools
- Predictive analytics implementation
- Mobile app development

**Enterprise Ready (Months 9-12)**
- White-label solutions
- Advanced security features
- Enterprise integrations and API marketplace`
  },

  // Scenario 3: Music Production Project
  MUSIC_PRODUCTION: {
    name: 'Harmony Collective',
    description: 'A collaborative music production platform connecting independent artists, producers, and songwriters for creating and distributing original music across multiple genres',
    project_type: 'music',
    estimated_duration: 8,
    platforms: 'Digital Audio Workstation Plugin, Web Platform, Mobile App',
    tools: 'Pro Tools, Logic Pro, Ableton Live integration',
    
    // Company Information
    company_name: 'Sonic Innovations LLC',
    legal_entity_type: 'llc',
    incorporation_state: 'Tennessee',
    address: '1200 Music Row, Nashville, Tennessee 37203',
    city: 'Nashville',
    state: 'Tennessee',
    county: 'Davidson',
    zip: '37203',
    contact_email: '<EMAIL>',
    signer_name: 'Sarah Johnson',
    signer_title: 'Managing Member',
    
    // Financial Terms
    revenueShare: 40,
    payoutThreshold: 100000,
    maxPayment: 1500000,
    
    // Project Details
    features: 'Real-time collaboration tools, version control for audio projects, integrated marketplace, royalty management, distribution network',
    coreFeatures: `1. **Collaborative Production Tools**
   - Real-time multi-user audio editing
   - Cloud-based project synchronization
   - Integrated communication and feedback systems

2. **Marketplace and Distribution**
   - Artist-to-artist collaboration marketplace
   - Integrated music distribution to streaming platforms
   - Royalty tracking and payment automation

3. **Professional Audio Tools**
   - High-quality audio processing and effects
   - Industry-standard plugin compatibility
   - Advanced mixing and mastering tools`,
    
    technicalRequirements: `- Platform: Cross-platform desktop app, web interface, mobile companion
- Audio: 24-bit/192kHz audio processing, low-latency real-time collaboration
- Integration: VST/AU plugin support, DAW integration APIs
- Storage: Cloud-based project storage with version control
- Distribution: API integrations with Spotify, Apple Music, YouTube Music`,
    
    roadmapPhases: `**Core Platform (Months 1-3)**
- Basic collaboration tools and audio engine
- User authentication and project management
- Initial web platform development

**Advanced Features (Months 4-6)**
- Marketplace implementation
- Distribution network integration
- Mobile app development

**Professional Tools (Months 7-8)**
- Advanced audio processing features
- Industry partnership integrations
- Royalty management system`
  },

  // Scenario 4: Different Legal Entity (LLC) - Creative Agency
  CREATIVE_AGENCY_LLC: {
    name: 'Brand Elevation Suite',
    description: 'A comprehensive digital marketing and brand development platform providing AI-powered design tools, campaign management, and performance analytics for small to medium businesses',
    project_type: 'software',
    estimated_duration: 10,
    platforms: 'Web Application, Browser Extensions, Mobile Apps',
    framework: 'Vue.js, Python/Django, AI/ML Integration',
    
    // Company Information
    company_name: 'Creative Dynamics LLC',
    legal_entity_type: 'llc',
    incorporation_state: 'Colorado',
    address: '3000 Creative Commons Boulevard, Denver, Colorado 80202',
    city: 'Denver',
    state: 'Colorado',
    county: 'Denver',
    zip: '80202',
    contact_email: '<EMAIL>',
    signer_name: 'David Kim',
    signer_title: 'Managing Partner',
    
    // Financial Terms
    revenueShare: 30,
    payoutThreshold: 150000,
    maxPayment: 2000000,
    
    // Project Details
    features: 'AI-powered design generation, campaign automation, brand consistency tools, performance analytics, client collaboration portals',
    coreFeatures: `1. **AI-Powered Design Tools**
   - Automated logo and brand asset generation
   - Template customization with brand guidelines
   - Smart color palette and typography suggestions

2. **Campaign Management**
   - Multi-platform campaign orchestration
   - Automated A/B testing and optimization
   - Real-time performance monitoring

3. **Client Collaboration**
   - White-label client portals
   - Approval workflows and feedback systems
   - Project timeline and milestone tracking`,
    
    technicalRequirements: `- Platform: Progressive Web App with native mobile apps
- Frontend: Vue.js 3, TypeScript, Tailwind CSS
- Backend: Python/Django, PostgreSQL, Redis, Celery
- AI/ML: TensorFlow, OpenAI API integration
- Infrastructure: Google Cloud Platform with Kubernetes`,
    
    roadmapPhases: `**Foundation (Months 1-3)**
- Core design tools and user interface
- Basic campaign management features
- User authentication and workspace setup

**AI Integration (Months 4-7)**
- Machine learning model integration
- Automated design generation
- Performance optimization algorithms

**Enterprise Features (Months 8-10)**
- White-label solutions
- Advanced analytics and reporting
- Enterprise security and compliance features`
  },

  // Scenario 5: Different Jurisdiction (New York) - Fintech
  FINTECH_NY: {
    name: 'SecureWallet Pro',
    description: 'A next-generation cryptocurrency wallet and DeFi platform featuring advanced security protocols, multi-chain support, and institutional-grade portfolio management tools',
    project_type: 'software',
    estimated_duration: 15,
    platforms: 'Desktop Application, Mobile Apps, Web Interface, Hardware Integration',
    blockchain: 'Ethereum, Bitcoin, Polygon, Solana',
    
    // Company Information
    company_name: 'Blockchain Security Systems Inc.',
    legal_entity_type: 'corporation',
    incorporation_state: 'New York',
    address: '200 Financial District Plaza, New York, New York 10004',
    city: 'New York',
    state: 'New York',
    county: 'New York',
    zip: '10004',
    contact_email: '<EMAIL>',
    signer_name: 'Jennifer Walsh',
    signer_title: 'Chief Executive Officer',
    
    // Financial Terms
    revenueShare: 25,
    payoutThreshold: 750000,
    maxPayment: 5000000,
    
    // Project Details
    features: 'Multi-signature security, hardware wallet integration, DeFi protocol access, institutional custody, regulatory compliance tools',
    coreFeatures: `1. **Advanced Security Architecture**
   - Multi-signature wallet technology
   - Hardware security module integration
   - Biometric authentication and encryption

2. **Multi-Chain DeFi Access**
   - Cross-chain transaction support
   - Integrated DeFi protocol access
   - Automated yield farming and staking

3. **Institutional Features**
   - Enterprise-grade custody solutions
   - Compliance and reporting tools
   - Advanced portfolio analytics`,
    
    technicalRequirements: `- Platform: Cross-platform desktop (Electron), native mobile apps
- Blockchain: Multi-chain support (Ethereum, Bitcoin, Polygon, Solana)
- Security: Hardware security modules, multi-signature protocols
- Compliance: KYC/AML integration, regulatory reporting
- Infrastructure: High-availability cloud infrastructure with disaster recovery`,
    
    roadmapPhases: `**Security Foundation (Months 1-5)**
- Core wallet functionality and security protocols
- Multi-signature implementation
- Basic transaction management

**DeFi Integration (Months 6-10)**
- Cross-chain protocol integration
- DeFi access and yield farming tools
- Advanced portfolio management

**Institutional Platform (Months 11-15)**
- Enterprise custody solutions
- Regulatory compliance tools
- Institutional API and white-label solutions`
  }
};

/**
 * Load the agreement generator and template
 */
async function loadAgreementGenerator() {
  try {
    const { NewAgreementGenerator } = await import('../client/src/utils/agreement/newAgreementGenerator.js');
    const templatePath = path.join(__dirname, '../client/public/contributor-agreement-template.md');
    const templateText = fs.readFileSync(templatePath, 'utf8');
    
    return { NewAgreementGenerator, templateText };
  } catch (error) {
    console.error('❌ Failed to load agreement generator:', error);
    throw error;
  }
}

/**
 * Load the lawyer-approved example for comparison
 */
function loadLawyerExample() {
  const examplePath = path.join(__dirname, '../client/public/example-cog-contributor-agreement.md');
  return fs.readFileSync(examplePath, 'utf8');
}

/**
 * Generate agreement for a specific scenario
 */
async function generateScenarioAgreement(scenarioName, projectData, generator, templateText) {
  console.log(`\n🎯 Generating Agreement: ${scenarioName}`);
  console.log(`   📋 Project: ${projectData.name}`);
  console.log(`   🏢 Company: ${projectData.company_name}`);
  console.log(`   📍 Location: ${projectData.city}, ${projectData.state}`);
  console.log(`   💰 Revenue Share: ${projectData.revenueShare}%`);
  
  try {
    // Prepare realistic contributor and options data
    const options = {
      contributors: [{
        id: 'contributor_001',
        email: '<EMAIL>',
        name: 'Alex Thompson',
        address: '456 Developer Lane, Tech City, CA 90210',
        permission_level: 'Contributor'
      }],
      currentUser: { 
        id: 'project_owner', 
        email: projectData.contact_email,
        user_metadata: { full_name: projectData.signer_name }
      },
      fullName: 'Alex Thompson',
      agreementDate: new Date('2025-01-15'),

      // Alliance information for proper company validation
      allianceInfo: {
        id: `alliance_${scenarioName.toLowerCase()}`,
        name: projectData.company_name,
        owner_id: 'project_owner',
        contact_information: {
          primaryContact: {
            name: projectData.signer_name,
            email: projectData.contact_email,
            title: projectData.signer_title
          }
        },
        company_info: {
          legal_name: projectData.company_name,
          address: projectData.address,
          state: projectData.state,
          city: projectData.city,
          zip: projectData.zip,
          county: projectData.county,
          billing_email: projectData.contact_email,
          legal_entity_type: projectData.legal_entity_type,
          incorporation_state: projectData.incorporation_state
        }
      }
    };

    const startTime = Date.now();
    const agreement = await generator.generateAgreement(templateText, projectData, options);
    const endTime = Date.now();

    // Save the generated agreement
    const filename = `${scenarioName.toLowerCase()}-agreement.md`;
    const filepath = path.join(__dirname, filename);
    fs.writeFileSync(filepath, agreement);

    console.log(`   ✅ Generated in ${endTime - startTime}ms`);
    console.log(`   📄 Saved to: ${filename}`);
    console.log(`   📊 Length: ${agreement.length.toLocaleString()} characters`);

    return {
      success: true,
      agreement,
      filename,
      filepath,
      generationTime: endTime - startTime,
      length: agreement.length,
      projectData
    };
  } catch (error) {
    console.log(`   ❌ Generation failed: ${error.message}`);
    return {
      success: false,
      error: error.message,
      projectData
    };
  }
}

/**
 * Analyze agreement accuracy
 */
function analyzeAgreementAccuracy(agreement, lawyerExample, scenarioName) {
  const analysis = {
    scenarioName,
    structuralAccuracy: 0,
    contentAccuracy: 0,
    variableSubstitution: 0,
    legalCompliance: 0,
    overallAccuracy: 0,
    issues: [],
    strengths: []
  };

  // Essential structural elements to check
  const structuralElements = [
    'CONTRIBUTOR AGREEMENT',
    'Definitions',
    'Treatment of Confidential Information',
    'Ownership of Work Product',
    'Non-Disparagement',
    'Termination',
    'Equitable Remedies',
    'Assignment',
    'Waivers and Amendments',
    'Survival',
    'Status as Independent Contractor',
    'Representations and Warranties',
    'Indemnification',
    'Entire Agreement',
    'Governing Law',
    'Consent to Jurisdiction',
    'Settlement of Disputes',
    'Titles and Subtitles',
    'Opportunity to Consult',
    'Gender; Singular and Plural',
    'Notice',
    'Counterparts',
    'SCHEDULE A',
    'SCHEDULE B',
    'EXHIBIT I',
    'EXHIBIT II'
  ];

  // Check structural elements
  let structuralMatches = 0;
  structuralElements.forEach(element => {
    if (agreement.includes(element)) {
      structuralMatches++;
    } else {
      analysis.issues.push(`Missing structural element: ${element}`);
    }
  });
  analysis.structuralAccuracy = Math.round((structuralMatches / structuralElements.length) * 100);

  // Check for proper variable substitution (no remaining placeholders)
  const placeholderPattern = /\[([^\]]+)\]/g;
  const remainingPlaceholders = agreement.match(placeholderPattern) || [];
  const filteredPlaceholders = remainingPlaceholders.filter(p => 
    !p.includes('If an individual') && 
    !p.includes('If a company') && 
    !p.includes('Name of Company')
  );
  
  if (filteredPlaceholders.length === 0) {
    analysis.variableSubstitution = 100;
    analysis.strengths.push('Perfect variable substitution - no remaining placeholders');
  } else {
    analysis.variableSubstitution = Math.max(0, 100 - (filteredPlaceholders.length * 10));
    analysis.issues.push(`${filteredPlaceholders.length} unresolved placeholders: ${filteredPlaceholders.slice(0, 3).join(', ')}`);
  }

  // Check content accuracy (key legal phrases)
  const keyLegalPhrases = [
    'independent contractor',
    'intellectual property',
    'confidential information',
    'work product',
    'indemnification',
    'representations and warranties'
  ];

  let contentMatches = 0;
  keyLegalPhrases.forEach(phrase => {
    if (agreement.toLowerCase().includes(phrase)) {
      contentMatches++;
    }
  });
  analysis.contentAccuracy = Math.round((contentMatches / keyLegalPhrases.length) * 100);

  // Legal compliance check (critical sections)
  const criticalSections = [
    'Section 10(a)',
    'Section 10(b)', 
    'Section 11',
    'Section 12',
    'Section 17',
    'Section 18',
    'Section 19',
    'Section 20',
    'Section 21'
  ];

  let legalMatches = 0;
  criticalSections.forEach(section => {
    if (agreement.includes(section) || agreement.includes(section.replace('Section ', '## '))) {
      legalMatches++;
    }
  });
  analysis.legalCompliance = Math.round((legalMatches / criticalSections.length) * 100);

  // Calculate overall accuracy
  analysis.overallAccuracy = Math.round(
    (analysis.structuralAccuracy * 0.3 + 
     analysis.contentAccuracy * 0.2 + 
     analysis.variableSubstitution * 0.3 + 
     analysis.legalCompliance * 0.2)
  );

  return analysis;
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log('\n🚀 Loading agreement generator and template...');
    const { NewAgreementGenerator, templateText } = await loadAgreementGenerator();
    const lawyerExample = loadLawyerExample();
    const generator = new NewAgreementGenerator();
    
    console.log('✅ Dependencies loaded successfully\n');

    const results = [];
    const analyses = [];

    // Generate agreements for all scenarios
    for (const [scenarioName, projectData] of Object.entries(DIVERSE_SCENARIOS)) {
      const result = await generateScenarioAgreement(scenarioName, projectData, generator, templateText);
      results.push(result);

      if (result.success) {
        const analysis = analyzeAgreementAccuracy(result.agreement, lawyerExample, scenarioName);
        analyses.push(analysis);
      }
    }

    // Generate summary report
    console.log('\n' + '=' .repeat(70));
    console.log('📊 COMPREHENSIVE ANALYSIS SUMMARY');
    console.log('=' .repeat(70));

    const successfulGenerations = results.filter(r => r.success);
    const averageAccuracy = analyses.reduce((sum, a) => sum + a.overallAccuracy, 0) / analyses.length;
    const averageGenerationTime = successfulGenerations.reduce((sum, r) => sum + r.generationTime, 0) / successfulGenerations.length;

    console.log(`\n📈 OVERALL PERFORMANCE:`);
    console.log(`   ✅ Successful Generations: ${successfulGenerations.length}/${results.length}`);
    console.log(`   🎯 Average Accuracy: ${Math.round(averageAccuracy)}%`);
    console.log(`   ⚡ Average Generation Time: ${Math.round(averageGenerationTime)}ms`);
    console.log(`   📊 Total Characters Generated: ${successfulGenerations.reduce((sum, r) => sum + r.length, 0).toLocaleString()}`);

    console.log(`\n📋 INDIVIDUAL SCENARIO RESULTS:`);
    analyses.forEach(analysis => {
      const status = analysis.overallAccuracy >= 95 ? '✅' : analysis.overallAccuracy >= 90 ? '⚠️' : '❌';
      console.log(`   ${status} ${analysis.scenarioName}: ${analysis.overallAccuracy}% accuracy`);
      console.log(`      🏗️  Structural: ${analysis.structuralAccuracy}% | 📝 Content: ${analysis.contentAccuracy}% | 🔄 Variables: ${analysis.variableSubstitution}% | ⚖️  Legal: ${analysis.legalCompliance}%`);
    });

    // Save detailed results
    const detailedResults = {
      timestamp: new Date().toISOString(),
      summary: {
        totalScenarios: results.length,
        successfulGenerations: successfulGenerations.length,
        averageAccuracy: Math.round(averageAccuracy),
        averageGenerationTime: Math.round(averageGenerationTime)
      },
      scenarios: results.map((result, index) => ({
        ...result,
        analysis: analyses[index] || null
      }))
    };

    const reportPath = path.join(__dirname, 'diverse-agreements-analysis-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(detailedResults, null, 2));

    console.log(`\n💾 Detailed analysis saved to: diverse-agreements-analysis-report.json`);
    console.log(`📁 Generated agreements saved in tests/ directory`);

    // Final assessment
    if (averageAccuracy >= 95) {
      console.log(`\n🎉 EXCELLENT: System demonstrates high accuracy across diverse scenarios`);
      console.log(`✅ Production-ready for multiple business use cases`);
    } else if (averageAccuracy >= 90) {
      console.log(`\n⚠️  GOOD: System performs well but may need minor improvements`);
    } else {
      console.log(`\n❌ NEEDS IMPROVEMENT: System requires attention before production use`);
    }

  } catch (error) {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  }
}

// Execute the main function
main().catch(console.error);

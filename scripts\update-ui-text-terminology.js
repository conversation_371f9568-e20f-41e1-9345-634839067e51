#!/usr/bin/env node

/**
 * Update UI Text Terminology Script
 * Updates all user-facing text from old terminology to new terminology
 * Alliance → Studio, Venture → Project, Quest → Mission
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define comprehensive terminology mappings
const TERMINOLOGY_MAPPINGS = {
  // Primary terminology
  'Alliance': 'Studio',
  'alliance': 'studio',
  'Alliances': 'Studios',
  'alliances': 'studios',
  'ALLIANCE': 'STUDIO',
  'ALLIANCES': 'STUDIOS',
  
  'Venture': 'Project',
  'venture': 'project',
  'Ventures': 'Projects',
  'ventures': 'projects',
  'VENTURE': 'PROJECT',
  'VENTURES': 'PROJECTS',
  
  'Quest': 'Mission',
  'quest': 'mission',
  'Quests': 'Missions',
  'quests': 'missions',
  'QUEST': 'MISSION',
  'QUESTS': 'MISSIONS',

  // Specific UI text patterns
  'Create New Alliance': 'Create New Studio',
  'Alliance Management': 'Studio Management',
  'Alliance Dashboard': 'Studio Dashboard',
  'Alliance Creation': 'Studio Creation',
  'Alliance Settings': 'Studio Settings',
  'Alliance Members': 'Studio Members',
  'Alliance Analytics': 'Studio Analytics',
  'Alliance Treasury': 'Studio Treasury',
  'Alliance Ventures': 'Studio Projects',
  'Alliance Invitations': 'Studio Invitations',
  'Alliance Permissions': 'Studio Permissions',
  
  'Venture Management': 'Project Management',
  'Venture Creation': 'Project Creation',
  'Venture Dashboard': 'Project Dashboard',
  'Venture Settings': 'Project Settings',
  'Venture Analytics': 'Project Analytics',
  'Create Venture': 'Create Project',
  'New Venture': 'New Project',
  
  'Quest Board': 'Mission Board',
  'Quest System': 'Mission System',
  'Quest Creator': 'Mission Creator',
  'Quest Management': 'Mission Management',
  'Start Quest': 'Start Mission',
  'Complete Quest': 'Complete Mission',
  'Quest Progress': 'Mission Progress',
  'Quest Rewards': 'Mission Rewards',
  'Quest Requirements': 'Mission Requirements',

  // Alliance type descriptions
  'Emerging Alliance': 'Emerging Studio',
  'Established Alliance': 'Established Studio',
  'Solo Alliance': 'Solo Studio',
  'Growing collaborative group': 'Growing creative studio',
  'Incorporated business entity': 'Established creative business',
  'Individual creator': 'Independent creator',

  // Navigation and breadcrumbs
  'Back to Teams': 'Back to Studios',
  'Alliance List': 'Studio List',
  'Alliance Page': 'Studio Page',
  'Teams/Alliances': 'Teams/Studios',

  // Form labels and placeholders
  'Alliance Name': 'Studio Name',
  'Alliance Type': 'Studio Type',
  'Alliance Description': 'Studio Description',
  'Venture Name': 'Project Name',
  'Venture Type': 'Project Type',
  'Venture Description': 'Project Description',
  'Quest Title': 'Mission Title',
  'Quest Description': 'Mission Description',

  // Status and messages
  'Alliance elevated to Established status': 'Studio elevated to Established status',
  'Alliance created successfully': 'Studio created successfully',
  'Venture created successfully': 'Project created successfully',
  'Quest completed successfully': 'Mission completed successfully',
  'Alliance not found': 'Studio not found',
  'Venture not found': 'Project not found',
  'Quest not found': 'Mission not found',

  // Help text and descriptions
  'Build your professional network': 'Build your creative studio',
  'manage ventures': 'manage projects',
  'collaborate with the best talent': 'collaborate with creative professionals',
  'Professional network and start collaborating': 'Creative studio and start collaborating',
  'Enhanced team system with business compliance': 'Enhanced studio system with business compliance',
  'Alliance system with business compliance': 'Studio system with business compliance',

  // Icons and emojis context
  '🏰 Alliance': '🏢 Studio',
  '⚔️ Solo Alliance': '⭐ Solo Studio',
  '🌱 Emerging Alliance': '🌱 Emerging Studio',

  // Database and technical terms (for comments and logs)
  'alliance_id': 'studio_id',
  'venture_id': 'project_id',
  'quest_id': 'mission_id',
  'alliance_type': 'studio_type',
  'venture_type': 'project_type',
  'quest_type': 'mission_type',
  'alliance_invitations': 'studio_invitations',
  'alliance_preferences': 'studio_preferences',
  'user_quests': 'user_missions'
};

// Files to update (focusing on user-facing content)
const FILES_TO_UPDATE = [
  // Pages
  'client/src/pages/alliance/AllianceCreationPage.jsx',
  'client/src/pages/alliance/AlliancePage.jsx',
  'client/src/pages/ventures/VenturePage.jsx',
  'client/src/pages/quests/QuestPage.jsx',
  
  // Components
  'client/src/components/alliance/AllianceManage.jsx',
  'client/src/components/alliance/AllianceList.jsx',
  'client/src/components/alliance/AllianceDashboard.jsx',
  'client/src/components/alliance/AllianceVentures.jsx',
  'client/src/sections/teams/TeamCreation.jsx',
  
  // Any other files with user-facing text
  'client/src/components/team/TeamList.jsx',
  'client/src/components/team/TeamDetail.jsx',
  'client/src/components/team/TeamManage.jsx',
];

// Directories to scan for additional files
const DIRECTORIES_TO_SCAN = [
  'client/src/pages',
  'client/src/components',
  'client/src/sections'
];

function updateFileContent(content) {
  let updatedContent = content;
  
  // Apply all terminology mappings
  for (const [oldTerm, newTerm] of Object.entries(TERMINOLOGY_MAPPINGS)) {
    // Use word boundaries for exact matches to avoid partial replacements
    const regex = new RegExp(`\\b${oldTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g');
    updatedContent = updatedContent.replace(regex, newTerm);
  }
  
  return updatedContent;
}

function findFilesWithOldTerminology(dir, extensions = ['.jsx', '.js', '.ts', '.tsx']) {
  const files = [];
  
  function scanDirectory(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // Skip node_modules and other irrelevant directories
          if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist' && item !== 'build') {
            scanDirectory(fullPath);
          }
        } else if (stat.isFile()) {
          // Check if file has relevant extension
          if (extensions.some(ext => item.endsWith(ext))) {
            const content = fs.readFileSync(fullPath, 'utf8');
            
            // Check if file contains old terminology
            const hasOldTerms = Object.keys(TERMINOLOGY_MAPPINGS).some(term => 
              content.includes(term)
            );
            
            if (hasOldTerms) {
              files.push(fullPath);
            }
          }
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not scan directory ${currentDir}: ${error.message}`);
    }
  }
  
  scanDirectory(dir);
  return files;
}

async function updateUIText() {
  console.log('🔄 Updating UI Text Terminology...');
  console.log('=====================================\n');

  const projectRoot = path.join(__dirname, '..');
  let updatedFiles = 0;
  let totalFiles = 0;

  // Scan for files with old terminology
  console.log('1️⃣ Scanning for files with old terminology...');
  
  const filesToUpdate = new Set();
  
  // Add explicitly listed files
  FILES_TO_UPDATE.forEach(file => {
    const fullPath = path.join(projectRoot, file);
    if (fs.existsSync(fullPath)) {
      filesToUpdate.add(fullPath);
    }
  });

  // Scan directories for additional files
  DIRECTORIES_TO_SCAN.forEach(dir => {
    const fullDir = path.join(projectRoot, dir);
    if (fs.existsSync(fullDir)) {
      const foundFiles = findFilesWithOldTerminology(fullDir);
      foundFiles.forEach(file => filesToUpdate.add(file));
    }
  });

  console.log(`   Found ${filesToUpdate.size} files with old terminology`);

  // Update each file
  console.log('\n2️⃣ Updating files...');
  
  for (const filePath of filesToUpdate) {
    try {
      totalFiles++;
      const relativePath = path.relative(projectRoot, filePath);
      
      // Read current content
      const originalContent = fs.readFileSync(filePath, 'utf8');
      
      // Apply terminology updates
      const updatedContent = updateFileContent(originalContent);
      
      // Check if content actually changed
      if (originalContent !== updatedContent) {
        // Write updated content
        fs.writeFileSync(filePath, updatedContent, 'utf8');
        console.log(`   ✅ Updated: ${relativePath}`);
        updatedFiles++;
      } else {
        console.log(`   ⏭️ No changes: ${relativePath}`);
      }
      
    } catch (error) {
      console.error(`   ❌ Error updating ${filePath}: ${error.message}`);
    }
  }

  console.log('\n📊 Update Summary:');
  console.log('=====================================');
  console.log(`   Total files scanned: ${totalFiles}`);
  console.log(`   Files updated: ${updatedFiles}`);
  console.log(`   Files unchanged: ${totalFiles - updatedFiles}`);

  console.log('\n🎯 Terminology Updates Applied:');
  console.log('=====================================');
  console.log('   ✅ Alliance → Studio');
  console.log('   ✅ Venture → Project');
  console.log('   ✅ Quest → Mission');
  console.log('   ✅ UI labels and descriptions');
  console.log('   ✅ Form placeholders and help text');
  console.log('   ✅ Status messages and notifications');

  console.log('\n🔍 Next Steps:');
  console.log('=====================================');
  console.log('1. Review updated files for any context-specific adjustments');
  console.log('2. Test the application to ensure all text displays correctly');
  console.log('3. Check for any remaining old terminology in:');
  console.log('   • Error messages');
  console.log('   • API responses');
  console.log('   • Database comments');
  console.log('   • Documentation files');
  console.log('4. Update any remaining documentation and help content');
}

// Main execution
updateUIText().catch(console.error);

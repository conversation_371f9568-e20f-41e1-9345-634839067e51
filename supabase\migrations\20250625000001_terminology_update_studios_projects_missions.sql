-- Terminology Update Migration: Alliance → Studio, Venture → Project, Quest → Mission
-- Updates all database terminology to use the new simplified naming convention
-- Date: 2025-06-25

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- PHASE 1: UPDATE COLUMN NAMES AND VALUES
-- ============================================================================

-- Update teams table to use studio terminology
DO $$
BEGIN
    -- Handle alliance_type to studio_type migration
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'alliance_type') THEN
        -- If studio_type already exists, copy data from alliance_type and drop alliance_type
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'studio_type') THEN
            -- Copy data from alliance_type to studio_type where studio_type is null
            UPDATE public.teams SET studio_type = alliance_type WHERE studio_type IS NULL AND alliance_type IS NOT NULL;
            -- Drop the old alliance_type column
            ALTER TABLE public.teams DROP COLUMN alliance_type;
        ELSE
            -- If studio_type doesn't exist, rename alliance_type to studio_type
            ALTER TABLE public.teams RENAME COLUMN alliance_type TO studio_type;
        END IF;
    END IF;

    -- Add studio_type column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'studio_type') THEN
        ALTER TABLE public.teams ADD COLUMN studio_type TEXT DEFAULT 'emerging' CHECK (studio_type IN ('emerging', 'established', 'solo'));
    END IF;
END $$;

-- Update projects table to use project terminology (venture_type → project_type)
DO $$
BEGIN
    -- Handle venture_type to project_type migration
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'venture_type') THEN
        -- If project_type already exists, copy data from venture_type and drop venture_type
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'project_type') THEN
            -- Copy data from venture_type to project_type where project_type is null
            UPDATE public.projects SET project_type = venture_type WHERE project_type IS NULL AND venture_type IS NOT NULL;
            -- Drop the old venture_type column
            ALTER TABLE public.projects DROP COLUMN venture_type;
        ELSE
            -- If project_type doesn't exist, rename venture_type to project_type
            ALTER TABLE public.projects RENAME COLUMN venture_type TO project_type;
        END IF;
    END IF;

    -- Add project_type column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'project_type') THEN
        ALTER TABLE public.projects ADD COLUMN project_type TEXT DEFAULT 'software' CHECK (project_type IN ('software', 'game', 'film', 'music', 'art', 'business', 'research', 'other'));
    END IF;

    -- Handle alliance_id to studio_id migration
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'alliance_id') THEN
        -- If studio_id already exists, copy data from alliance_id and drop alliance_id
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'studio_id') THEN
            -- Copy data from alliance_id to studio_id where studio_id is null
            UPDATE public.projects SET studio_id = alliance_id WHERE studio_id IS NULL AND alliance_id IS NOT NULL;
            -- Drop the old alliance_id column
            ALTER TABLE public.projects DROP COLUMN alliance_id;
        ELSE
            -- If studio_id doesn't exist, rename alliance_id to studio_id
            ALTER TABLE public.projects RENAME COLUMN alliance_id TO studio_id;
        END IF;
    END IF;

    -- Add studio_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'studio_id') THEN
        ALTER TABLE public.projects ADD COLUMN studio_id UUID REFERENCES public.teams(id);
    END IF;
END $$;

-- Update tasks table to use mission terminology
DO $$
BEGIN
    -- Update task_category values: quest → mission
    UPDATE public.tasks
    SET task_category = 'mission'
    WHERE task_category = 'quest';

    -- Handle quest_type to mission_type migration
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'quest_type') THEN
        -- If mission_type already exists, copy data from quest_type and drop quest_type
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'mission_type') THEN
            -- Copy data from quest_type to mission_type where mission_type is null
            UPDATE public.tasks SET mission_type = quest_type WHERE mission_type IS NULL AND quest_type IS NOT NULL;
            -- Drop the old quest_type column
            ALTER TABLE public.tasks DROP COLUMN quest_type;
        ELSE
            -- If mission_type doesn't exist, rename quest_type to mission_type
            ALTER TABLE public.tasks RENAME COLUMN quest_type TO mission_type;
        END IF;
    END IF;

    -- Handle quest_requirements to mission_requirements migration
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'quest_requirements') THEN
        -- If mission_requirements already exists, copy data from quest_requirements and drop quest_requirements
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'mission_requirements') THEN
            -- Copy data from quest_requirements to mission_requirements where mission_requirements is null
            UPDATE public.tasks SET mission_requirements = quest_requirements WHERE mission_requirements IS NULL AND quest_requirements IS NOT NULL;
            -- Drop the old quest_requirements column
            ALTER TABLE public.tasks DROP COLUMN quest_requirements;
        ELSE
            -- If mission_requirements doesn't exist, rename quest_requirements to mission_requirements
            ALTER TABLE public.tasks RENAME COLUMN quest_requirements TO mission_requirements;
        END IF;
    END IF;

    -- Handle quest_rewards to mission_rewards migration
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'quest_rewards') THEN
        -- If mission_rewards already exists, copy data from quest_rewards and drop quest_rewards
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'mission_rewards') THEN
            -- Copy data from quest_rewards to mission_rewards where mission_rewards is null
            UPDATE public.tasks SET mission_rewards = quest_rewards WHERE mission_rewards IS NULL AND quest_rewards IS NOT NULL;
            -- Drop the old quest_rewards column
            ALTER TABLE public.tasks DROP COLUMN quest_rewards;
        ELSE
            -- If mission_rewards doesn't exist, rename quest_rewards to mission_rewards
            ALTER TABLE public.tasks RENAME COLUMN quest_rewards TO mission_rewards;
        END IF;
    END IF;

    -- Update task_category constraint to use mission instead of quest
    ALTER TABLE public.tasks DROP CONSTRAINT IF EXISTS tasks_task_category_check;
    ALTER TABLE public.tasks ADD CONSTRAINT tasks_task_category_check
        CHECK (task_category IN ('mission', 'bounty', 'task'));

    -- Update mission_type constraint if column exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'mission_type') THEN
        ALTER TABLE public.tasks DROP CONSTRAINT IF EXISTS tasks_quest_type_check;
        ALTER TABLE public.tasks DROP CONSTRAINT IF EXISTS tasks_mission_type_check;
        ALTER TABLE public.tasks ADD CONSTRAINT tasks_mission_type_check
            CHECK (mission_type IN ('skill', 'collaboration', 'achievement', 'exploration', 'social'));
    END IF;
END $$;

-- ============================================================================
-- PHASE 2: UPDATE TABLE NAMES AND REFERENCES
-- ============================================================================

-- Rename alliance_invitations to studio_invitations
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'alliance_invitations' AND table_schema = 'public') THEN
        -- Rename the table
        ALTER TABLE public.alliance_invitations RENAME TO studio_invitations;
        
        -- Rename the alliance_id column to studio_id
        ALTER TABLE public.studio_invitations RENAME COLUMN alliance_id TO studio_id;
    END IF;
END $$;

-- Rename alliance_preferences to studio_preferences
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'alliance_preferences' AND table_schema = 'public') THEN
        -- Rename the table
        ALTER TABLE public.alliance_preferences RENAME TO studio_preferences;
        
        -- Rename the alliance_id column to studio_id
        ALTER TABLE public.studio_preferences RENAME COLUMN alliance_id TO studio_id;
    END IF;
END $$;

-- Rename user_quests to user_missions
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_quests' AND table_schema = 'public') THEN
        -- Rename the table
        ALTER TABLE public.user_quests RENAME TO user_missions;
        
        -- Rename the quest_id column to mission_id
        ALTER TABLE public.user_missions RENAME COLUMN quest_id TO mission_id;
    END IF;
END $$;

-- ============================================================================
-- PHASE 3: ADD PEOPLE TYPE SYSTEM
-- ============================================================================

-- Add people type system to team_members table
DO $$
BEGIN
    -- Add collaboration_type column for the new people type system
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_members' AND column_name = 'collaboration_type') THEN
        ALTER TABLE public.team_members ADD COLUMN collaboration_type TEXT DEFAULT 'studio_member' 
            CHECK (collaboration_type IN ('studio_member', 'contractor', 'specialist'));
    END IF;
    
    -- Add engagement_duration column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_members' AND column_name = 'engagement_duration') THEN
        ALTER TABLE public.team_members ADD COLUMN engagement_duration TEXT DEFAULT 'permanent'
            CHECK (engagement_duration IN ('permanent', 'project_based', 'one_off'));
    END IF;
    
    -- Add specialization column for specialists
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_members' AND column_name = 'specialization') THEN
        ALTER TABLE public.team_members ADD COLUMN specialization TEXT[];
    END IF;
END $$;

-- Update existing team members with default collaboration types based on their roles
UPDATE public.team_members 
SET collaboration_type = CASE 
    WHEN role IN ('founder', 'owner', 'admin', 'member') THEN 'studio_member'
    WHEN role = 'contributor' THEN 'contractor'
    ELSE 'studio_member'
END
WHERE collaboration_type IS NULL OR collaboration_type = 'studio_member';

-- ============================================================================
-- PHASE 4: UPDATE INDEXES
-- ============================================================================

-- Drop old indexes with alliance/venture/quest terminology
DROP INDEX IF EXISTS idx_teams_alliance_type;
DROP INDEX IF EXISTS idx_projects_alliance_id;
DROP INDEX IF EXISTS idx_projects_venture_type;
DROP INDEX IF EXISTS idx_tasks_quest_type;
DROP INDEX IF EXISTS idx_alliance_invitations_alliance;
DROP INDEX IF EXISTS idx_alliance_invitations_alliance_id;
DROP INDEX IF EXISTS idx_user_quests_quest_id;

-- Create new indexes with studio/project/mission terminology
CREATE INDEX IF NOT EXISTS idx_teams_studio_type ON public.teams(studio_type);
CREATE INDEX IF NOT EXISTS idx_projects_studio_id ON public.projects(studio_id);
CREATE INDEX IF NOT EXISTS idx_projects_project_type ON public.projects(project_type);
CREATE INDEX IF NOT EXISTS idx_tasks_mission_type ON public.tasks(mission_type) WHERE mission_type IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_team_members_collaboration_type ON public.team_members(collaboration_type);

-- Update table-specific indexes if tables were renamed
CREATE INDEX IF NOT EXISTS idx_studio_invitations_studio_id ON public.studio_invitations(studio_id);
CREATE INDEX IF NOT EXISTS idx_user_missions_mission_id ON public.user_missions(mission_id);

-- ============================================================================
-- PHASE 5: UPDATE COMMENTS AND DOCUMENTATION
-- ============================================================================

-- Update table comments
COMMENT ON TABLE public.studio_invitations IS 'Invitations to join studios with role-based access';
COMMENT ON TABLE public.studio_preferences IS 'Studio-specific configuration and preferences';
COMMENT ON TABLE public.user_missions IS 'User mission progress tracking for gamified collaboration';

-- Update column comments
COMMENT ON COLUMN public.teams.studio_type IS 'Type of studio: emerging, established, solo';
COMMENT ON COLUMN public.projects.project_type IS 'Type of project: software, game, film, music, art, business, research, other';
COMMENT ON COLUMN public.tasks.task_category IS 'Category: mission (internal), bounty (public), task (simple)';
COMMENT ON COLUMN public.tasks.mission_type IS 'Type of mission: skill, collaboration, achievement, exploration, social';
COMMENT ON COLUMN public.tasks.mission_requirements IS 'JSON object defining mission completion requirements';
COMMENT ON COLUMN public.tasks.mission_rewards IS 'JSON object defining mission completion rewards';
COMMENT ON COLUMN public.team_members.collaboration_type IS 'Type of collaboration: studio_member, contractor, specialist';
COMMENT ON COLUMN public.team_members.engagement_duration IS 'Duration of engagement: permanent, project_based, one_off';
COMMENT ON COLUMN public.team_members.specialization IS 'Array of specializations for specialists';

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify studio system updates
SELECT 'Studio system updated' as status,
       COUNT(*) as studio_count
FROM public.teams
WHERE studio_type IS NOT NULL;

-- Verify project system updates  
SELECT 'Project system updated' as status,
       COUNT(*) as project_count
FROM public.projects
WHERE project_type IS NOT NULL;

-- Verify mission system updates
SELECT 'Mission system updated' as status,
       COUNT(*) as mission_count
FROM public.tasks
WHERE task_category = 'mission';

-- Verify people type system
SELECT 'People type system ready' as status,
       collaboration_type,
       COUNT(*) as member_count
FROM public.team_members
GROUP BY collaboration_type;

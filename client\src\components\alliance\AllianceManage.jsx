import React, { useState, useEffect, useContext } from 'react';
import { use<PERSON>arams, Link, useNavigate } from 'react-router-dom';
import { Card, CardBody, CardHeader, Button, Chip, Tabs, Tab, Input, Select, SelectItem } from '@heroui/react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';

// Import studio components
import AlliancePermissions from './AlliancePermissions';
import CompanyRegistration from './CompanyRegistration';
import CompanyDetails from './CompanyDetails';
import MemberRoleManager from './MemberRoleManager';
import BusinessModelConfig from './BusinessModelConfig';
import RevenueSharing from './RevenueSharing';
import AllianceSettings from './AllianceSettings';
import './studio-styles.css';

/**
 * AllianceManage component - Enhanced team management with business entity support
 * Maintains gamified theming while adding compliance features
 */
const AllianceManage = () => {
  const { teamId } = useParams(); // Keep teamId for backward compatibility
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();

  // Studio state (formerly team)
  const [studio, setAlliance] = useState(null);
  const [members, setMembers] = useState([]);
  const [invitations, setInvitations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isFounder, setIsFounder] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [userRole, setUserRole] = useState('member');

  // Company/Business entity state
  const [company, setCompany] = useState(null);
  const [showCompanyRegistration, setShowCompanyRegistration] = useState(false);
  const [studioType, setAllianceType] = useState('emerging'); // emerging, established, solo

  const [studioData, setStudioData] = useState({
    name: '',
    description: '',
    studio_type: 'emerging',
    is_business_entity: false
  });

  useEffect(() => {
    if (teamId && currentUser) {
      fetchAllianceData();
    }
  }, [teamId, currentUser]);

  const fetchAllianceData = async () => {
    try {
      setLoading(true);

      // Fetch studio (team) details with company info
      const { data: studioData, error: allianceError } = await supabase
        .from('teams')
        .select(`
          *,
          company:company_id(*)
        `)
        .eq('id', teamId)
        .single();

      if (allianceError) throw allianceError;

      setAlliance(studioData);
      setCompany(studioData.company);
      setAllianceType(studioData.studio_type || 'emerging');

      setStudioData({
        name: studioData.name,
        description: studioData.description || '',
        studio_type: studioData.studio_type || 'emerging',
        is_business_entity: studioData.is_business_entity || false
      });

      // Fetch studio members (team members)
      const { data: membersData, error: membersError } = await supabase
        .from('team_members')
        .select(`
          id,
          role,
          is_admin,
          joined_at,
          user:user_id(id, email, user_metadata)
        `)
        .eq('team_id', teamId);

      if (membersError) throw membersError;

      const processedMembers = membersData.map(member => ({
        id: member.id,
        userId: member.user.id,
        email: member.user.email,
        displayName: member.user.user_metadata?.full_name || member.user.email,
        role: member.role === 'owner' ? 'founder' : member.role, // Gamify: owner -> founder
        isAdmin: member.is_admin,
        joinedAt: member.joined_at
      }));

      setMembers(processedMembers);

      // Check current user's role
      const currentMember = processedMembers.find(member => member.userId === currentUser.id);
      if (currentMember) {
        setIsAdmin(currentMember.isAdmin);
        setIsFounder(currentMember.role === 'founder');
        setUserRole(currentMember.role || 'member');
      } else {
        toast.error('You do not have permission to manage this studio');
        navigate(`/studios/${teamId}`);
      }

      // Fetch invitations
      const { data: invitationsData, error: invitationsError } = await supabase
        .from('team_invitations')
        .select('*')
        .eq('team_id', teamId)
        .order('created_at', { ascending: false });

      if (invitationsError) throw invitationsError;
      setInvitations(invitationsData);

    } catch (error) {
      console.error('Error fetching studio data:', error);
      toast.error('Failed to load studio data');
      navigate('/studios');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateAlliance = async (e) => {
    e.preventDefault();

    if (!studioData.name.trim()) {
      toast.error('Studio name is required');
      return;
    }

    try {
      setLoading(true);

      const { error } = await supabase
        .from('teams')
        .update({
          name: studioData.name,
          description: studioData.description,
          studio_type: studioData.studio_type,
          is_business_entity: studioData.is_business_entity,
          updated_at: new Date()
        })
        .eq('id', teamId);

      if (error) throw error;

      toast.success('Studio updated successfully');
      setEditMode(false);
      fetchAllianceData();
    } catch (error) {
      console.error('Error updating studio:', error);
      toast.error('Failed to update studio');
    } finally {
      setLoading(false);
    }
  };

  const handleCompanyRegistered = (newCompany) => {
    setCompany(newCompany);
    setShowCompanyRegistration(false);
    setStudioData(prev => ({ ...prev, is_business_entity: true }));
    toast.success('🏰 Studio elevated to Established status!');
    fetchAllianceData();
  };

  const getAllianceTypeInfo = (type) => {
    const types = {
      emerging: {
        icon: '🌱',
        title: 'Emerging Studio',
        description: 'Growing creative studio'
      },
      established: {
        icon: '🏰',
        title: 'Established Studio',
        description: 'Established creative business'
      },
      solo: {
        icon: '⚔️',
        title: 'Solo Studio',
        description: 'Independent creator'
      }
    };
    return types[type] || types.emerging;
  };

  if (loading && !studio) {
    return (
      <Card className="w-full">
        <CardBody className="p-6 text-center">
          <div className="animate-spin text-2xl mb-2">🔄</div>
          <div>Loading studio management...</div>
        </CardBody>
      </Card>
    );
  }

  const allianceTypeInfo = getAllianceTypeInfo(studioData.studio_type);

  return (
    <div className="studio-manage-container space-y-6">
      {/* Enhanced Header */}
      <Card className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <span className="text-3xl">⚔️</span>
              <div>
                <h2 className="text-2xl font-bold">Studio Management</h2>
                <p className="text-default-600">Comprehensive studio administration and configuration</p>
              </div>
            </div>
            <Button
              as={Link}
              to={`/studios/${teamId}`}
              variant="bordered"
              startContent="←"
            >
              Back to Studio
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Enhanced Tabbed Interface */}
      <Card>
        <CardBody className="p-0">
          <Tabs
            selectedKey={activeTab}
            onSelectionChange={setActiveTab}
            variant="underlined"
            classNames={{
              tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
              cursor: "w-full bg-primary",
              tab: "max-w-fit px-4 h-12",
              tabContent: "group-data-[selected=true]:text-primary"
            }}
          >
            <Tab key="overview" title={
              <div className="flex items-center space-x-2">
                <span>🏰</span>
                <span>Overview</span>
              </div>
            }>
              {/* Studio Information Section */}
              <div className="p-6 space-y-6">
                <Card className="border border-default-200">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2">
                        <span className="text-xl">{allianceTypeInfo.icon}</span>
                        <h3 className="text-lg font-semibold">Studio Information</h3>
                      </div>
                      {!editMode && (
                        <Button
                          size="sm"
                          variant="bordered"
                          onPress={() => setEditMode(true)}
                        >
                          Edit
                        </Button>
                      )}
                    </div>
                  </CardHeader>

                  <CardBody className="pt-0">
                    {editMode ? (
                      <form onSubmit={handleUpdateAlliance} className="space-y-4">
                        <Input
                          label="Studio Name"
                          value={studioData.name}
                          onChange={(e) => setStudioData({ ...studioData, name: e.target.value })}
                          placeholder="Enter studio name"
                          isRequired
                        />

                        <Select
                          label="Studio Type"
                          selectedKeys={[studioData.studio_type]}
                          onSelectionChange={(keys) => setStudioData({
                            ...studioData,
                            studio_type: Array.from(keys)[0]
                          })}
                        >
                          <SelectItem key="emerging">🌱 Emerging Studio</SelectItem>
                          <SelectItem key="established">🏰 Established Studio</SelectItem>
                          <SelectItem key="solo">⚔️ Solo Studio</SelectItem>
                        </Select>

                        <Input
                          label="Description (optional)"
                          value={studioData.description}
                          onChange={(e) => setStudioData({ ...studioData, description: e.target.value })}
                          placeholder="Describe your studio's mission"
                        />

                        <div className="flex gap-2">
                          <Button type="submit" color="primary">Save Changes</Button>
                          <Button
                            variant="bordered"
                            onPress={() => {
                              setEditMode(false);
                              setStudioData({
                                name: studio.name,
                                description: studio.description || '',
                                studio_type: studio.studio_type || 'emerging',
                                is_business_entity: studio.is_business_entity || false
                              });
                            }}
                          >
                            Cancel
                          </Button>
                        </div>
                      </form>
                    ) : (
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-default-600">Name:</span>
                          <span className="font-medium">{studio?.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-default-600">Type:</span>
                          <span className="font-medium">
                            {allianceTypeInfo.icon} {allianceTypeInfo.title}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-default-600">Description:</span>
                          <span className="font-medium">{studio?.description || 'No description'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-default-600">Founded:</span>
                          <span className="font-medium">{new Date(studio?.created_at).toLocaleDateString()}</span>
                        </div>
                      </div>
                    )}
                  </CardBody>
                </Card>

                {/* Business Entity Section */}
                {(studioData.studio_type === 'established' || company) && (
                  <Card className="border border-default-200">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-2">
                          <span className="text-xl">🏢</span>
                          <h3 className="text-lg font-semibold">Business Entity</h3>
                        </div>
                        {!company && (
                          <Button
                            size="sm"
                            color="primary"
                            onPress={() => setShowCompanyRegistration(true)}
                          >
                            Register Business
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardBody className="pt-0">
                      {company ? (
                        <CompanyDetails
                          company={company}
                          onUpdate={() => fetchAllianceData()}
                          canEdit={isFounder || isAdmin}
                        />
                      ) : showCompanyRegistration ? (
                        <CompanyRegistration
                          studioId={teamId}
                          onCompanyRegistered={handleCompanyRegistered}
                          onCancel={() => setShowCompanyRegistration(false)}
                        />
                      ) : (
                        <div className="text-center py-6">
                          <span className="text-4xl mb-4 block">🏰</span>
                          <h4 className="text-lg font-medium mb-2">Elevate to Established Status</h4>
                          <p className="text-default-600 mb-4">
                            Register as a business entity to unlock advanced features
                          </p>
                          <div className="grid grid-cols-2 gap-2 text-sm text-default-600">
                            <div>💰 Tax-compliant payments</div>
                            <div>📊 Professional reporting</div>
                            <div>🤝 Business contracts</div>
                            <div>🛡️ Legal protection</div>
                          </div>
                        </div>
                      )}
                    </CardBody>
                  </Card>
                )}

                {/* Studio Permissions Section */}
                <AlliancePermissions
                  studioId={teamId}
                  isFounder={isFounder}
                  hasBusinessEntity={!!company}
                  onPermissionsUpdated={fetchAllianceData}
                />
              </div>
            </Tab>

            <Tab key="members" title={
              <div className="flex items-center space-x-2">
                <span>👥</span>
                <span>Members</span>
              </div>
            }>
              <div className="p-6">
                <MemberRoleManager
                  studioId={teamId}
                  userRole={userRole}
                />
              </div>
            </Tab>

            <Tab key="business" title={
              <div className="flex items-center space-x-2">
                <span>💼</span>
                <span>Business Model</span>
              </div>
            }>
              <div className="p-6">
                <BusinessModelConfig
                  studioId={teamId}
                  userRole={userRole}
                />
              </div>
            </Tab>

            <Tab key="revenue" title={
              <div className="flex items-center space-x-2">
                <span>💰</span>
                <span>Revenue Sharing</span>
              </div>
            }>
              <div className="p-6">
                <RevenueSharing
                  studioId={teamId}
                  userRole={userRole}
                />
              </div>
            </Tab>

            <Tab key="settings" title={
              <div className="flex items-center space-x-2">
                <span>⚙️</span>
                <span>Settings</span>
              </div>
            }>
              <div className="p-6">
                <AllianceSettings
                  studioId={teamId}
                  userRole={userRole}
                />
              </div>
            </Tab>
          </Tabs>
        </CardBody>
      </Card>
    </div>
  );
};

export default AllianceManage;

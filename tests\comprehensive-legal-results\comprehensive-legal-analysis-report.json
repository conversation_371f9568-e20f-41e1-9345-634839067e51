{"timestamp": "2025-06-23T07:39:42.807Z", "testType": "Comprehensive Legal Agreement Analysis", "summary": {"overallScore": 80, "criticalIssuesCount": 3, "financialAccuracy": 100, "crossContaminationScore": 100, "productionReady": false}, "detailed": {"scenarios": {"VOTA_EXACT": {"categories": {"HEADER": {"found": ["CONTRIBUTOR AGREEMENT", "This Contributor Agreement (this \"Agreement\")"], "missing": ["CITY OF GAMERS INC.", "Florida corporation", "1205 43rd Street, Suite B, Orlando, Florida 32839"], "accuracy": 40}, "DEFINITIONS": {"found": ["Background IP", "Confidential Documents", "Confidential Information", "Contribution", "Developed IP", "Governmental Authority", "Launch", "Milestones", "Person", "Programs", "Specification", "Termination Date", "Work Product", "Work Product Management", "Revenue Tranch", "Contribution Points"], "missing": [], "accuracy": 100}, "PROJECT_CONTENT": {"found": ["Village of The Ages", "village simulation game", "historical progressions", "resource-based challenges", "Village Building & Management", "Historical Progression", "Resource Management", "Interface Requirements", "Technology tree advancement", "Architectural evolution", "Dynamic weather systems", "Natural disasters and seasonal challenges", "Trading systems with neighboring villages", "Resource scarcity mechanics", "Intuitive building placement system", "Resource management dashboard", "Population statistics and management panels", "Technology and progression trackers"], "missing": [], "accuracy": 100}, "FINANCIAL_TERMS": {"found": ["SCHEDULE B", "Description of Consideration", "Contribution Points", "Revenue Tranch", "Revenue", "Revenue Share Percentage", "Minimum Threshold for Payout", "Maximum Individual Payment", "Payment Schedule", "Quarterly reports", "Revenue Tranch Parameters", "Contribution Point System", "Variable Costs", "Payments on Termination", "<PERSON><PERSON>", "Audit Rights"], "missing": [], "accuracy": 100}, "FINANCIAL_DETAILS": {"found": ["33% of post-expense Revenue", "$100,000 in post-expense Revenue", "$1,000,000 per Dev<PERSON>per", "quarterly within 45 days", "Platform fees", "Payment processing fees", "Marketing expenses", "Third-party licensing fees"], "missing": [], "accuracy": 100}, "LEGAL_CLAUSES": {"found": ["Treatment of Confidential Information", "Ownership of Work Product", "Non-Disparagement", "Termination", "Equitable Remedies", "Assignment", "Waivers and Amendments", "Survival", "Status as Independent Contractor", "Representations and Warranties", "Indemnification", "Entire Agreement", "Governing Law", "Consent to Juris<PERSON>", "Settlement of Disputes"], "missing": [], "accuracy": 100}, "EXHIBITS": {"found": ["SCHEDULE A", "Description of Services", "EXHIBIT I", "SPECIFICATIONS", "EXHIBIT II", "PRODUCT ROADMAP"], "missing": [], "accuracy": 100}, "MILESTONES": {"found": ["Core Gameplay Development", "Resource Management System", "Historical Progression Features", "Basic village layout and building system", "Implementation of resource scarcity mechanics", "Time-based progression and historical events"], "missing": [], "accuracy": 100}, "TECHNICAL": {"found": ["Platform: PC (Steam, Epic Games Store)", "Engine: Unreal Engine 5", "Minimum Specs: Standard hardware requirements", "Art Style: Stylized, readable visuals", "Audio: Atmospheric soundtrack", "Version Control: Git-based source control"], "missing": [], "accuracy": 100}}, "overallAccuracy": 97, "totalElements": 96, "foundElements": 93, "missingElements": ["HEADER: CITY OF GAMERS INC.", "HEADER: Florida corporation", "HEADER: 1205 43rd Street, Suite B, Orlando, Florida 32839"], "criticalIssues": [], "financialConsistency": {"isConsistent": true, "issues": []}, "outputFile": "C:\\Data\\Projects\\Royaltea\\tests\\comprehensive-legal-results\\vota_exact-agreement.md", "scenarioData": {"name": "Village of The Ages", "description": "A village simulation game where players guide communities through historical progressions and manage resource-based challenges", "project_type": "game", "company_name": "City of Gamers Inc.", "address": "1205 43rd Street, Suite B, Orlando, Florida 32839", "contact_email": "<EMAIL>", "revenueShare": 33, "payoutThreshold": 100000, "maxPayment": 1000000, "features": "The game features dynamic challenges based on resource scarcity and natural disasters, creating an engaging gameplay loop.", "coreFeatures": "1. **Village Building & Management**\n   - Resource gathering and management\n   - Building placement and construction\n   - Population growth and management\n   - Cultural evolution systems\n\n2. **Historical Progression**\n   - Players guide their community through multiple historical eras\n   - Technology tree advancement\n   - Cultural and social development\n   - Architectural evolution\n\n3. **Resource Management**\n   - Dynamic weather systems affecting resources\n   - Natural disasters and seasonal challenges\n   - Trading systems with neighboring villages\n   - Resource scarcity mechanics\n\n4. **Interface Requirements**\n   - Intuitive building placement system\n   - Resource management dashboard\n   - Population statistics and management panels\n   - Technology and progression trackers", "technicalRequirements": "- Platform: PC (Steam, Epic Games Store)\n- Engine: Unreal Engine 5\n- Minimum Specs: Standard hardware requirements for the target platforms\n- Art Style: Stylized, readable visuals with distinctive aesthetics\n- Audio: Atmospheric soundtrack with appropriate sound design\n- Version Control: Git-based source control with proper branching strategy", "roadmapPhases": "**Phase 1: Core Gameplay Development (Months 1-2)**\n- Basic village layout and building system\n- Core resource gathering mechanics\n- Initial AI for villagers\n- Basic UI framework\n- First playable prototype with one historical era\n\n**Phase 2: Feature Expansion (Months 2-3)**\n- Additional historical eras\n- Enhanced resource management systems\n- Weather and disaster systems\n- Trading mechanics\n- Technology progression system\n\n**Phase 3: Polish and Enhancement (Month 4)**\n- UI refinement\n- Performance optimization\n- Additional content (buildings, resources, etc.)\n- Balancing and gameplay tuning\n- Audio implementation", "milestones": [{"title": "Core Gameplay Development", "description": "Basic village layout and building system", "dueDate": "Months 1-2"}, {"title": "Resource Management System", "description": "Implementation of resource scarcity mechanics", "dueDate": "Months 3-4"}, {"title": "Historical Progression Features", "description": "Time-based progression and historical events, architectural evolution through eras", "dueDate": "Months 5-6"}]}}, "SOFTWARE_SAAS": {"categories": {"HEADER": {"found": ["CONTRIBUTOR AGREEMENT", "This Contributor Agreement (this \"Agreement\")"], "missing": ["CITY OF GAMERS INC.", "Florida corporation", "1205 43rd Street, Suite B, Orlando, Florida 32839"], "accuracy": 40}, "DEFINITIONS": {"found": ["Background IP", "Confidential Documents", "Confidential Information", "Contribution", "Developed IP", "Governmental Authority", "Launch", "Milestones", "Person", "Programs", "Specification", "Termination Date", "Work Product", "Work Product Management", "Revenue Tranch", "Contribution Points"], "missing": [], "accuracy": 100}, "PROJECT_CONTENT": {"found": [], "missing": ["Village of The Ages", "village simulation game", "historical progressions", "resource-based challenges", "Village Building & Management", "Historical Progression", "Resource Management", "Interface Requirements", "Technology tree advancement", "Architectural evolution", "Dynamic weather systems", "Natural disasters and seasonal challenges", "Trading systems with neighboring villages", "Resource scarcity mechanics", "Intuitive building placement system", "Resource management dashboard", "Population statistics and management panels", "Technology and progression trackers"], "accuracy": 0}, "FINANCIAL_TERMS": {"found": ["SCHEDULE B", "Description of Consideration", "Contribution Points", "Revenue Tranch", "Revenue", "Revenue Share Percentage", "Minimum Threshold for Payout", "Maximum Individual Payment", "Payment Schedule", "Quarterly reports", "Revenue Tranch Parameters", "Contribution Point System", "Variable Costs", "Payments on Termination", "<PERSON><PERSON>", "Audit Rights"], "missing": [], "accuracy": 100}, "FINANCIAL_DETAILS": {"found": ["quarterly within 45 days", "Platform fees", "Payment processing fees", "Marketing expenses", "Third-party licensing fees"], "missing": ["33% of post-expense Revenue", "$100,000 in post-expense Revenue", "$1,000,000 per Dev<PERSON>per"], "accuracy": 63}, "LEGAL_CLAUSES": {"found": ["Treatment of Confidential Information", "Ownership of Work Product", "Non-Disparagement", "Termination", "Equitable Remedies", "Assignment", "Waivers and Amendments", "Survival", "Status as Independent Contractor", "Representations and Warranties", "Indemnification", "Entire Agreement", "Governing Law", "Consent to Juris<PERSON>", "Settlement of Disputes"], "missing": [], "accuracy": 100}, "EXHIBITS": {"found": ["SCHEDULE A", "Description of Services", "EXHIBIT I", "SPECIFICATIONS", "EXHIBIT II", "PRODUCT ROADMAP"], "missing": [], "accuracy": 100}, "MILESTONES": {"found": [], "missing": ["Core Gameplay Development", "Resource Management System", "Historical Progression Features", "Basic village layout and building system", "Implementation of resource scarcity mechanics", "Time-based progression and historical events"], "accuracy": 0}, "TECHNICAL": {"found": [], "missing": ["Platform: PC (Steam, Epic Games Store)", "Engine: Unreal Engine 5", "Minimum Specs: Standard hardware requirements", "Art Style: Stylized, readable visuals", "Audio: Atmospheric soundtrack", "Version Control: Git-based source control"], "accuracy": 0}}, "overallAccuracy": 63, "totalElements": 96, "foundElements": 60, "missingElements": ["HEADER: CITY OF GAMERS INC.", "HEADER: Florida corporation", "HEADER: 1205 43rd Street, Suite B, Orlando, Florida 32839", "PROJECT_CONTENT: Village of The Ages", "PROJECT_CONTENT: village simulation game", "PROJECT_CONTENT: historical progressions", "PROJECT_CONTENT: resource-based challenges", "PROJECT_CONTENT: Village Building & Management", "PROJECT_CONTENT: Historical Progression", "PROJECT_CONTENT: Resource Management", "PROJECT_CONTENT: Interface Requirements", "PROJECT_CONTENT: Technology tree advancement", "PROJECT_CONTENT: Architectural evolution", "PROJECT_CONTENT: Dynamic weather systems", "PROJECT_CONTENT: Natural disasters and seasonal challenges", "PROJECT_CONTENT: Trading systems with neighboring villages", "PROJECT_CONTENT: Resource scarcity mechanics", "PROJECT_CONTENT: Intuitive building placement system", "PROJECT_CONTENT: Resource management dashboard", "PROJECT_CONTENT: Population statistics and management panels", "PROJECT_CONTENT: Technology and progression trackers", "FINANCIAL_DETAILS: 33% of post-expense Revenue", "FINANCIAL_DETAILS: $100,000 in post-expense Revenue", "FINANCIAL_DETAILS: $1,000,000 per Developer", "MILESTONES: Core Gameplay Development", "MILESTONES: Resource Management System", "MILESTONES: Historical Progression Features", "MILESTONES: Basic village layout and building system", "MILESTONES: Implementation of resource scarcity mechanics", "MILESTONES: Time-based progression and historical events", "TECHNICAL: Platform: PC (Steam, Epic Games Store)", "TECHNICAL: Engine: Unreal Engine 5", "TECHNICAL: Minimum Specs: Standard hardware requirements", "TECHNICAL: Art Style: Stylized, readable visuals", "TECHNICAL: Audio: Atmospheric soundtrack", "TECHNICAL: Version Control: Git-based source control"], "criticalIssues": ["Missing critical financial_details: 33% of post-expense Revenue", "Missing critical financial_details: $100,000 in post-expense Revenue", "Missing critical financial_details: $1,000,000 per Developer"], "financialConsistency": {"isConsistent": true, "issues": []}, "outputFile": "C:\\Data\\Projects\\Royaltea\\tests\\comprehensive-legal-results\\software_saas-agreement.md", "scenarioData": {"name": "CloudFlow Enterprise", "description": "A comprehensive enterprise workflow automation platform for large organizations", "project_type": "software", "company_name": "Enterprise Solutions Corp", "address": "500 Corporate Plaza, San Francisco, CA 94105", "contact_email": "<EMAIL>", "revenueShare": 25, "payoutThreshold": 250000, "maxPayment": 2000000, "features": "Advanced enterprise-grade workflow automation with AI-powered process optimization and compliance tracking.", "coreFeatures": "1. **Workflow Automation**\n   - Visual workflow designer\n   - Automated task routing\n   - Conditional logic processing\n   - Integration with enterprise systems\n\n2. **AI-Powered Optimization**\n   - Process efficiency analysis\n   - Bottleneck identification\n   - Predictive resource allocation\n   - Performance recommendations\n\n3. **Compliance & Security**\n   - Audit trail management\n   - Role-based access control\n   - Data encryption and privacy\n   - Regulatory compliance reporting", "technicalRequirements": "- Platform: Cloud-native SaaS (AWS/Azure)\n- Architecture: Microservices with Kubernetes\n- Database: PostgreSQL with Redis caching\n- AI/ML: TensorFlow and custom ML models\n- Security: SOC 2 Type II compliance\n- API: RESTful and GraphQL endpoints", "roadmapPhases": "**Phase 1: Core Platform (Months 1-4)**\n- User management and authentication\n- Basic workflow designer\n- Core automation engine\n- Initial integrations\n\n**Phase 2: AI Integration (Months 5-8)**\n- Machine learning model development\n- Process optimization algorithms\n- Predictive analytics dashboard\n- Advanced reporting features\n\n**Phase 3: Enterprise Features (Months 9-12)**\n- Advanced security features\n- Compliance reporting\n- Enterprise integrations\n- Performance optimization", "milestones": [{"title": "Core Platform MVP", "description": "Basic workflow automation and user management", "dueDate": "Month 4"}, {"title": "AI-Powered Features", "description": "Machine learning integration and process optimization", "dueDate": "Month 8"}, {"title": "Enterprise Ready", "description": "Full compliance, security, and enterprise integrations", "dueDate": "Month 12"}]}}}, "crossContamination": {"totalContamination": 0, "highSeverityCount": 0, "details": []}}, "recommendations": [{"priority": "CRITICAL", "category": "Overall Accuracy", "issue": "Overall accuracy is 80%, below required 99%", "action": "Review template variable mapping and ensure all critical sections are properly substituted"}, {"priority": "CRITICAL", "category": "Critical Elements", "issue": "3 critical elements missing", "action": "Implement missing legal clauses and financial terms"}]}
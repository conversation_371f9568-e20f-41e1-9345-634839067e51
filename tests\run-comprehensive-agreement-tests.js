#!/usr/bin/env node

/**
 * Test Runner for Comprehensive Agreement E2E Testing
 * 
 * This script runs the comprehensive agreement testing suite and provides
 * detailed reporting on the platform's ability to:
 * 1. Recreate lawyer-approved templates with 99% accuracy
 * 2. Generate variable-based agreements for various business types
 * 3. Prevent cross-contamination between different projects
 */

import { ComprehensiveAgreementTester, TEST_CONFIG } from './comprehensive-agreement-e2e-test.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Main test execution function
 */
async function runComprehensiveTests() {
  console.log('🚀 STARTING COMPREHENSIVE AGREEMENT TESTING SUITE');
  console.log('=' .repeat(70));
  console.log('This test suite validates the Royaltea legal agreement generation system');
  console.log('Target: 99% minimum accuracy for 1:1 lawyer-approved template recreation');
  console.log('=' .repeat(70));
  
  const startTime = Date.now();
  
  try {
    // Initialize the test framework
    const tester = new ComprehensiveAgreementTester();
    
    // Run all comprehensive tests
    const results = await tester.runAllTests();
    
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    
    // Display final summary
    console.log('\n🎯 COMPREHENSIVE TEST SUITE COMPLETED');
    console.log('=' .repeat(50));
    console.log(`⏱️  Total execution time: ${duration} seconds`);
    console.log(`📊 Overall score: ${results.summary.overallScore}%`);
    console.log(`✅ Tests passed: ${results.summary.passedTests}/${results.summary.totalTests}`);
    console.log(`🎯 1:1 Recreation: ${results.tests['1to1_recreation']?.accuracy || 0}% (Required: ${TEST_CONFIG.MINIMUM_1TO1_ACCURACY}%)`);
    console.log(`🚫 Cross-contamination: ${results.tests['cross_contamination']?.contamination || 0} instances`);
    console.log(`🏭 Production ready: ${results.summary.productionReady ? 'YES' : 'NO'}`);
    
    // Display recommendations if any
    if (results.recommendations && results.recommendations.length > 0) {
      console.log('\n📋 RECOMMENDATIONS FOR IMPROVEMENT');
      console.log('=' .repeat(40));
      
      results.recommendations.forEach((rec, index) => {
        console.log(`\n${index + 1}. [${rec.priority}] ${rec.category}`);
        console.log(`   Issue: ${rec.issue}`);
        console.log(`   Recommendation: ${rec.recommendation}`);
        
        if (rec.actionItems && rec.actionItems.length > 0) {
          console.log('   Action Items:');
          rec.actionItems.forEach(item => {
            console.log(`   • ${item}`);
          });
        }
      });
    }
    
    // Final assessment
    console.log('\n🎯 FINAL ASSESSMENT');
    console.log('=' .repeat(30));
    
    if (results.summary.productionReady) {
      console.log('🎉 EXCELLENT: System meets all production requirements!');
      console.log('✅ Platform can recreate lawyer-approved templates accurately');
      console.log('✅ Variable-based generation works correctly');
      console.log('✅ No cross-contamination detected');
      console.log('✅ Ready for production use');
    } else if (results.summary.overallScore >= 85) {
      console.log('👍 GOOD: System mostly works but needs improvement');
      console.log(`🔧 Need ${100 - results.summary.overallScore}% improvement for production readiness`);
      
      if (!results.summary.meets1to1Requirement) {
        console.log('❌ 1:1 template recreation below required threshold');
      }
      if (!results.summary.meetsContaminationRequirement) {
        console.log('❌ Cross-contamination issues detected');
      }
    } else {
      console.log('❌ POOR: System has significant issues requiring attention');
      console.log(`🔧 Need ${100 - results.summary.overallScore}% improvement`);
      console.log('⚠️  Not ready for production use');
    }
    
    // Output file locations
    console.log('\n📁 OUTPUT FILES');
    console.log('=' .repeat(20));
    console.log(`📊 Comprehensive report: ${path.join(TEST_CONFIG.OUTPUT_DIR, TEST_CONFIG.REPORT_FILE)}`);
    console.log(`📄 Generated agreements: ${TEST_CONFIG.OUTPUT_DIR}/`);
    
    // Exit with appropriate code
    const exitCode = results.summary.productionReady ? 0 : 1;
    console.log(`\n${exitCode === 0 ? '✅' : '❌'} Test suite ${exitCode === 0 ? 'PASSED' : 'FAILED'}`);
    
    return {
      success: exitCode === 0,
      results: results,
      exitCode: exitCode
    };
    
  } catch (error) {
    console.error('\n❌ COMPREHENSIVE TEST SUITE FAILED');
    console.error('Error:', error.message);
    console.error('\nStack trace:', error.stack);
    
    return {
      success: false,
      error: error.message,
      exitCode: 1
    };
  }
}

/**
 * CLI argument parsing and execution
 */
async function main() {
  const args = process.argv.slice(2);
  
  // Check for help flag
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Comprehensive Agreement Testing Suite

Usage: node run-comprehensive-agreement-tests.js [options]

Options:
  --help, -h          Show this help message
  --verbose, -v       Enable verbose output
  --1to1-only         Run only 1:1 template recreation tests
  --variable-only     Run only variable-based generation tests
  --contamination-only Run only cross-contamination tests
  --business-only     Run only business scenario tests

Examples:
  node run-comprehensive-agreement-tests.js
  node run-comprehensive-agreement-tests.js --verbose
  node run-comprehensive-agreement-tests.js --1to1-only
    `);
    process.exit(0);
  }
  
  // Configure test options based on CLI arguments
  if (args.includes('--1to1-only')) {
    TEST_CONFIG.ENABLE_1TO1_RECREATION = true;
    TEST_CONFIG.ENABLE_VARIABLE_TESTING = false;
    TEST_CONFIG.ENABLE_CONTAMINATION_TESTING = false;
    TEST_CONFIG.ENABLE_BUSINESS_SCENARIOS = false;
  } else if (args.includes('--variable-only')) {
    TEST_CONFIG.ENABLE_1TO1_RECREATION = false;
    TEST_CONFIG.ENABLE_VARIABLE_TESTING = true;
    TEST_CONFIG.ENABLE_CONTAMINATION_TESTING = false;
    TEST_CONFIG.ENABLE_BUSINESS_SCENARIOS = false;
  } else if (args.includes('--contamination-only')) {
    TEST_CONFIG.ENABLE_1TO1_RECREATION = false;
    TEST_CONFIG.ENABLE_VARIABLE_TESTING = false;
    TEST_CONFIG.ENABLE_CONTAMINATION_TESTING = true;
    TEST_CONFIG.ENABLE_BUSINESS_SCENARIOS = false;
  } else if (args.includes('--business-only')) {
    TEST_CONFIG.ENABLE_1TO1_RECREATION = false;
    TEST_CONFIG.ENABLE_VARIABLE_TESTING = false;
    TEST_CONFIG.ENABLE_CONTAMINATION_TESTING = false;
    TEST_CONFIG.ENABLE_BUSINESS_SCENARIOS = true;
  }
  
  // Run the comprehensive tests
  const result = await runComprehensiveTests();
  
  // Exit with appropriate code
  process.exit(result.exitCode);
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

// Export for programmatic use
export { runComprehensiveTests };

import React, { useState } from 'react';
import { Dropdown, DropdownTrigger, DropdownMenu, DropdownItem, Button, Avatar, Chip, Divider } from '@heroui/react';
import { motion } from 'framer-motion';

/**
 * Studio Selector Component - Studio Switching and Selection Interface
 * 
 * Features:
 * - Quick studio switching with dropdown interface
 * - Studio overview with key metrics
 * - Role indicators and permissions display
 * - Create new studio option
 * - Recent studio access tracking
 */
const AllianceSelector = ({ 
  currentAlliance, 
  userAlliances = [], 
  onAllianceChange, 
  onCreateAlliance,
  className = "" 
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // Get studio type icon
  const getTypeIcon = (type) => {
    const icons = {
      'emerging': '🌱',
      'established': '🏢',
      'startup': '🚀',
      'collective': '🎨'
    };
    return icons[type] || '🏰';
  };

  // Get role color
  const getRoleColor = (role) => {
    const colors = {
      'founder': 'warning',
      'owner': 'primary',
      'admin': 'secondary',
      'member': 'default',
      'contributor': 'success'
    };
    return colors[role] || 'default';
  };

  // Get role icon
  const getRoleIcon = (role) => {
    const icons = {
      'founder': '👑',
      'owner': '🛡️',
      'admin': '⚙️',
      'member': '👤',
      'contributor': '🤝'
    };
    return icons[role] || '👤';
  };

  // Format member count
  const formatMemberCount = (count) => {
    if (!count) return '0 members';
    return `${count} member${count !== 1 ? 's' : ''}`;
  };

  // Handle studio selection
  const handleAllianceSelect = (allianceId) => {
    const selectedAlliance = userAlliances.find(a => a.id === allianceId);
    if (selectedAlliance && onAllianceChange) {
      onAllianceChange(selectedAlliance);
    }
    setIsOpen(false);
  };

  // Render current studio button
  const renderCurrentAllianceButton = () => {
    if (!currentAlliance) {
      return (
        <Button
          variant="bordered"
          className="justify-start min-w-48"
          startContent={<span className="text-xl">🏰</span>}
        >
          <div className="text-left">
            <div className="font-medium">No Studio Selected</div>
            <div className="text-xs text-default-500">Choose an studio</div>
          </div>
        </Button>
      );
    }

    return (
      <Button
        variant="bordered"
        className="justify-start min-w-48"
        startContent={
          <span className="text-xl">
            {getTypeIcon(currentAlliance.studio_type)}
          </span>
        }
        endContent={
          <Chip
            color={getRoleColor(currentAlliance.user_role)}
            size="sm"
            variant="flat"
            startContent={<span>{getRoleIcon(currentAlliance.user_role)}</span>}
          >
            {currentAlliance.user_role}
          </Chip>
        }
      >
        <div className="text-left flex-1">
          <div className="font-medium line-clamp-1">
            {currentAlliance.name}
          </div>
          <div className="text-xs text-default-500">
            {formatMemberCount(currentAlliance.members?.length)}
          </div>
        </div>
      </Button>
    );
  };

  return (
    <div className={className}>
      <Dropdown 
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        placement="bottom-start"
        classNames={{
          content: "min-w-80"
        }}
      >
        <DropdownTrigger>
          {renderCurrentAllianceButton()}
        </DropdownTrigger>
        
        <DropdownMenu
          aria-label="Studio selection"
          variant="flat"
          disallowEmptySelection={false}
          selectionMode="single"
          selectedKeys={currentAlliance ? [currentAlliance.id] : []}
          onSelectionChange={(keys) => {
            const selectedKey = Array.from(keys)[0];
            if (selectedKey) {
              handleAllianceSelect(selectedKey);
            }
          }}
        >
          {/* Header */}
          <DropdownItem key="header" isReadOnly className="opacity-100">
            <div className="flex items-center justify-between">
              <span className="font-semibold">Your Studios</span>
              <span className="text-xs text-default-500">
                {userAlliances.length} total
              </span>
            </div>
          </DropdownItem>

          {/* Studio List */}
          {userAlliances.map((studio) => (
            <DropdownItem
              key={studio.id}
              className="py-3"
              startContent={
                <div className="flex items-center gap-3">
                  <span className="text-xl">
                    {getTypeIcon(studio.studio_type)}
                  </span>
                  <Avatar
                    src={studio.logo_url}
                    name={studio.name}
                    size="sm"
                    className="flex-shrink-0"
                  />
                </div>
              }
              endContent={
                <Chip
                  color={getRoleColor(studio.user_role)}
                  size="sm"
                  variant="flat"
                  startContent={<span>{getRoleIcon(studio.user_role)}</span>}
                >
                  {studio.user_role}
                </Chip>
              }
            >
              <div className="flex-1">
                <div className="font-medium line-clamp-1">
                  {studio.name}
                </div>
                <div className="text-xs text-default-500 flex items-center gap-2">
                  <span>{studio.industry}</span>
                  <span>•</span>
                  <span>{formatMemberCount(studio.members?.length)}</span>
                  {studio.projects?.length > 0 && (
                    <>
                      <span>•</span>
                      <span>{studio.projects.length} projects</span>
                    </>
                  )}
                </div>
              </div>
            </DropdownItem>
          ))}

          {/* Empty state */}
          {userAlliances.length === 0 && (
            <DropdownItem key="empty" isReadOnly className="opacity-100">
              <div className="text-center py-4">
                <div className="text-4xl mb-2">🏰</div>
                <div className="font-medium mb-1">No Studios Yet</div>
                <div className="text-xs text-default-500">
                  Create your first studio to get started
                </div>
              </div>
            </DropdownItem>
          )}

          {/* Divider */}
          <DropdownItem key="divider" isReadOnly className="p-0">
            <Divider />
          </DropdownItem>

          {/* Actions */}
          <DropdownItem
            key="create"
            startContent={<span className="text-xl">➕</span>}
            className="text-primary"
            onPress={() => {
              setIsOpen(false);
              if (onCreateAlliance) {
                onCreateAlliance();
              }
            }}
          >
            <div>
              <div className="font-medium">Create New Studio</div>
              <div className="text-xs text-default-500">
                Start a new collaborative organization
              </div>
            </div>
          </DropdownItem>

          <DropdownItem
            key="discover"
            startContent={<span className="text-xl">🔍</span>}
            className="text-secondary"
            onPress={() => {
              setIsOpen(false);
              // Navigate to studio discovery
            }}
          >
            <div>
              <div className="font-medium">Discover Studios</div>
              <div className="text-xs text-default-500">
                Find and join existing studios
              </div>
            </div>
          </DropdownItem>

          {currentAlliance && (
            <DropdownItem
              key="manage"
              startContent={<span className="text-xl">⚙️</span>}
              onPress={() => {
                setIsOpen(false);
                // Navigate to studio management
              }}
            >
              <div>
                <div className="font-medium">Manage Studio</div>
                <div className="text-xs text-default-500">
                  Settings and configuration
                </div>
              </div>
            </DropdownItem>
          )}
        </DropdownMenu>
      </Dropdown>
    </div>
  );
};

export default AllianceSelector;

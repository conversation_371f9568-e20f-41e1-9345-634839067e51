#!/usr/bin/env node

/**
 * Detailed Schema Check
 * Uses SQL queries to get detailed column information from the database
 */

import { createClient } from '@supabase/supabase-js';

// Use the known Supabase URL and anon key
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function detailedSchemaCheck() {
  console.log('🔍 Detailed Database Schema Check...');
  console.log('=====================================\n');

  try {
    // Get sample data from each table to see actual columns
    console.log('📋 Checking TEAMS table structure...');
    const { data: teamsData, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .limit(1);

    if (teamsError) {
      console.log(`   ❌ Error: ${teamsError.message}`);
    } else {
      console.log('   ✅ Teams table accessible');
      if (teamsData && teamsData.length > 0) {
        const columns = Object.keys(teamsData[0]);
        console.log(`   📊 Columns (${columns.length}): ${columns.join(', ')}`);
        
        // Check for terminology columns
        const allianceColumns = columns.filter(col => col.includes('alliance'));
        const studioColumns = columns.filter(col => col.includes('studio'));
        
        if (allianceColumns.length > 0) {
          console.log(`   🎯 Alliance columns: ${allianceColumns.join(', ')}`);
        }
        if (studioColumns.length > 0) {
          console.log(`   🎯 Studio columns: ${studioColumns.join(', ')}`);
        }
        if (allianceColumns.length === 0 && studioColumns.length === 0) {
          console.log('   ℹ️ No alliance or studio columns found');
        }
      } else {
        console.log('   ⚠️ No data in teams table to analyze structure');
      }
    }

    console.log('\n📋 Checking PROJECTS table structure...');
    const { data: projectsData, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .limit(1);

    if (projectsError) {
      console.log(`   ❌ Error: ${projectsError.message}`);
    } else {
      console.log('   ✅ Projects table accessible');
      if (projectsData && projectsData.length > 0) {
        const columns = Object.keys(projectsData[0]);
        console.log(`   📊 Columns (${columns.length}): ${columns.join(', ')}`);
        
        // Check for terminology columns
        const ventureColumns = columns.filter(col => col.includes('venture'));
        const projectColumns = columns.filter(col => col.includes('project'));
        const allianceColumns = columns.filter(col => col.includes('alliance'));
        const studioColumns = columns.filter(col => col.includes('studio'));
        
        if (ventureColumns.length > 0) {
          console.log(`   🎯 Venture columns: ${ventureColumns.join(', ')}`);
        }
        if (projectColumns.length > 0) {
          console.log(`   🎯 Project columns: ${projectColumns.join(', ')}`);
        }
        if (allianceColumns.length > 0) {
          console.log(`   🎯 Alliance columns: ${allianceColumns.join(', ')}`);
        }
        if (studioColumns.length > 0) {
          console.log(`   🎯 Studio columns: ${studioColumns.join(', ')}`);
        }
      } else {
        console.log('   ⚠️ No data in projects table to analyze structure');
      }
    }

    console.log('\n📋 Checking TASKS table structure...');
    const { data: tasksData, error: tasksError } = await supabase
      .from('tasks')
      .select('*')
      .limit(1);

    if (tasksError) {
      console.log(`   ❌ Error: ${tasksError.message}`);
    } else {
      console.log('   ✅ Tasks table accessible');
      if (tasksData && tasksData.length > 0) {
        const columns = Object.keys(tasksData[0]);
        console.log(`   📊 Columns (${columns.length}): ${columns.join(', ')}`);
        
        // Check for terminology columns
        const questColumns = columns.filter(col => col.includes('quest'));
        const missionColumns = columns.filter(col => col.includes('mission'));
        const categoryColumns = columns.filter(col => col.includes('category'));
        
        if (questColumns.length > 0) {
          console.log(`   🎯 Quest columns: ${questColumns.join(', ')}`);
        }
        if (missionColumns.length > 0) {
          console.log(`   🎯 Mission columns: ${missionColumns.join(', ')}`);
        }
        if (categoryColumns.length > 0) {
          console.log(`   🎯 Category columns: ${categoryColumns.join(', ')}`);
        }
        
        // Check task_category values if it exists
        if (columns.includes('task_category')) {
          console.log('   🔍 Checking task_category values...');
          const { data: categoryData, error: categoryError } = await supabase
            .from('tasks')
            .select('task_category')
            .not('task_category', 'is', null);
          
          if (!categoryError && categoryData) {
            const uniqueCategories = [...new Set(categoryData.map(t => t.task_category))];
            console.log(`   📊 Task categories: ${uniqueCategories.join(', ')}`);
          }
        }
      } else {
        console.log('   ⚠️ No data in tasks table to analyze structure');
      }
    }

    // Check team_members table for collaboration types
    console.log('\n📋 Checking TEAM_MEMBERS table structure...');
    const { data: membersData, error: membersError } = await supabase
      .from('team_members')
      .select('*')
      .limit(1);

    if (membersError) {
      console.log(`   ❌ Error: ${membersError.message}`);
    } else {
      console.log('   ✅ Team_members table accessible');
      if (membersData && membersData.length > 0) {
        const columns = Object.keys(membersData[0]);
        console.log(`   📊 Columns (${columns.length}): ${columns.join(', ')}`);
        
        // Check for collaboration type columns
        const collaborationColumns = columns.filter(col => 
          col.includes('collaboration') || 
          col.includes('engagement') || 
          col.includes('specialization')
        );
        
        if (collaborationColumns.length > 0) {
          console.log(`   🎯 Collaboration columns: ${collaborationColumns.join(', ')}`);
        } else {
          console.log('   ℹ️ No collaboration type columns found');
        }
      } else {
        console.log('   ⚠️ No data in team_members table to analyze structure');
      }
    }

    // Generate specific migration recommendations
    console.log('\n📝 Specific Migration Recommendations:');
    console.log('=====================================');
    
    // Based on what we found, provide specific guidance
    if (teamsData && teamsData.length > 0) {
      const teamsColumns = Object.keys(teamsData[0]);
      const hasAllianceType = teamsColumns.includes('alliance_type');
      const hasStudioType = teamsColumns.includes('studio_type');
      
      if (hasAllianceType && hasStudioType) {
        console.log('⚠️ TEAMS: Both alliance_type and studio_type exist - need data migration and cleanup');
      } else if (hasAllianceType && !hasStudioType) {
        console.log('✅ TEAMS: Can safely rename alliance_type → studio_type');
      } else if (!hasAllianceType && hasStudioType) {
        console.log('✅ TEAMS: studio_type already exists, no migration needed');
      } else {
        console.log('➕ TEAMS: Need to add studio_type column');
      }
    }

    if (projectsData && projectsData.length > 0) {
      const projectsColumns = Object.keys(projectsData[0]);
      const hasVentureType = projectsColumns.includes('venture_type');
      const hasProjectType = projectsColumns.includes('project_type');
      const hasAllianceId = projectsColumns.includes('alliance_id');
      const hasStudioId = projectsColumns.includes('studio_id');
      
      if (hasVentureType && hasProjectType) {
        console.log('⚠️ PROJECTS: Both venture_type and project_type exist - need data migration and cleanup');
      } else if (hasVentureType && !hasProjectType) {
        console.log('✅ PROJECTS: Can safely rename venture_type → project_type');
      } else if (!hasVentureType && hasProjectType) {
        console.log('✅ PROJECTS: project_type already exists, no migration needed');
      } else {
        console.log('➕ PROJECTS: Need to add project_type column');
      }

      if (hasAllianceId && hasStudioId) {
        console.log('⚠️ PROJECTS: Both alliance_id and studio_id exist - need data migration and cleanup');
      } else if (hasAllianceId && !hasStudioId) {
        console.log('✅ PROJECTS: Can safely rename alliance_id → studio_id');
      } else if (!hasAllianceId && hasStudioId) {
        console.log('✅ PROJECTS: studio_id already exists, no migration needed');
      } else {
        console.log('➕ PROJECTS: Need to add studio_id column');
      }
    }

    if (tasksData && tasksData.length > 0) {
      const tasksColumns = Object.keys(tasksData[0]);
      const questColumns = tasksColumns.filter(col => col.includes('quest'));
      const missionColumns = tasksColumns.filter(col => col.includes('mission'));
      
      if (questColumns.length > 0 && missionColumns.length > 0) {
        console.log('⚠️ TASKS: Both quest and mission columns exist - need data migration and cleanup');
        console.log(`   Quest columns: ${questColumns.join(', ')}`);
        console.log(`   Mission columns: ${missionColumns.join(', ')}`);
      } else if (questColumns.length > 0 && missionColumns.length === 0) {
        console.log('✅ TASKS: Can safely rename quest columns to mission columns');
        console.log(`   Quest columns to rename: ${questColumns.join(', ')}`);
      } else if (questColumns.length === 0 && missionColumns.length > 0) {
        console.log('✅ TASKS: Mission columns already exist, no migration needed');
        console.log(`   Mission columns: ${missionColumns.join(', ')}`);
      } else {
        console.log('➕ TASKS: Need to add mission columns');
      }
    }

    console.log('\n🎯 Next Steps:');
    console.log('1. Create a targeted migration based on the findings above');
    console.log('2. Test the migration on a backup first');
    console.log('3. Apply the migration to production');
    console.log('4. Verify all data is preserved and accessible');

  } catch (error) {
    console.error('\n❌ Schema check failed:', error.message);
    process.exit(1);
  }
}

// Main execution
detailedSchemaCheck().catch(console.error);

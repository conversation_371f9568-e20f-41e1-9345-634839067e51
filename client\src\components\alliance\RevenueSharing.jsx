import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Avatar, Input } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * RevenueSharing Component - Automated Revenue Distribution Setup
 * 
 * Features:
 * - Real-time revenue tracking and distribution
 * - Member-specific revenue allocation
 * - Automated payout processing
 * - Historical revenue analytics
 * - Performance-based distribution metrics
 */
const RevenueSharing = ({ allianceId, userRole, className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [revenueData, setRevenueData] = useState({
    total_revenue: 0,
    pending_distribution: 0,
    distributed_revenue: 0,
    member_allocations: [],
    recent_distributions: []
  });
  const [showDistributionModal, setShowDistributionModal] = useState(false);
  const [customAllocations, setCustomAllocations] = useState({});

  useEffect(() => {
    if (allianceId && currentUser) {
      fetchRevenueData();
    }
  }, [allianceId, currentUser]);

  // Fetch revenue sharing data
  const fetchRevenueData = async () => {
    try {
      setLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      const response = await fetch(`/.netlify/functions/vrc-revenue-management/${allianceId}/revenue-sharing`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setRevenueData(data);
        
        // Initialize custom allocations
        const allocations = {};
        data.member_allocations?.forEach(member => {
          allocations[member.user_id] = member.allocation_percentage || 0;
        });
        setCustomAllocations(allocations);
      } else {
        throw new Error('Failed to fetch revenue data');
      }
    } catch (error) {
      console.error('Error fetching revenue data:', error);
      toast.error('Failed to load revenue sharing data');
    } finally {
      setLoading(false);
    }
  };

  // Process revenue distribution
  const processDistribution = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      const response = await fetch(`/.netlify/functions/vrc-revenue-management/${allianceId}/distribute`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          custom_allocations: customAllocations
        })
      });

      if (response.ok) {
        toast.success('Revenue distribution processed successfully!');
        setShowDistributionModal(false);
        await fetchRevenueData();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process distribution');
      }
    } catch (error) {
      console.error('Error processing distribution:', error);
      toast.error(error.message || 'Failed to process revenue distribution');
    }
  };

  // Update custom allocation
  const updateAllocation = (userId, percentage) => {
    setCustomAllocations(prev => ({
      ...prev,
      [userId]: Math.max(0, Math.min(100, percentage))
    }));
  };

  // Calculate total allocation percentage
  const getTotalAllocation = () => {
    return Object.values(customAllocations).reduce((sum, percentage) => sum + percentage, 0);
  };

  // Check if user can manage revenue
  const canManageRevenue = ['founder', 'owner'].includes(userRole);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardBody className="p-6 text-center">
          <div className="animate-spin text-2xl mb-2">🔄</div>
          <div>Loading revenue sharing data...</div>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className={`revenue-sharing ${className}`}>
      <Card className="bg-gradient-to-br from-yellow-50 to-orange-100 dark:from-yellow-900/20 dark:to-orange-800/20 border-2 border-yellow-200 dark:border-yellow-700">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">💰</span>
              <h3 className="text-lg font-semibold">Revenue Sharing</h3>
            </div>
            {canManageRevenue && revenueData.pending_distribution > 0 && (
              <Button
                color="warning"
                onPress={() => setShowDistributionModal(true)}
              >
                Distribute Revenue
              </Button>
            )}
          </div>
        </CardHeader>
        
        <CardBody className="pt-0 space-y-6">
          {/* Revenue Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="bg-white/50 dark:bg-slate-800/50">
              <CardBody className="p-4 text-center">
                <div className="text-2xl font-bold text-success">
                  {formatCurrency(revenueData.total_revenue)}
                </div>
                <div className="text-sm text-default-600">Total Revenue</div>
              </CardBody>
            </Card>
            
            <Card className="bg-white/50 dark:bg-slate-800/50">
              <CardBody className="p-4 text-center">
                <div className="text-2xl font-bold text-warning">
                  {formatCurrency(revenueData.pending_distribution)}
                </div>
                <div className="text-sm text-default-600">Pending Distribution</div>
              </CardBody>
            </Card>
            
            <Card className="bg-white/50 dark:bg-slate-800/50">
              <CardBody className="p-4 text-center">
                <div className="text-2xl font-bold text-primary">
                  {formatCurrency(revenueData.distributed_revenue)}
                </div>
                <div className="text-sm text-default-600">Distributed</div>
              </CardBody>
            </Card>
          </div>

          {/* Member Allocations */}
          <div>
            <h4 className="text-lg font-semibold mb-4">👥 Member Revenue Allocations</h4>
            <div className="space-y-3">
              {revenueData.member_allocations?.map((member, index) => (
                <motion.div
                  key={member.user_id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <Card className="border border-default-200">
                    <CardBody className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <Avatar 
                            src={member.user?.avatar_url}
                            name={member.user?.display_name || member.user?.email}
                            size="sm"
                          />
                          <div>
                            <div className="font-medium">
                              {member.user?.display_name || member.user?.email}
                            </div>
                            <div className="text-sm text-default-600">
                              {member.role} • Joined {formatDate(member.joined_at)}
                            </div>
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <div className="font-semibold">
                            {member.allocation_percentage?.toFixed(1)}%
                          </div>
                          <div className="text-sm text-default-600">
                            {formatCurrency((revenueData.pending_distribution * member.allocation_percentage) / 100)}
                          </div>
                        </div>
                      </div>

                      {/* Allocation Progress */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span>Revenue Share</span>
                          <span>{member.allocation_percentage?.toFixed(1)}%</span>
                        </div>
                        <Progress 
                          value={member.allocation_percentage || 0} 
                          color="warning" 
                          size="sm"
                        />
                      </div>

                      {/* Performance Metrics */}
                      <div className="grid grid-cols-3 gap-4 mt-3 pt-3 border-t border-default-200 text-xs">
                        <div>
                          <div className="text-default-500">Contributions</div>
                          <div className="font-medium">{member.total_contributions || 0}</div>
                        </div>
                        <div>
                          <div className="text-default-500">Hours</div>
                          <div className="font-medium">{Math.round(member.total_hours || 0)}h</div>
                        </div>
                        <div>
                          <div className="text-default-500">Quality</div>
                          <div className="font-medium">{Math.round(member.quality_score || 0)}%</div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Recent Distributions */}
          {revenueData.recent_distributions?.length > 0 && (
            <div>
              <h4 className="text-lg font-semibold mb-4">📊 Recent Distributions</h4>
              <div className="space-y-2">
                {revenueData.recent_distributions.slice(0, 5).map((distribution, index) => (
                  <Card key={index} className="border border-default-200">
                    <CardBody className="p-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">
                            {formatCurrency(distribution.amount)} distributed
                          </div>
                          <div className="text-sm text-default-600">
                            {formatDate(distribution.created_at)} • {distribution.recipients_count} recipients
                          </div>
                        </div>
                        <Chip 
                          color={distribution.status === 'completed' ? 'success' : 'warning'} 
                          size="sm" 
                          variant="flat"
                        >
                          {distribution.status}
                        </Chip>
                      </div>
                    </CardBody>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* No Revenue Message */}
          {revenueData.total_revenue === 0 && (
            <Card className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200">
              <CardBody className="p-6 text-center">
                <span className="text-4xl mb-4 block">💰</span>
                <h4 className="text-lg font-medium mb-2">No Revenue Yet</h4>
                <p className="text-default-600 mb-4">
                  Revenue sharing will be available once your studio starts generating income
                </p>
                <Button
                  color="primary"
                  variant="bordered"
                  onPress={() => toast.info('Revenue tracking setup coming soon!')}
                >
                  Set Up Revenue Tracking
                </Button>
              </CardBody>
            </Card>
          )}
        </CardBody>
      </Card>

      {/* Distribution Modal */}
      <Modal 
        isOpen={showDistributionModal} 
        onClose={() => setShowDistributionModal(false)}
        size="3xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>
            <span className="text-xl">💰 Distribute Revenue</span>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-6">
              <Card className="bg-warning-50 dark:bg-warning-900/20 border border-warning-200">
                <CardBody className="p-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-warning">
                      {formatCurrency(revenueData.pending_distribution)}
                    </div>
                    <div className="text-sm text-default-600">Available for Distribution</div>
                  </div>
                </CardBody>
              </Card>

              <div>
                <h4 className="font-semibold mb-3">Adjust Member Allocations</h4>
                <div className="space-y-3">
                  {revenueData.member_allocations?.map((member) => (
                    <div key={member.user_id} className="flex items-center gap-3">
                      <Avatar 
                        src={member.user?.avatar_url}
                        name={member.user?.display_name || member.user?.email}
                        size="sm"
                      />
                      <div className="flex-1">
                        <div className="font-medium text-sm">
                          {member.user?.display_name || member.user?.email}
                        </div>
                      </div>
                      <div className="w-24">
                        <Input
                          size="sm"
                          value={customAllocations[member.user_id] || 0}
                          onChange={(e) => updateAllocation(member.user_id, parseFloat(e.target.value) || 0)}
                          endContent="%"
                          type="number"
                          min="0"
                          max="100"
                        />
                      </div>
                      <div className="w-20 text-right text-sm">
                        {formatCurrency((revenueData.pending_distribution * (customAllocations[member.user_id] || 0)) / 100)}
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Total Allocation:</span>
                    <span className={`font-bold ${getTotalAllocation() === 100 ? 'text-success' : 'text-warning'}`}>
                      {getTotalAllocation().toFixed(1)}%
                    </span>
                  </div>
                  {getTotalAllocation() !== 100 && (
                    <div className="text-sm text-warning mt-1">
                      ⚠️ Total allocation must equal 100%
                    </div>
                  )}
                </div>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={() => setShowDistributionModal(false)}>
              Cancel
            </Button>
            <Button 
              color="warning" 
              onPress={processDistribution}
              isDisabled={getTotalAllocation() !== 100}
            >
              Process Distribution
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default RevenueSharing;

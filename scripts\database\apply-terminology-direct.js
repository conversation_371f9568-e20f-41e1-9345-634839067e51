#!/usr/bin/env node

/**
 * Apply Terminology Migration - Direct SQL
 * Applies the terminology migration using direct SQL statements
 */

import { createClient } from '@supabase/supabase-js';

// Use the known Supabase URL and service key
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyMigration() {
  console.log('🔄 Applying Terminology Migration (Direct SQL)...');
  console.log('=====================================');

  try {
    // Step 1: Add studio_type to teams table
    console.log('\n1️⃣ Adding studio_type to teams table...');
    
    // First check if column exists
    const { data: teamsColumns } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'teams')
      .eq('column_name', 'studio_type');

    if (!teamsColumns || teamsColumns.length === 0) {
      // Column doesn't exist, add it
      const { error: studioTypeError } = await supabase.rpc('exec_sql', {
        sql: `ALTER TABLE public.teams ADD COLUMN studio_type TEXT DEFAULT 'emerging' CHECK (studio_type IN ('emerging', 'established', 'solo'));`
      });

      if (studioTypeError) {
        console.log(`❌ Error adding studio_type: ${studioTypeError.message}`);
        // Try alternative approach
        console.log('Trying alternative approach...');
        
        // Use a simple insert to test if we can modify the schema
        const { error: testError } = await supabase
          .from('teams')
          .select('id')
          .limit(1);
          
        if (testError) {
          console.log(`❌ Cannot access teams table: ${testError.message}`);
        } else {
          console.log('✅ Teams table accessible, but cannot add columns via API');
          console.log('ℹ️ You may need to add the column manually in the Supabase dashboard');
        }
      } else {
        console.log('✅ studio_type column added successfully');
      }
    } else {
      console.log('✅ studio_type column already exists');
    }

    // Step 2: Test what we can do with the current permissions
    console.log('\n2️⃣ Testing current database permissions...');
    
    // Test if we can read from teams
    const { data: teamsData, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .limit(1);

    if (teamsError) {
      console.log(`❌ Cannot read teams: ${teamsError.message}`);
    } else {
      console.log('✅ Can read teams table');
      if (teamsData && teamsData.length > 0) {
        const columns = Object.keys(teamsData[0]);
        console.log(`   Columns: ${columns.join(', ')}`);
        
        if (columns.includes('studio_type')) {
          console.log('   ✅ studio_type column exists!');
        } else {
          console.log('   ❌ studio_type column missing');
        }
      }
    }

    // Test projects table
    const { data: projectsData, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .limit(1);

    if (projectsError) {
      console.log(`❌ Cannot read projects: ${projectsError.message}`);
    } else {
      console.log('✅ Can read projects table');
      if (projectsData && projectsData.length > 0) {
        const columns = Object.keys(projectsData[0]);
        console.log(`   Columns: ${columns.join(', ')}`);
        
        const hasProjectType = columns.includes('project_type');
        const hasStudioId = columns.includes('studio_id');
        
        if (hasProjectType) {
          console.log('   ✅ project_type column exists!');
        } else {
          console.log('   ❌ project_type column missing');
        }
        
        if (hasStudioId) {
          console.log('   ✅ studio_id column exists!');
        } else {
          console.log('   ❌ studio_id column missing');
        }
      }
    }

    // Test tasks table
    const { data: tasksData, error: tasksError } = await supabase
      .from('tasks')
      .select('*')
      .limit(1);

    if (tasksError) {
      console.log(`❌ Cannot read tasks: ${tasksError.message}`);
    } else {
      console.log('✅ Can read tasks table');
      if (tasksData && tasksData.length > 0) {
        const columns = Object.keys(tasksData[0]);
        console.log(`   Columns: ${columns.join(', ')}`);
        
        const missionColumns = columns.filter(col => 
          col.includes('mission') || col === 'task_category'
        );
        
        if (missionColumns.length > 0) {
          console.log(`   ✅ Mission columns exist: ${missionColumns.join(', ')}`);
        } else {
          console.log('   ❌ Mission columns missing');
        }
      }
    }

    // Test team_members table
    const { data: membersData, error: membersError } = await supabase
      .from('team_members')
      .select('*')
      .limit(1);

    if (membersError) {
      console.log(`❌ Cannot read team_members: ${membersError.message}`);
    } else {
      console.log('✅ Can read team_members table');
      if (membersData && membersData.length > 0) {
        const columns = Object.keys(membersData[0]);
        console.log(`   Columns: ${columns.join(', ')}`);
        
        const collaborationColumns = columns.filter(col => 
          col.includes('collaboration') || col.includes('engagement') || col.includes('specialization')
        );
        
        if (collaborationColumns.length > 0) {
          console.log(`   ✅ Collaboration columns exist: ${collaborationColumns.join(', ')}`);
        } else {
          console.log('   ❌ Collaboration columns missing');
        }
      }
    }

    console.log('\n📋 Migration Status Summary:');
    console.log('=====================================');
    console.log('Based on the current database state:');
    
    if (teamsData && teamsData.length > 0) {
      const teamsColumns = Object.keys(teamsData[0]);
      console.log(`✅ Teams table: ${teamsColumns.includes('studio_type') ? 'Has studio_type' : 'Missing studio_type'}`);
    }
    
    if (projectsData && projectsData.length > 0) {
      const projectsColumns = Object.keys(projectsData[0]);
      console.log(`✅ Projects table: ${projectsColumns.includes('project_type') ? 'Has project_type' : 'Missing project_type'}, ${projectsColumns.includes('studio_id') ? 'Has studio_id' : 'Missing studio_id'}`);
    }
    
    if (tasksData && tasksData.length > 0) {
      const tasksColumns = Object.keys(tasksData[0]);
      const missionCols = tasksColumns.filter(col => col.includes('mission') || col === 'task_category');
      console.log(`✅ Tasks table: ${missionCols.length > 0 ? `Has mission columns: ${missionCols.join(', ')}` : 'Missing mission columns'}`);
    }
    
    if (membersData && membersData.length > 0) {
      const membersColumns = Object.keys(membersData[0]);
      const collabCols = membersColumns.filter(col => col.includes('collaboration') || col.includes('engagement'));
      console.log(`✅ Team_members table: ${collabCols.length > 0 ? `Has collaboration columns: ${collabCols.join(', ')}` : 'Missing collaboration columns'}`);
    }

    console.log('\n🎯 Next Steps:');
    console.log('If columns are missing, you can add them manually in the Supabase dashboard:');
    console.log('1. Go to https://supabase.com/dashboard/project/hqqlrrqvjcetoxbdjgzx/editor');
    console.log('2. Use the SQL editor to run the migration script');
    console.log('3. Or add columns individually using the table editor');

  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Test database connection first
async function testConnection() {
  console.log('🔌 Testing database connection...');
  
  try {
    const { data, error } = await supabase
      .from('teams')
      .select('id')
      .limit(1);

    if (error) {
      throw error;
    }

    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

// Main execution
async function main() {
  const connected = await testConnection();
  if (!connected) {
    process.exit(1);
  }

  await applyMigration();
}

// Handle command line execution
main().catch(console.error);

import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Avatar, AvatarGroup } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * AllianceVentures Component - Active Projects Management
 * 
 * Features:
 * - Real-time project tracking with progress indicators
 * - Member assignment and leadership display
 * - Revenue tracking and milestone management
 * - Priority indicators and status management
 * - Quick access to project details and analytics
 */
const AllianceVentures = ({ allianceId, className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [projects, setVentures] = useState([
    {
      id: 1,
      name: 'TaskMaster Pro',
      icon: '⚔️',
      revenue: 18400,
      members: 8,
      progress: 85,
      status: 'Beta Testing',
      leader: 'Alex',
      launch: '3 months',
      missions: 23,
      quality: 4.8,
      priority: 'high'
    },
    {
      id: 2,
      name: 'Creative Studio Platform',
      icon: '🎨',
      revenue: 8200,
      members: 6,
      progress: 60,
      status: 'Development',
      leader: 'Sarah',
      launch: '6 months',
      missions: 15,
      quality: 4.6,
      priority: 'medium'
    },
    {
      id: 3,
      name: 'Testing Automation Tool',
      icon: '🧪',
      revenue: 3600,
      members: 4,
      progress: 40,
      status: 'Early Stage',
      leader: 'Lisa',
      launch: '8 months',
      missions: 9,
      quality: 4.4,
      priority: 'low'
    }
  ]);

  // Format currency display
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Get priority color
  const getPriorityColor = (priority) => {
    const colors = {
      'high': 'danger',
      'medium': 'warning',
      'low': 'default'
    };
    return colors[priority] || 'default';
  };

  // Get priority icon
  const getPriorityIcon = (priority) => {
    const icons = {
      'high': '🔥',
      'medium': '⚡',
      'low': '📋'
    };
    return icons[priority] || '📋';
  };

  // Load projects data
  const loadVentures = async () => {
    try {
      setLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      const response = await fetch(`/.netlify/functions/studio-management/${allianceId}/projects`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setVentures(data.projects || projects);
      } else {
        // Use mock data for now
        console.log('Using mock projects data');
      }
      
    } catch (error) {
      console.error('Error loading projects:', error);
      // Continue with mock data
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (allianceId && currentUser) {
      loadVentures();
    }
  }, [allianceId, currentUser]);

  if (loading) {
    return (
      <Card className={`h-full ${className}`}>
        <CardBody className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardBody>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Card className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-800/20 hover:shadow-lg transition-shadow h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">🚀</span>
              <h3 className="text-lg font-semibold">Active Projects</h3>
            </div>
            <Chip color="primary" variant="flat" size="sm">
              6×2
            </Chip>
          </div>
        </CardHeader>
        <CardBody className="space-y-4">
          {/* Projects List */}
          <div className="space-y-4">
            {projects.map((project) => (
              <div key={project.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{project.icon}</span>
                    <span className="font-semibold">{project.name}</span>
                  </div>
                  <Chip
                    color={getPriorityColor(project.priority)}
                    size="sm"
                    variant="flat"
                    startContent={<span>{getPriorityIcon(project.priority)}</span>}
                  >
                    {project.priority}
                  </Chip>
                </div>

                <div className="text-sm text-default-600 space-y-1">
                  <div className="flex items-center justify-between">
                    <span>💰 {formatCurrency(project.revenue)} revenue</span>
                    <span>👥 {project.members} members</span>
                  </div>
                  
                  <Progress
                    value={project.progress}
                    color={project.progress > 70 ? 'success' : project.progress > 40 ? 'warning' : 'default'}
                    size="sm"
                    className="mt-1"
                    label={`${project.progress}% complete`}
                  />
                  
                  <div className="flex items-center justify-between text-xs">
                    <span>🎯 Status: {project.status}</span>
                    <span>👑 Leader: {project.leader}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs">
                    <span>⏰ Launch: {project.launch}</span>
                    <span>📊 {project.missions} missions • {project.quality}★ quality</span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            <Button
              color="primary"
              variant="flat"
              size="sm"
              onClick={() => {
                toast.info('View all projects coming soon');
              }}
            >
              View All ({projects.length + 2})
            </Button>
            <Button
              color="success"
              variant="flat"
              size="sm"
              onClick={() => {
                toast.info('Create project coming soon');
              }}
            >
              Create Project
            </Button>
          </div>

          <div className="flex gap-2">
            <Button
              color="secondary"
              variant="flat"
              size="sm"
              onClick={() => {
                toast.info('Project analytics coming soon');
              }}
            >
              Analytics
            </Button>
            <Button
              color="default"
              variant="flat"
              size="sm"
              onClick={() => {
                toast.info('Resource allocation coming soon');
              }}
            >
              Resource Allocation
            </Button>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default AllianceVentures;

/**
 * Comprehensive Agreement Analysis Test Suite
 * 
 * This test suite simulates the actual user flow from alliance creation
 * through venture setup to agreement generation, then performs detailed
 * analysis against the lawyer-approved template.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import agreement generation system
import { NewAgreementGenerator } from '../client/src/utils/agreement/newAgreementGenerator.js';
import { agreementTemplateSystem } from '../client/src/utils/agreement/agreementTemplateSystem.js';

describe('Comprehensive Agreement Analysis', () => {
  let generator;
  let lawyerApprovedTemplate;
  let generatedAgreements = [];
  let analysisResults = [];

  beforeAll(async () => {
    generator = new NewAgreementGenerator();
    
    // Load the lawyer-approved template for comparison
    const templatePath = path.join(__dirname, '../client/public/example-cog-contributor-agreement.md');
    lawyerApprovedTemplate = fs.readFileSync(templatePath, 'utf8');
  });

  describe('User Flow Simulation Tests', () => {
    
    test('Scenario 1: Software Development Venture - Tech Alliance', async () => {
      console.log('\n🧪 TESTING SCENARIO 1: Software Development Venture');
      
      // Step 1: Create Alliance (simulating user input)
      const alliance = {
        id: 'alliance_tech_001',
        name: 'Tech Innovation Alliance',
        industry: 'TECHNOLOGY',
        collaborationType: 'software_development',
        jurisdiction: 'Florida',
        governingLaw: 'Florida',
        disputeResolution: 'court_litigation',
        revenueModel: 'revenue_sharing',
        ipOwnershipModel: 'company_owned',
        companyInfo: {
          legalName: 'TechCorp Solutions Inc.',
          state: 'Florida',
          address: '123 Innovation Drive, Orlando, FL 32801',
          signerName: 'John Smith',
          signerTitle: 'CEO',
          billingEmail: '<EMAIL>'
        }
      };

      // Step 2: Create Venture (simulating user input)
      const venture = {
        id: 'venture_software_001',
        allianceId: alliance.id,
        name: 'AI Analytics Platform',
        description: 'Advanced AI-powered analytics platform for enterprise data insights',
        projectType: 'software',
        scope: 'Full-stack web application with AI/ML capabilities',
        objectives: ['Develop core analytics engine', 'Build user interface', 'Implement AI models'],
        deliverables: [
          { type: 'software', name: 'Core Analytics Engine', description: 'Backend processing system' },
          { type: 'software', name: 'Web Dashboard', description: 'Frontend user interface' },
          { type: 'documentation', name: 'API Documentation', description: 'Technical documentation' }
        ],
        milestones: [
          { name: 'MVP Development', deadline: '2024-06-01', description: 'Basic functionality complete' },
          { name: 'Beta Release', deadline: '2024-09-01', description: 'Feature-complete beta version' },
          { name: 'Production Launch', deadline: '2024-12-01', description: 'Full production release' }
        ],
        techStack: {
          platforms: ['Web', 'Cloud'],
          languages: ['Python', 'JavaScript', 'TypeScript'],
          frameworks: ['React', 'FastAPI', 'TensorFlow'],
          databases: ['PostgreSQL', 'Redis']
        },
        startDate: '2024-03-01',
        endDate: '2024-12-31',
        estimatedDuration: 10
      };

      // Step 3: Add Contributors (simulating user input)
      const contributors = [
        {
          email: '<EMAIL>',
          fullName: 'Sarah Johnson',
          role: 'Lead Developer',
          revenueShare: 40,
          contributionType: 'development',
          skills: ['Python', 'AI/ML', 'System Architecture']
        },
        {
          email: '<EMAIL>', 
          fullName: 'Mike Chen',
          role: 'Frontend Developer',
          revenueShare: 30,
          contributionType: 'development',
          skills: ['React', 'TypeScript', 'UI/UX']
        },
        {
          email: '<EMAIL>',
          fullName: 'Dr. Emily Rodriguez',
          role: 'Data Scientist',
          revenueShare: 30,
          contributionType: 'research',
          skills: ['Machine Learning', 'Data Analysis', 'Python']
        }
      ];

      // Step 4: Generate Agreement using actual system
      const currentUser = contributors[0]; // Simulating first contributor signing
      
      try {
        const templateText = await generator.loadTemplate('STANDARD');
        const agreement = await generator.generateAgreement(templateText, venture, {
          contributors,
          currentUser,
          fullName: currentUser.fullName,
          allianceInfo: alliance
        });

        generatedAgreements.push({
          scenario: 'Software Development - Tech Alliance',
          alliance,
          venture,
          contributors,
          currentUser,
          agreement,
          timestamp: new Date().toISOString()
        });

        console.log('✅ Agreement generated successfully');
        console.log(`📄 Agreement length: ${agreement.length} characters`);
        
        // Save for detailed analysis
        const outputDir = path.join(__dirname, 'output/agreements');
        if (!fs.existsSync(outputDir)) {
          fs.mkdirSync(outputDir, { recursive: true });
        }
        
        const filename = `scenario-1-software-dev-${new Date().toISOString().split('T')[0]}.md`;
        fs.writeFileSync(path.join(outputDir, filename), agreement);
        console.log(`💾 Saved to: ${filename}`);

      } catch (error) {
        console.error('❌ Agreement generation failed:', error);
        throw error;
      }
    });

    test('Scenario 2: Game Development Venture - Creative Alliance', async () => {
      console.log('\n🧪 TESTING SCENARIO 2: Game Development Venture');
      
      const alliance = {
        id: 'alliance_game_001',
        name: 'Indie Game Creators Alliance',
        industry: 'ENTERTAINMENT',
        collaborationType: 'game_development',
        jurisdiction: 'California',
        governingLaw: 'California',
        disputeResolution: 'arbitration',
        revenueModel: 'revenue_sharing',
        ipOwnershipModel: 'shared_ownership',
        companyInfo: {
          legalName: 'GameStudio Collective LLC',
          state: 'California',
          address: '456 Creative Blvd, Los Angeles, CA 90210',
          signerName: 'Alex Thompson',
          signerTitle: 'Creative Director',
          billingEmail: '<EMAIL>'
        }
      };

      const venture = {
        id: 'venture_game_001',
        allianceId: alliance.id,
        name: 'Mystic Realms RPG',
        description: 'Fantasy role-playing game with immersive storytelling and strategic combat',
        projectType: 'game',
        scope: 'Full 3D RPG with multiplayer capabilities',
        objectives: ['Create engaging storyline', 'Develop combat system', 'Build multiplayer infrastructure'],
        deliverables: [
          { type: 'software', name: 'Game Engine', description: 'Core game mechanics and systems' },
          { type: 'design', name: 'Art Assets', description: '3D models, textures, animations' },
          { type: 'audio', name: 'Sound Design', description: 'Music and sound effects' }
        ],
        milestones: [
          { name: 'Prototype', deadline: '2024-05-01', description: 'Playable prototype' },
          { name: 'Alpha Build', deadline: '2024-08-01', description: 'Feature-complete alpha' },
          { name: 'Beta Release', deadline: '2024-11-01', description: 'Public beta testing' },
          { name: 'Gold Master', deadline: '2025-02-01', description: 'Final release version' }
        ],
        techStack: {
          platforms: ['PC', 'Console'],
          engine: 'Unity',
          languages: ['C#', 'JavaScript'],
          additionalTech: 'Multiplayer networking, AI systems'
        },
        startDate: '2024-02-01',
        endDate: '2025-03-01',
        estimatedDuration: 13
      };

      const contributors = [
        {
          email: '<EMAIL>',
          fullName: 'Jordan Williams',
          role: 'Game Designer',
          revenueShare: 35,
          contributionType: 'design',
          skills: ['Game Design', 'Unity', 'C#']
        },
        {
          email: '<EMAIL>',
          fullName: 'Maya Patel',
          role: '3D Artist',
          revenueShare: 25,
          contributionType: 'art',
          skills: ['3D Modeling', 'Texturing', 'Animation']
        },
        {
          email: '<EMAIL>',
          fullName: 'Chris Anderson',
          role: 'Programmer',
          revenueShare: 25,
          contributionType: 'development',
          skills: ['C#', 'Unity', 'Networking']
        },
        {
          email: '<EMAIL>',
          fullName: 'Luna Martinez',
          role: 'Composer',
          revenueShare: 15,
          contributionType: 'audio',
          skills: ['Music Composition', 'Sound Design', 'Audio Engineering']
        }
      ];

      const currentUser = contributors[0];
      
      try {
        const templateText = await generator.loadTemplate('STANDARD');
        const agreement = await generator.generateAgreement(templateText, venture, {
          contributors,
          currentUser,
          fullName: currentUser.fullName,
          allianceInfo: alliance
        });

        generatedAgreements.push({
          scenario: 'Game Development - Creative Alliance',
          alliance,
          venture,
          contributors,
          currentUser,
          agreement,
          timestamp: new Date().toISOString()
        });

        console.log('✅ Agreement generated successfully');
        console.log(`📄 Agreement length: ${agreement.length} characters`);
        
        const outputDir = path.join(__dirname, 'output/agreements');
        const filename = `scenario-2-game-dev-${new Date().toISOString().split('T')[0]}.md`;
        fs.writeFileSync(path.join(outputDir, filename), agreement);
        console.log(`💾 Saved to: ${filename}`);

      } catch (error) {
        console.error('❌ Agreement generation failed:', error);
        throw error;
      }
    });
  });

  describe('Detailed Agreement Analysis', () => {

    test('Compare Generated Agreements Against Lawyer Template', async () => {
      console.log('\n🔍 PERFORMING DETAILED COMPARISON ANALYSIS');

      expect(generatedAgreements.length).toBeGreaterThan(0);

      for (const testCase of generatedAgreements) {
        console.log(`\n📋 Analyzing: ${testCase.scenario}`);

        const analysis = await performDetailedComparison(
          testCase.agreement,
          lawyerApprovedTemplate,
          testCase
        );

        analysisResults.push({
          scenario: testCase.scenario,
          analysis,
          timestamp: new Date().toISOString()
        });

        // Log critical issues
        if (analysis.criticalIssues.length > 0) {
          console.log(`❌ CRITICAL ISSUES FOUND: ${analysis.criticalIssues.length}`);
          analysis.criticalIssues.forEach((issue, index) => {
            console.log(`   ${index + 1}. ${issue.type}: ${issue.description}`);
          });
        }

        // Log missing sections
        if (analysis.missingSections.length > 0) {
          console.log(`⚠️  MISSING SECTIONS: ${analysis.missingSections.length}`);
          analysis.missingSections.forEach((section, index) => {
            console.log(`   ${index + 1}. ${section}`);
          });
        }

        // Log accuracy score
        console.log(`📊 ACCURACY SCORE: ${analysis.accuracyScore}%`);
        console.log(`📊 COMPLETENESS SCORE: ${analysis.completenessScore}%`);
      }

      // Save comprehensive analysis report
      const reportPath = path.join(__dirname, 'output/analysis-report.json');
      fs.writeFileSync(reportPath, JSON.stringify({
        timestamp: new Date().toISOString(),
        totalScenarios: analysisResults.length,
        results: analysisResults,
        summary: generateAnalysisSummary(analysisResults)
      }, null, 2));

      console.log(`\n📄 Comprehensive analysis report saved to: analysis-report.json`);
    });
  });

  // Helper function to perform detailed comparison
  async function performDetailedComparison(generatedAgreement, lawyerTemplate, testCase) {
    const analysis = {
      criticalIssues: [],
      missingSections: [],
      incorrectVariables: [],
      structuralProblems: [],
      exhibitIssues: [],
      scheduleIssues: [],
      accuracyScore: 0,
      completenessScore: 0
    };

    // Define expected sections from lawyer template
    const expectedSections = [
      'CONTRIBUTOR AGREEMENT',
      'Recitals',
      '1. Definitions',
      '2. Treatment of Confidential Information',
      '3. Ownership of Work Product',
      '4. Non-Disparagement',
      '5. Termination',
      '6. Equitable Remedies',
      '7. Assignment',
      '8. Waivers and Amendments',
      '9. Survival',
      '10. Status as Independent Contractor',
      '11. Representations and Warranties',
      '12. Indemnification',
      '13. Entire Agreement',
      '14. Governing Law',
      '15. Consent to Jurisdiction',
      '16. Settlement of Disputes',
      '17. Titles and Subtitles',
      '18. Opportunity to Consult',
      '19. Gender; Singular and Plural',
      '20. Notice',
      '21. Counterparts',
      'SCHEDULE A',
      'SCHEDULE B'
    ];

    // Check for missing sections
    expectedSections.forEach(section => {
      if (!generatedAgreement.includes(section)) {
        analysis.missingSections.push(section);
      }
    });

    // Check critical company information
    const companyInfo = testCase.alliance.companyInfo;

    // Check if company name is properly replaced
    if (generatedAgreement.includes('[Company Legal Name]') ||
        generatedAgreement.includes('[COMPANY NAME]')) {
      analysis.criticalIssues.push({
        type: 'UNREPLACED_VARIABLE',
        description: 'Company name placeholder not replaced',
        severity: 'CRITICAL'
      });
    }

    // Check if company address is properly replaced
    if (generatedAgreement.includes('[Company Address]')) {
      analysis.criticalIssues.push({
        type: 'UNREPLACED_VARIABLE',
        description: 'Company address placeholder not replaced',
        severity: 'CRITICAL'
      });
    }

    // Check if project name is properly replaced
    if (generatedAgreement.includes('[Project Name]')) {
      analysis.criticalIssues.push({
        type: 'UNREPLACED_VARIABLE',
        description: 'Project name placeholder not replaced',
        severity: 'CRITICAL'
      });
    }

    // Check for hardcoded City of Gamers references (should be user's company)
    if (generatedAgreement.includes('City of Gamers Inc.') &&
        companyInfo.legalName !== 'City of Gamers Inc.') {
      analysis.criticalIssues.push({
        type: 'HARDCODED_COMPANY',
        description: 'Hardcoded City of Gamers Inc. instead of user company',
        severity: 'CRITICAL'
      });
    }

    // Check jurisdiction consistency
    const expectedJurisdiction = testCase.alliance.jurisdiction;
    if (generatedAgreement.includes('Florida') && expectedJurisdiction !== 'Florida') {
      analysis.criticalIssues.push({
        type: 'INCORRECT_JURISDICTION',
        description: `Hardcoded Florida jurisdiction instead of ${expectedJurisdiction}`,
        severity: 'CRITICAL'
      });
    }

    // Check for proper Schedule A content
    if (!generatedAgreement.includes('SCHEDULE A') ||
        !generatedAgreement.includes('Description of Services')) {
      analysis.scheduleIssues.push('Schedule A missing or incomplete');
    }

    // Check for proper Schedule B content
    if (!generatedAgreement.includes('SCHEDULE B') ||
        !generatedAgreement.includes('Description of Consideration')) {
      analysis.scheduleIssues.push('Schedule B missing or incomplete');
    }

    // Check for Exhibit references
    if (generatedAgreement.includes('Exhibit I') || generatedAgreement.includes('Exhibit II')) {
      if (!generatedAgreement.includes('EXHIBIT I') && !generatedAgreement.includes('EXHIBIT II')) {
        analysis.exhibitIssues.push('Exhibit references found but exhibits missing');
      }
    }

    // Calculate accuracy score
    const totalChecks = expectedSections.length + 10; // sections + critical checks
    const passedChecks = expectedSections.length - analysis.missingSections.length +
                        (10 - analysis.criticalIssues.length);
    analysis.accuracyScore = Math.round((passedChecks / totalChecks) * 100);

    // Calculate completeness score
    const requiredElements = ['company info', 'project details', 'schedules', 'signatures'];
    let completedElements = 0;

    if (!generatedAgreement.includes('[Company Legal Name]')) completedElements++;
    if (generatedAgreement.includes(testCase.venture.name)) completedElements++;
    if (generatedAgreement.includes('SCHEDULE A') && generatedAgreement.includes('SCHEDULE B')) completedElements++;
    if (generatedAgreement.includes('COMPANY:') && generatedAgreement.includes('CONTRIBUTOR:')) completedElements++;

    analysis.completenessScore = Math.round((completedElements / requiredElements.length) * 100);

    return analysis;
  }

  // Helper function to generate analysis summary
  function generateAnalysisSummary(results) {
    const summary = {
      totalIssues: 0,
      criticalIssues: 0,
      averageAccuracy: 0,
      averageCompleteness: 0,
      commonIssues: {},
      recommendations: []
    };

    results.forEach(result => {
      summary.totalIssues += result.analysis.criticalIssues.length +
                            result.analysis.missingSections.length +
                            result.analysis.scheduleIssues.length +
                            result.analysis.exhibitIssues.length;

      summary.criticalIssues += result.analysis.criticalIssues.length;
      summary.averageAccuracy += result.analysis.accuracyScore;
      summary.averageCompleteness += result.analysis.completenessScore;

      // Track common issues
      result.analysis.criticalIssues.forEach(issue => {
        summary.commonIssues[issue.type] = (summary.commonIssues[issue.type] || 0) + 1;
      });
    });

    summary.averageAccuracy = Math.round(summary.averageAccuracy / results.length);
    summary.averageCompleteness = Math.round(summary.averageCompleteness / results.length);

    // Generate recommendations
    if (summary.averageAccuracy < 95) {
      summary.recommendations.push('Agreement accuracy is below 95% threshold - critical system fixes needed');
    }
    if (summary.criticalIssues > 0) {
      summary.recommendations.push('Critical issues found - immediate attention required');
    }
    if (summary.commonIssues.UNREPLACED_VARIABLE) {
      summary.recommendations.push('Variable replacement system needs fixing');
    }
    if (summary.commonIssues.HARDCODED_COMPANY) {
      summary.recommendations.push('Remove hardcoded company references');
    }

    return summary;
  }
});

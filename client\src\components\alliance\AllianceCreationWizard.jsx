import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON>dal<PERSON><PERSON><PERSON>, Button, Input, Textarea, Select, SelectItem, Card, CardBody, Chip } from '@heroui/react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';

/**
 * Studio Creation Wizard Component - Multi-step Studio Creation Interface
 * 
 * Features:
 * - Multi-step wizard following exact wireframe specifications
 * - Studio basics, business model, and member setup
 * - Real-time validation and progress tracking
 * - Integration with studio creation APIs
 * - Template selection for quick setup
 */
const AllianceCreationWizard = ({ isOpen, onClose, onSuccess, currentUser }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  
  // Form data state
  const [formData, setFormData] = useState({
    // Step 1: Studio Basics
    name: '',
    description: '',
    industry: '',
    studio_type: 'emerging',
    
    // Step 2: Business Model
    business_model: {
      revenue_sharing: {
        method: 'contribution_based',
        base_percentage: 70
      },
      commission_rate: 15,
      recurring_fee: 0,
      billing_cycle: 'monthly'
    },
    
    // Step 3: Initial Settings
    max_members: 10,
    is_public: true,
    auto_venture_creation: false,
    
    // Step 4: Review
    terms_accepted: false
  });

  const totalSteps = 4;

  // Industry options
  const industries = [
    'Technology',
    'Film & Entertainment',
    'Gaming',
    'Design',
    'Marketing',
    'Consulting',
    'Education',
    'Healthcare',
    'Finance',
    'Other'
  ];

  // Studio type options
  const allianceTypes = [
    { key: 'emerging', label: 'Emerging', description: 'New collaborative group' },
    { key: 'established', label: 'Established', description: 'Existing business entity' },
    { key: 'startup', label: 'Startup', description: 'Early-stage company' },
    { key: 'collective', label: 'Collective', description: 'Creative collaborative' }
  ];

  // Handle form field changes
  const handleFieldChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Handle business model changes
  const handleBusinessModelChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      business_model: {
        ...prev.business_model,
        [field]: value
      }
    }));
  };

  // Validate current step
  const validateStep = (step) => {
    switch (step) {
      case 1:
        return formData.name.trim() && formData.description.trim() && formData.industry && formData.studio_type;
      case 2:
        return true; // Business model has defaults
      case 3:
        return formData.max_members > 0;
      case 4:
        return formData.terms_accepted;
      default:
        return false;
    }
  };

  // Handle next step
  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    } else {
      toast.error('Please fill in all required fields');
    }
  };

  // Handle previous step
  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // Handle studio creation
  const handleCreateAlliance = async () => {
    try {
      setLoading(true);
      
      const allianceData = {
        name: formData.name,
        description: formData.description,
        industry: formData.industry,
        studio_type: formData.studio_type,
        business_model: formData.business_model,
        max_members: formData.max_members,
        is_public: formData.is_public,
        auto_venture_creation: formData.auto_venture_creation
      };

      const response = await fetch('/.netlify/functions/studios', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(allianceData)
      });

      if (response.ok) {
        const result = await response.json();
        toast.success('Studio created successfully!');
        onSuccess(result.studio);
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create studio');
      }
      
    } catch (error) {
      console.error('Error creating studio:', error);
      toast.error(error.message || 'Failed to create studio');
    } finally {
      setLoading(false);
    }
  };

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold mb-2">Studio Basics</h3>
              <p className="text-default-600">Set up the foundation of your studio</p>
            </div>

            <Input
              label="Studio Name"
              placeholder="Enter studio name"
              value={formData.name}
              onChange={(e) => handleFieldChange('name', e.target.value)}
              isRequired
              variant="bordered"
            />

            <Textarea
              label="Description"
              placeholder="Describe your studio's purpose and goals"
              value={formData.description}
              onChange={(e) => handleFieldChange('description', e.target.value)}
              isRequired
              variant="bordered"
              minRows={3}
            />

            <Select
              label="Industry"
              placeholder="Select your industry"
              selectedKeys={formData.industry ? [formData.industry] : []}
              onSelectionChange={(keys) => handleFieldChange('industry', Array.from(keys)[0])}
              isRequired
              variant="bordered"
            >
              {industries.map((industry) => (
                <SelectItem key={industry} value={industry}>
                  {industry}
                </SelectItem>
              ))}
            </Select>

            <div>
              <label className="block text-sm font-medium mb-3">Studio Type</label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {allianceTypes.map((type) => (
                  <Card
                    key={type.key}
                    className={`cursor-pointer transition-all ${
                      formData.studio_type === type.key
                        ? 'ring-2 ring-primary bg-primary-50 dark:bg-primary-900/20'
                        : 'hover:shadow-md'
                    }`}
                    onClick={() => handleFieldChange('studio_type', type.key)}
                  >
                    <CardBody className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold">{type.label}</h4>
                        {formData.studio_type === type.key && (
                          <Chip color="primary" size="sm">Selected</Chip>
                        )}
                      </div>
                      <p className="text-sm text-default-600">{type.description}</p>
                    </CardBody>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold mb-2">Business Model</h3>
              <p className="text-default-600">Configure how your studio handles revenue</p>
            </div>

            <Card className="bg-blue-50 dark:bg-blue-900/20">
              <CardBody className="p-4">
                <h4 className="font-semibold mb-2">Revenue Sharing</h4>
                <div className="space-y-4">
                  <Select
                    label="Sharing Method"
                    selectedKeys={[formData.business_model.revenue_sharing.method]}
                    onSelectionChange={(keys) => 
                      handleFieldChange('business_model.revenue_sharing.method', Array.from(keys)[0])
                    }
                    variant="bordered"
                  >
                    <SelectItem key="contribution_based" value="contribution_based">
                      Contribution Based
                    </SelectItem>
                    <SelectItem key="equal_split" value="equal_split">
                      Equal Split
                    </SelectItem>
                    <SelectItem key="role_based" value="role_based">
                      Role Based
                    </SelectItem>
                  </Select>

                  <Input
                    type="number"
                    label="Base Percentage"
                    placeholder="70"
                    value={formData.business_model.revenue_sharing.base_percentage}
                    onChange={(e) => 
                      handleFieldChange('business_model.revenue_sharing.base_percentage', parseInt(e.target.value))
                    }
                    endContent={<span className="text-default-400">%</span>}
                    variant="bordered"
                  />
                </div>
              </CardBody>
            </Card>

            <Card className="bg-green-50 dark:bg-green-900/20">
              <CardBody className="p-4">
                <h4 className="font-semibold mb-2">Commission & Fees</h4>
                <div className="space-y-4">
                  <Input
                    type="number"
                    label="Commission Rate"
                    placeholder="15"
                    value={formData.business_model.commission_rate}
                    onChange={(e) => handleBusinessModelChange('commission_rate', parseInt(e.target.value))}
                    endContent={<span className="text-default-400">%</span>}
                    variant="bordered"
                  />

                  <Input
                    type="number"
                    label="Monthly Fee (Optional)"
                    placeholder="0"
                    value={formData.business_model.recurring_fee}
                    onChange={(e) => handleBusinessModelChange('recurring_fee', parseFloat(e.target.value))}
                    startContent={<span className="text-default-400">$</span>}
                    variant="bordered"
                  />
                </div>
              </CardBody>
            </Card>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold mb-2">Studio Settings</h3>
              <p className="text-default-600">Configure your studio preferences</p>
            </div>

            <Input
              type="number"
              label="Maximum Members"
              placeholder="10"
              value={formData.max_members}
              onChange={(e) => handleFieldChange('max_members', parseInt(e.target.value))}
              variant="bordered"
              min={1}
              max={100}
            />

            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Public Studio</h4>
                  <p className="text-sm text-default-600">Allow others to discover and request to join</p>
                </div>
                <Button
                  color={formData.is_public ? 'success' : 'default'}
                  variant={formData.is_public ? 'solid' : 'bordered'}
                  onClick={() => handleFieldChange('is_public', !formData.is_public)}
                >
                  {formData.is_public ? 'Public' : 'Private'}
                </Button>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Auto Project Creation</h4>
                  <p className="text-sm text-default-600">Allow members to create projects automatically</p>
                </div>
                <Button
                  color={formData.auto_venture_creation ? 'success' : 'default'}
                  variant={formData.auto_venture_creation ? 'solid' : 'bordered'}
                  onClick={() => handleFieldChange('auto_venture_creation', !formData.auto_venture_creation)}
                >
                  {formData.auto_venture_creation ? 'Enabled' : 'Disabled'}
                </Button>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold mb-2">Review & Create</h3>
              <p className="text-default-600">Review your studio configuration</p>
            </div>

            <Card>
              <CardBody className="p-6 space-y-4">
                <div>
                  <h4 className="font-semibold text-lg">{formData.name}</h4>
                  <p className="text-default-600">{formData.description}</p>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-default-500">Industry:</span>
                    <span className="ml-2 font-medium">{formData.industry}</span>
                  </div>
                  <div>
                    <span className="text-default-500">Type:</span>
                    <span className="ml-2 font-medium">{formData.studio_type}</span>
                  </div>
                  <div>
                    <span className="text-default-500">Max Members:</span>
                    <span className="ml-2 font-medium">{formData.max_members}</span>
                  </div>
                  <div>
                    <span className="text-default-500">Visibility:</span>
                    <span className="ml-2 font-medium">{formData.is_public ? 'Public' : 'Private'}</span>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <h5 className="font-medium mb-2">Business Model</h5>
                  <div className="text-sm space-y-1">
                    <div>Revenue Sharing: {formData.business_model.revenue_sharing.base_percentage}% ({formData.business_model.revenue_sharing.method})</div>
                    <div>Commission Rate: {formData.business_model.commission_rate}%</div>
                    {formData.business_model.recurring_fee > 0 && (
                      <div>Monthly Fee: ${formData.business_model.recurring_fee}</div>
                    )}
                  </div>
                </div>
              </CardBody>
            </Card>

            <div className="flex items-center gap-3 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <input
                type="checkbox"
                id="terms"
                checked={formData.terms_accepted}
                onChange={(e) => handleFieldChange('terms_accepted', e.target.checked)}
                className="rounded"
              />
              <label htmlFor="terms" className="text-sm">
                I agree to the studio terms and conditions and understand the revenue sharing model
              </label>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="2xl"
      isDismissable={!loading}
      hideCloseButton={loading}
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h2 className="text-2xl font-bold">Create New Studio</h2>
          <div className="flex items-center gap-2">
            <span className="text-default-600 font-normal">Step {currentStep} of {totalSteps}</span>
            <div className="flex-1 bg-default-200 rounded-full h-2 ml-4">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${(currentStep / totalSteps) * 100}%` }}
              />
            </div>
          </div>
        </ModalHeader>
        
        <ModalBody>
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {renderStepContent()}
          </motion.div>
        </ModalBody>
        
        <ModalFooter>
          <div className="flex justify-between w-full">
            <Button 
              color="danger" 
              variant="flat" 
              onPress={onClose}
              disabled={loading}
            >
              Cancel
            </Button>
            
            <div className="flex gap-2">
              {currentStep > 1 && (
                <Button 
                  color="default" 
                  variant="bordered"
                  onPress={handlePrevious}
                  disabled={loading}
                >
                  Previous
                </Button>
              )}
              
              {currentStep < totalSteps ? (
                <Button 
                  color="primary" 
                  onPress={handleNext}
                  disabled={!validateStep(currentStep) || loading}
                >
                  Next
                </Button>
              ) : (
                <Button 
                  color="success" 
                  onPress={handleCreateAlliance}
                  disabled={!validateStep(currentStep) || loading}
                  isLoading={loading}
                >
                  Create Studio
                </Button>
              )}
            </div>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default AllianceCreationWizard;

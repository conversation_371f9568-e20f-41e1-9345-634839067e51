// AllianceList - Enhanced studio listing with discovery and management
// Replaces basic team listing with comprehensive studio management
import React, { useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Card, CardBody, Button, Badge, Input, Select, SelectItem } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { 
  Crown, 
  Users, 
  Plus, 
  Search, 
  Filter,
  Building,
  TrendingUp,
  Calendar,
  MapPin,
  Star,
  Shield,
  User
} from 'lucide-react';

const AllianceList = () => {
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();
  const [studios, setAlliances] = useState([]);
  const [publicAlliances, setPublicAlliances] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterRole, setFilterRole] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('my-studios');

  useEffect(() => {
    if (currentUser) {
      loadAlliancesData();
    }
  }, [currentUser]);

  const loadAlliancesData = async () => {
    try {
      setIsLoading(true);
      await Promise.all([
        loadUserAlliances(),
        loadPublicAlliances()
      ]);
    } catch (error) {
      console.error('Error loading studios data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadUserAlliances = async () => {
    try {
      const { data, error } = await supabase
        .from('team_members')
        .select(`
          id,
          role,
          status,
          joined_at,
          team:team_id(
            id,
            name,
            description,
            studio_type,
            industry,
            is_public,
            created_at,
            team_members(id, role, status)
          )
        `)
        .eq('user_id', currentUser.id)
        .eq('status', 'active')
        .order('joined_at', { ascending: false });

      if (error) throw error;
      
      const processedAlliances = data.map(membership => ({
        ...membership.team,
        userRole: membership.role,
        userStatus: membership.status,
        joinedAt: membership.joined_at,
        memberCount: membership.team.team_members?.length || 0
      }));

      setAlliances(processedAlliances);
    } catch (error) {
      console.error('Error loading user studios:', error);
    }
  };

  const loadPublicAlliances = async () => {
    try {
      const { data, error } = await supabase
        .from('teams')
        .select(`
          id,
          name,
          description,
          studio_type,
          industry,
          created_at,
          team_members(id, role, status)
        `)
        .eq('is_public', true)
        .not('id', 'in', `(${studios.map(a => a.id).join(',') || 'null'})`)
        .order('created_at', { ascending: false })
        .limit(12);

      if (error) throw error;
      
      const processedPublicAlliances = data.map(studio => ({
        ...studio,
        memberCount: studio.team_members?.length || 0
      }));

      setPublicAlliances(processedPublicAlliances);
    } catch (error) {
      console.error('Error loading public studios:', error);
    }
  };

  const getAllianceTypeInfo = (type) => {
    const types = {
      emerging: { icon: '🌱', title: 'Emerging', color: 'text-green-600', badgeColor: 'success' },
      established: { icon: '🏰', title: 'Established', color: 'text-blue-600', badgeColor: 'primary' },
      solo: { icon: '⚔️', title: 'Solo', color: 'text-purple-600', badgeColor: 'secondary' }
    };
    return types[type] || types.emerging;
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case 'founder': return <Crown className="text-yellow-500" size={16} />;
      case 'owner': return <Crown className="text-yellow-500" size={16} />;
      case 'admin': return <Shield className="text-blue-500" size={16} />;
      default: return <User className="text-gray-500" size={16} />;
    }
  };

  const getRoleBadgeColor = (role) => {
    switch (role) {
      case 'founder': return 'warning';
      case 'owner': return 'warning';
      case 'admin': return 'primary';
      default: return 'default';
    }
  };

  const filteredAlliances = studios.filter(studio => {
    const matchesSearch = !searchQuery || 
      studio.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      studio.description?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesType = filterType === 'all' || studio.studio_type === filterType;
    const matchesRole = filterRole === 'all' || studio.userRole === filterRole;
    
    return matchesSearch && matchesType && matchesRole;
  });

  const filteredPublicAlliances = publicAlliances.filter(studio => {
    const matchesSearch = !searchQuery || 
      studio.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      studio.description?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesType = filterType === 'all' || studio.studio_type === filterType;
    
    return matchesSearch && matchesType;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="studio-list space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Studios</h1>
          <p className="text-gray-600">Manage your professional studios and discover new opportunities</p>
        </div>
        <Button 
          color="primary" 
          size="lg"
          startContent={<Plus size={20} />}
          onPress={() => navigate('/studios/create')}
        >
          Create Studio
        </Button>
      </div>

      {/* Tab Navigation */}
      <div className="flex items-center gap-4 border-b">
        <button
          className={`pb-2 px-1 border-b-2 transition-colors ${
            activeTab === 'my-studios' 
              ? 'border-primary text-primary font-medium' 
              : 'border-transparent text-gray-600 hover:text-gray-900'
          }`}
          onClick={() => setActiveTab('my-studios')}
        >
          My Studios ({studios.length})
        </button>
        <button
          className={`pb-2 px-1 border-b-2 transition-colors ${
            activeTab === 'discover' 
              ? 'border-primary text-primary font-medium' 
              : 'border-transparent text-gray-600 hover:text-gray-900'
          }`}
          onClick={() => setActiveTab('discover')}
        >
          Discover Studios
        </button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardBody className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <Input
              placeholder="Search studios..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              startContent={<Search size={18} />}
              className="flex-1"
            />
            <Select
              placeholder="Filter by type"
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="md:w-48"
            >
              <SelectItem key="all" value="all">All Types</SelectItem>
              <SelectItem key="emerging" value="emerging">🌱 Emerging</SelectItem>
              <SelectItem key="established" value="established">🏰 Established</SelectItem>
              <SelectItem key="solo" value="solo">⚔️ Solo</SelectItem>
            </Select>
            {activeTab === 'my-studios' && (
              <Select
                placeholder="Filter by role"
                value={filterRole}
                onChange={(e) => setFilterRole(e.target.value)}
                className="md:w-48"
              >
                <SelectItem key="all" value="all">All Roles</SelectItem>
                <SelectItem key="founder" value="founder">Founder</SelectItem>
                <SelectItem key="admin" value="admin">Admin</SelectItem>
                <SelectItem key="member" value="member">Member</SelectItem>
              </Select>
            )}
          </div>
        </CardBody>
      </Card>

      {/* Studio Grid */}
      {activeTab === 'my-studios' ? (
        <div className="space-y-6">
          {filteredAlliances.length === 0 ? (
            <Card>
              <CardBody className="p-12 text-center">
                <Building size={48} className="mx-auto mb-4 text-gray-400" />
                <h3 className="text-xl font-semibold mb-2">No Studios Found</h3>
                <p className="text-gray-600 mb-4">
                  {studios.length === 0 
                    ? "You haven't joined any studios yet. Create your first studio or discover existing ones."
                    : "No studios match your current filters. Try adjusting your search criteria."
                  }
                </p>
                <Button color="primary" onPress={() => navigate('/studios/create')}>
                  Create Your First Studio
                </Button>
              </CardBody>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredAlliances.map((studio) => {
                const typeInfo = getAllianceTypeInfo(studio.studio_type);
                return (
                  <motion.div
                    key={studio.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    whileHover={{ y: -4 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer" onPress={() => navigate(`/studios/${studio.id}`)}>
                      <CardBody className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <span className="text-2xl">{typeInfo.icon}</span>
                              <h3 className="text-lg font-semibold line-clamp-1">{studio.name}</h3>
                            </div>
                            <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                              {studio.description || 'No description available'}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-2">
                            {getRoleIcon(studio.userRole)}
                            <Badge color={getRoleBadgeColor(studio.userRole)} size="sm">
                              {studio.userRole}
                            </Badge>
                          </div>
                          <Badge color={typeInfo.badgeColor} variant="flat" size="sm">
                            {typeInfo.title}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center justify-between text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Users size={14} />
                            <span>{studio.memberCount} members</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar size={14} />
                            <span>Joined {new Date(studio.joinedAt).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  </motion.div>
                );
              })}
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-6">
          {filteredPublicAlliances.length === 0 ? (
            <Card>
              <CardBody className="p-12 text-center">
                <Search size={48} className="mx-auto mb-4 text-gray-400" />
                <h3 className="text-xl font-semibold mb-2">No Public Studios Found</h3>
                <p className="text-gray-600">
                  No public studios match your search criteria. Try adjusting your filters.
                </p>
              </CardBody>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredPublicAlliances.map((studio) => {
                const typeInfo = getAllianceTypeInfo(studio.studio_type);
                return (
                  <motion.div
                    key={studio.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    whileHover={{ y: -4 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Card className="h-full hover:shadow-lg transition-shadow">
                      <CardBody className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <span className="text-2xl">{typeInfo.icon}</span>
                              <h3 className="text-lg font-semibold line-clamp-1">{studio.name}</h3>
                            </div>
                            <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                              {studio.description || 'No description available'}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between mb-4">
                          <Badge color={typeInfo.badgeColor} variant="flat" size="sm">
                            {typeInfo.title}
                          </Badge>
                          {studio.industry && (
                            <Badge color="default" variant="flat" size="sm">
                              {studio.industry}
                            </Badge>
                          )}
                        </div>
                        
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-1 text-sm text-gray-600">
                            <Users size={14} />
                            <span>{studio.memberCount} members</span>
                          </div>
                          <div className="flex items-center gap-1 text-sm text-gray-600">
                            <Calendar size={14} />
                            <span>{new Date(studio.created_at).toLocaleDateString()}</span>
                          </div>
                        </div>
                        
                        <Button color="primary" variant="light" className="w-full">
                          Request to Join
                        </Button>
                      </CardBody>
                    </Card>
                  </motion.div>
                );
              })}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AllianceList;

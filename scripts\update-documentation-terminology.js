#!/usr/bin/env node

/**
 * Update Documentation Terminology Script
 * Updates documentation files to use new terminology
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Documentation terminology mappings
const DOC_MAPPINGS = {
  // Main terminology
  'Alliance System': 'Studio System',
  'alliance system': 'studio system',
  'Alliance Management': 'Studio Management',
  'alliance management': 'studio management',
  'Alliance Dashboard': 'Studio Dashboard',
  'alliance dashboard': 'studio dashboard',
  
  'Venture System': 'Project System',
  'venture system': 'project system',
  'Venture Management': 'Project Management',
  'venture management': 'project management',
  'Venture Creation': 'Project Creation',
  'venture creation': 'project creation',
  
  'Quest System': 'Mission System',
  'quest system': 'mission system',
  'Quest Board': 'Mission Board',
  'quest board': 'mission board',
  'Quest Management': 'Mission Management',
  'quest management': 'mission management',

  // File and directory references
  'alliance-system.md': 'studio-system.md',
  'alliance-dashboard.md': 'studio-dashboard.md',
  'venture-system.md': 'project-system.md',
  'quest-system.md': 'mission-system.md',
  
  // API references
  '/api/alliances': '/api/studios',
  '/api/ventures': '/api/projects',
  '/api/quest-system': '/api/missions',
  
  // Database references
  'alliance_invitations': 'studio_invitations',
  'alliance_preferences': 'studio_preferences',
  'user_quests': 'user_missions',
  
  // Implementation status
  'Alliance system implementation': 'Studio system implementation',
  'Venture system implementation': 'Project system implementation',
  'Quest system implementation': 'Mission system implementation',

  // Descriptions
  'gamified, flexible organizational structure': 'gamified, flexible creative studio structure',
  'traditional "Teams" concept': 'traditional "Teams" concept',
  'gaming collaboratives': 'creative collaboratives',
  'established businesses': 'established creative businesses',
  'professional network': 'creative studio network',
  'business compliance': 'creative business compliance'
};

// Documentation directories and files to update
const DOC_PATHS = [
  'docs',
  'README.md',
  'TERMINOLOGY_UPDATE_SUMMARY.md'
];

function updateDocContent(content) {
  let updatedContent = content;
  
  // Apply documentation mappings
  for (const [oldTerm, newTerm] of Object.entries(DOC_MAPPINGS)) {
    // Use global replacement for documentation
    const regex = new RegExp(oldTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
    updatedContent = updatedContent.replace(regex, newTerm);
  }
  
  return updatedContent;
}

function findDocFiles(dir, extensions = ['.md', '.txt', '.rst']) {
  const files = [];
  
  function scanDirectory(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // Skip hidden directories and node_modules
          if (!item.startsWith('.') && item !== 'node_modules') {
            scanDirectory(fullPath);
          }
        } else if (stat.isFile()) {
          // Check if file has documentation extension
          if (extensions.some(ext => item.endsWith(ext))) {
            files.push(fullPath);
          }
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not scan directory ${currentDir}: ${error.message}`);
    }
  }
  
  scanDirectory(dir);
  return files;
}

async function updateDocumentation() {
  console.log('📚 Updating Documentation Terminology...');
  console.log('=====================================\n');

  const projectRoot = path.join(__dirname, '..');
  let updatedFiles = 0;
  let totalFiles = 0;

  console.log('1️⃣ Scanning for documentation files...');
  
  const filesToUpdate = new Set();
  
  // Add specific files
  DOC_PATHS.forEach(docPath => {
    const fullPath = path.join(projectRoot, docPath);
    
    if (fs.existsSync(fullPath)) {
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Scan directory for documentation files
        const foundFiles = findDocFiles(fullPath);
        foundFiles.forEach(file => filesToUpdate.add(file));
      } else if (stat.isFile()) {
        // Add individual file
        filesToUpdate.add(fullPath);
      }
    }
  });

  console.log(`   Found ${filesToUpdate.size} documentation files`);

  console.log('\n2️⃣ Updating documentation files...');
  
  for (const filePath of filesToUpdate) {
    try {
      totalFiles++;
      const relativePath = path.relative(projectRoot, filePath);
      
      // Read current content
      const originalContent = fs.readFileSync(filePath, 'utf8');
      
      // Apply documentation updates
      const updatedContent = updateDocContent(originalContent);
      
      // Check if content actually changed
      if (originalContent !== updatedContent) {
        // Write updated content
        fs.writeFileSync(filePath, updatedContent, 'utf8');
        console.log(`   ✅ Updated: ${relativePath}`);
        updatedFiles++;
      } else {
        console.log(`   ⏭️ No changes: ${relativePath}`);
      }
      
    } catch (error) {
      console.error(`   ❌ Error updating ${filePath}: ${error.message}`);
    }
  }

  console.log('\n📊 Documentation Update Summary:');
  console.log('=====================================');
  console.log(`   Total files processed: ${totalFiles}`);
  console.log(`   Files updated: ${updatedFiles}`);
  console.log(`   Files unchanged: ${totalFiles - updatedFiles}`);

  console.log('\n🎯 Documentation Updates Applied:');
  console.log('=====================================');
  console.log('   ✅ System documentation updated');
  console.log('   ✅ API documentation updated');
  console.log('   ✅ Implementation guides updated');
  console.log('   ✅ README and summary files updated');

  console.log('\n📋 Final Terminology Update Status:');
  console.log('=====================================');
  console.log('✅ Database migration applied');
  console.log('✅ Routing and navigation updated');
  console.log('✅ Component files updated');
  console.log('✅ UI text updated');
  console.log('✅ Component-level terminology updated');
  console.log('✅ Documentation updated');
  console.log('🎉 Terminology update complete!');

  console.log('\n🚀 Ready for Testing:');
  console.log('=====================================');
  console.log('1. Start the development server: npm run dev');
  console.log('2. Test new routes:');
  console.log('   • /studios (should show Studio List)');
  console.log('   • /missions (should show Mission Board)');
  console.log('3. Test backward compatibility:');
  console.log('   • /alliances (should redirect to /studios)');
  console.log('   • /quests (should redirect to /missions)');
  console.log('4. Verify all UI text uses new terminology');
  console.log('5. Test component functionality');
}

// Main execution
updateDocumentation().catch(console.error);

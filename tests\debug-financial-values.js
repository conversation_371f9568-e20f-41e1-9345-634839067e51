/**
 * Debug Financial Values Test
 * 
 * This test specifically checks if the financial values are being properly
 * substituted in the generated agreements.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 DEBUG FINANCIAL VALUES TEST');
console.log('=' .repeat(50));

// Read the generated agreement
const agreementPath = path.join(__dirname, 'fixed-platform-vota_project.md');
const agreement = fs.readFileSync(agreementPath, 'utf8');

console.log('📄 Checking financial values in generated agreement...\n');

// Test data
const projectData = {
  revenueShare: 33,
  payoutThreshold: 100000,
  maxPayment: 1000000
};

// Check for specific patterns
const checks = [
  {
    name: 'Revenue Share Pattern',
    pattern: new RegExp(`share of ${projectData.revenueShare}% of post-expense Revenue`),
    expected: `share of 33% of post-expense Revenue`,
    found: false
  },
  {
    name: 'Payout Threshold Pattern', 
    pattern: new RegExp(`\\$${projectData.payoutThreshold.toLocaleString()} in post-expense Revenue`),
    expected: `$100,000 in post-expense Revenue`,
    found: false
  },
  {
    name: 'Max Payment Pattern',
    pattern: new RegExp(`\\$${projectData.maxPayment.toLocaleString()} per Developer`),
    expected: `$1,000,000 per Developer`,
    found: false
  }
];

// Check each pattern
checks.forEach(check => {
  check.found = check.pattern.test(agreement);
  console.log(`${check.found ? '✅' : '❌'} ${check.name}`);
  console.log(`   Expected: "${check.expected}"`);
  
  if (!check.found) {
    // Find what's actually there
    const lines = agreement.split('\n');
    const relevantLines = lines.filter(line => 
      line.includes('share of') || 
      line.includes('Threshold for Payout') || 
      line.includes('Maximum Individual Payment')
    );
    
    console.log(`   Actually found:`);
    relevantLines.forEach(line => {
      console.log(`     "${line.trim()}"`);
    });
  }
  console.log('');
});

// Check for variable placeholders that weren't replaced
console.log('🔍 Checking for unreplaced variable placeholders...\n');

const variablePlaceholders = [
  '[Revenue Share]',
  '[Payout Threshold]', 
  '[Max Payment]'
];

variablePlaceholders.forEach(placeholder => {
  const found = agreement.includes(placeholder);
  console.log(`${found ? '⚠️' : '✅'} ${placeholder}: ${found ? 'STILL PRESENT (not replaced)' : 'properly replaced'}`);
});

// Show the actual financial section
console.log('\n📋 ACTUAL FINANCIAL SECTION:');
console.log('=' .repeat(40));

const lines = agreement.split('\n');
const startIndex = lines.findIndex(line => line.includes('Revenue Share Percentage'));
const endIndex = lines.findIndex((line, index) => index > startIndex && line.includes('Payment Schedule'));

if (startIndex !== -1 && endIndex !== -1) {
  const financialSection = lines.slice(startIndex, endIndex + 2).join('\n');
  console.log(financialSection);
} else {
  console.log('❌ Could not find financial section');
}

console.log('\n🎯 SUMMARY:');
console.log('=' .repeat(30));

const passedChecks = checks.filter(c => c.found).length;
const totalChecks = checks.length;

console.log(`✅ Passed: ${passedChecks}/${totalChecks} financial value checks`);
console.log(`📊 Financial accuracy: ${Math.round((passedChecks / totalChecks) * 100)}%`);

if (passedChecks < totalChecks) {
  console.log('\n⚠️  ISSUE IDENTIFIED:');
  console.log('The platform is generating agreements but financial values are not being substituted.');
  console.log('This indicates the NewAgreementGenerator variable replacement logic needs to be fixed.');
} else {
  console.log('\n🎉 All financial values are properly substituted!');
}

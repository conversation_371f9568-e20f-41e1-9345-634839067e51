import React, { useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import AllianceDashboard from '../../components/alliance/AllianceDashboard';

/**
 * Studio Page - Main Studio Management Interface
 * 
 * This page provides the complete studio management experience including:
 * - Studio creation and configuration
 * - Member management and invitations
 * - Business model setup and revenue sharing
 * - Project management and project tracking
 * - Treasury and financial overview
 * - Analytics and performance metrics
 * 
 * Features:
 * - Bento grid layout following exact wireframe specifications
 * - Real-time data integration with backend APIs
 * - Complete studio lifecycle management
 * - Professional networking and collaboration tools
 */
const AlliancePage = () => {
  const { currentUser, loading } = useContext(UserContext);

  // Show loading state while authentication is being verified
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-default-600">Loading studio system...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!currentUser) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-6xl mb-6">🏰</div>
          <h2 className="text-2xl font-bold mb-4">Studio Access Required</h2>
          <p className="text-default-600 mb-8 max-w-md mx-auto">
            Please log in to access the studio management system and collaborate with other professionals.
          </p>
          <button
            onClick={() => window.location.href = '/login'}
            className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-600 transition-colors"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="studio-page min-h-screen bg-background">
      {/* Page Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white py-8 mb-8">
        <div className="container mx-auto px-6">
          <div className="flex items-center gap-4">
            <span className="text-5xl">🏰</span>
            <div>
              <h1 className="text-4xl font-bold mb-2">Studio Management</h1>
              <p className="text-purple-100 text-lg">
                Build your creative studio, manage projects, and collaborate with creative professionals
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 pb-12">
        <AllianceDashboard className="w-full" />
      </div>

      {/* Footer Info */}
      <div className="bg-default-50 dark:bg-default-900 py-8 mt-12">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-3xl mb-2">🤝</div>
              <h3 className="font-semibold mb-2">Professional Networking</h3>
              <p className="text-sm text-default-600">
                Connect with talented professionals and build lasting business relationships
              </p>
            </div>
            <div>
              <div className="text-3xl mb-2">💰</div>
              <h3 className="font-semibold mb-2">Revenue Sharing</h3>
              <p className="text-sm text-default-600">
                Transparent, automated revenue distribution based on contribution and agreements
              </p>
            </div>
            <div>
              <div className="text-3xl mb-2">🎯</div>
              <h3 className="font-semibold mb-2">Project Management</h3>
              <p className="text-sm text-default-600">
                Create and manage projects with clear goals, timelines, and success metrics
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AlliancePage;

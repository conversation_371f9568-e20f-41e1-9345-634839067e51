/**
 * Simple 1:1 Template Recreation Test
 * 
 * A basic test to validate the platform's ability to recreate
 * the lawyer-approved template with VOTA data.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🎯 SIMPLE 1:1 TEMPLATE RECREATION TEST');
console.log('=' .repeat(50));

/**
 * VOTA data from lawyer-approved template
 */
const VOTA_DATA = {
  name: 'Village of The Ages',
  description: 'A village simulation game where players guide communities through historical progressions and manage resource-based challenges',
  project_type: 'game',
  company_name: 'City of Gamers Inc.',
  address: '1205 43rd Street, Suite B, Orlando, Florida 32839',
  contact_email: '<EMAIL>',
  features: 'The game features dynamic challenges based on resource scarcity and natural disasters, creating an engaging gameplay loop.',
  coreFeatures: `1. **Village Building & Management**
   - Resource gathering and management
   - Building placement and construction
   - Population growth and management
   - Cultural evolution systems

2. **Historical Progression**
   - Players guide their community through multiple historical eras
   - Technology tree advancement
   - Cultural and social development
   - Architectural evolution

3. **Resource Management**
   - Dynamic weather systems affecting resources
   - Natural disasters and seasonal challenges
   - Trading systems with neighboring villages
   - Resource scarcity mechanics

4. **Interface Requirements**
   - Intuitive building placement system
   - Resource management dashboard
   - Population statistics and management panels
   - Technology and progression trackers`,
  technicalRequirements: `- Platform: PC (Steam, Epic Games Store)
- Engine: Unreal Engine 5
- Minimum Specs: Standard hardware requirements for the target platforms
- Art Style: Stylized, readable visuals with distinctive aesthetics
- Audio: Atmospheric soundtrack with appropriate sound design
- Version Control: Git-based source control with proper branching strategy`,
  roadmapPhases: `**Phase 1: Core Gameplay Development (Months 1-2)**
- Basic village layout and building system
- Core resource gathering mechanics
- Initial AI for villagers
- Basic UI framework
- First playable prototype with one historical era

**Phase 2: Feature Expansion (Months 2-3)**
- Additional historical eras
- Enhanced resource management systems
- Weather and disaster systems
- Trading mechanics
- Technology progression system

**Phase 3: Polish and Enhancement (Month 4)**
- UI refinement
- Performance optimization
- Additional content (buildings, resources, etc.)
- Balancing and gameplay tuning
- Audio implementation`,
  milestones: [
    {
      title: 'Core Gameplay Development',
      description: 'Basic village layout and building system',
      dueDate: 'Months 1-2'
    },
    {
      title: 'Resource Management System',
      description: 'Implementation of resource scarcity mechanics',
      dueDate: 'Months 3-4'
    },
    {
      title: 'Historical Progression Features',
      description: 'Time-based progression and historical events, architectural evolution through eras',
      dueDate: 'Months 5-6'
    }
  ]
};

/**
 * Critical elements that must be present for 1:1 recreation
 */
const CRITICAL_ELEMENTS = [
  'Village of The Ages',
  'village simulation game',
  'historical progressions',
  'resource-based challenges',
  'Village Building & Management',
  'Historical Progression',
  'Resource Management',
  'Interface Requirements',
  'Technology tree advancement',
  'Architectural evolution',
  'Dynamic weather systems',
  'Natural disasters and seasonal challenges',
  'Trading systems with neighboring villages',
  'Resource scarcity mechanics',
  'Core Gameplay Development',
  'Resource Management System',
  'Historical Progression Features'
];

/**
 * Simple template processor
 */
function processTemplate(template, data) {
  let processed = template;
  
  // Replace basic placeholders
  processed = processed.replace(/\[Project Name\]/g, data.name || '[Project Name]');
  processed = processed.replace(/\[Project Description\]/g, data.description || '[Project Description]');
  processed = processed.replace(/\[Project Features\]/g, data.features || '[Project Features]');
  processed = processed.replace(/\[Project Core Features\]/g, data.coreFeatures || '[Project Core Features]');
  processed = processed.replace(/\[Project Technical Requirements\]/g, data.technicalRequirements || '[Project Technical Requirements]');
  processed = processed.replace(/\[Project Roadmap Phases\]/g, data.roadmapPhases || '[Project Roadmap Phases]');
  
  // Format milestones
  if (data.milestones && data.milestones.length > 0) {
    const formattedMilestones = data.milestones.map((milestone, index) => {
      const title = milestone.title || `Milestone ${index + 1}`;
      const description = milestone.description || '';
      const dueDate = milestone.dueDate || '';

      let formatted = `${index + 1}. **${title}**`;
      if (dueDate) formatted += ` (${dueDate})`;
      if (description) formatted += `\n   - ${description}`;

      return formatted;
    }).join('\n\n');
    
    processed = processed.replace(/\[Project Milestones\]/g, formattedMilestones);
  }
  
  return processed;
}

/**
 * Check accuracy of generated agreement
 */
function checkAccuracy(lawyerTemplate, generatedAgreement) {
  let found = 0;
  const missing = [];
  
  for (const element of CRITICAL_ELEMENTS) {
    if (generatedAgreement.includes(element)) {
      found++;
    } else {
      missing.push(element);
    }
  }
  
  const accuracy = Math.round((found / CRITICAL_ELEMENTS.length) * 100);
  
  return {
    accuracy,
    found,
    total: CRITICAL_ELEMENTS.length,
    missing
  };
}

/**
 * Main test function
 */
async function runSimpleTest() {
  try {
    console.log('\n🚀 Starting simple 1:1 test...\n');
    
    // Load templates
    const lawyerTemplatePath = path.join(__dirname, '../client/public/example-cog-contributor-agreement.md');
    const variableTemplatePath = path.join(__dirname, '../client/public/contributor-agreement-template.md');
    
    console.log('📋 Loading templates...');
    const lawyerTemplate = fs.readFileSync(lawyerTemplatePath, 'utf8');
    const variableTemplate = fs.readFileSync(variableTemplatePath, 'utf8');
    
    console.log('📋 Processing template with VOTA data...');
    const generatedAgreement = processTemplate(variableTemplate, VOTA_DATA);
    
    // Save generated agreement
    const outputPath = path.join(__dirname, 'simple-1to1-output.md');
    fs.writeFileSync(outputPath, generatedAgreement);
    console.log(`📄 Generated agreement saved to: ${outputPath}`);
    
    // Check accuracy
    console.log('\n🔍 Checking accuracy...');
    const accuracyResults = checkAccuracy(lawyerTemplate, generatedAgreement);
    
    // Display results
    console.log(`\n📊 ACCURACY RESULTS:`);
    console.log(`   Found: ${accuracyResults.found}/${accuracyResults.total} critical elements`);
    console.log(`   Accuracy: ${accuracyResults.accuracy}%`);
    console.log(`   Target: 99%`);
    console.log(`   ${accuracyResults.accuracy >= 99 ? '✅ PASSED' : '❌ FAILED'}`);
    
    if (accuracyResults.missing.length > 0) {
      console.log('\n⚠️  Missing elements:');
      accuracyResults.missing.slice(0, 10).forEach(element => {
        console.log(`   • "${element}"`);
      });
      if (accuracyResults.missing.length > 10) {
        console.log(`   • ... and ${accuracyResults.missing.length - 10} more`);
      }
    }
    
    // Save report
    const report = {
      timestamp: new Date().toISOString(),
      accuracy: accuracyResults.accuracy,
      passed: accuracyResults.accuracy >= 99,
      found: accuracyResults.found,
      total: accuracyResults.total,
      missing: accuracyResults.missing,
      outputFile: outputPath
    };
    
    const reportPath = path.join(__dirname, 'simple-1to1-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📊 Report saved to: ${reportPath}`);
    
    return report;
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    throw error;
  }
}

// Run the test
runSimpleTest()
  .then(report => {
    console.log(`\n✅ Test completed with ${report.accuracy}% accuracy`);
    process.exit(report.passed ? 0 : 1);
  })
  .catch(error => {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  });

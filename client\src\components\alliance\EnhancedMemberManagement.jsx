// EnhancedMemberManagement - Advanced member management with role-based permissions
// Implements comprehensive member management following studio system specifications
import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button, Badge, Input, Select, SelectItem, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure, Textarea, Chip } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { 
  Users, 
  Search, 
  Filter, 
  UserPlus, 
  MessageCircle, 
  Settings, 
  Crown,
  Shield,
  User,
  Mail,
  Calendar,
  TrendingUp,
  Award,
  Clock,
  CheckCircle,
  XCircle,
  MoreVertical,
  Edit,
  Trash2
} from 'lucide-react';

const EnhancedMemberManagement = ({ allianceId, userRole, onMemberUpdate }) => {
  const { currentUser } = useContext(UserContext);
  const [members, setMembers] = useState([]);
  const [invitations, setInvitations] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedMember, setSelectedMember] = useState(null);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('member');
  const [inviteMessage, setInviteMessage] = useState('');
  
  const { isOpen: isInviteOpen, onOpen: onInviteOpen, onClose: onInviteClose } = useDisclosure();
  const { isOpen: isMemberOpen, onOpen: onMemberOpen, onClose: onMemberClose } = useDisclosure();

  useEffect(() => {
    if (allianceId) {
      loadMembersData();
    }
  }, [allianceId]);

  const loadMembersData = async () => {
    try {
      setIsLoading(true);
      await Promise.all([
        loadMembers(),
        loadInvitations()
      ]);
    } catch (error) {
      console.error('Error loading members data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadMembers = async () => {
    try {
      const response = await fetch(`/.netlify/functions/studio-management/${allianceId}/members`, {
        headers: {
          'Authorization': `Bearer ${currentUser.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) throw new Error('Failed to fetch members');
      
      const data = await response.json();
      setMembers(data.members || []);
    } catch (error) {
      console.error('Error loading members:', error);
    }
  };

  const loadInvitations = async () => {
    try {
      const { data, error } = await supabase
        .from('studio_invitations')
        .select('*')
        .eq('studio_id', allianceId)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setInvitations(data || []);
    } catch (error) {
      console.error('Error loading invitations:', error);
    }
  };

  const sendInvitation = async () => {
    try {
      setIsLoading(true);
      
      const response = await fetch(`/.netlify/functions/studio-management/${allianceId}/invite`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${currentUser.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: inviteEmail,
          role: inviteRole,
          message: inviteMessage
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to send invitation');
      }

      // Reset form
      setInviteEmail('');
      setInviteRole('member');
      setInviteMessage('');
      onInviteClose();
      
      // Reload data
      await loadInvitations();
      
      console.log('Invitation sent successfully');
    } catch (error) {
      console.error('Error sending invitation:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateMemberRole = async (memberId, newRole) => {
    try {
      setIsLoading(true);
      
      const response = await fetch(`/.netlify/functions/studio-management/${allianceId}/members/${memberId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${currentUser.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          role: newRole
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update member role');
      }

      // Reload members
      await loadMembers();
      onMemberUpdate?.();
      
      console.log('Member role updated successfully');
    } catch (error) {
      console.error('Error updating member role:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case 'founder': return <Crown className="text-yellow-500" size={16} />;
      case 'owner': return <Crown className="text-yellow-500" size={16} />;
      case 'admin': return <Shield className="text-blue-500" size={16} />;
      default: return <User className="text-gray-500" size={16} />;
    }
  };

  const getRoleBadgeColor = (role) => {
    switch (role) {
      case 'founder': return 'warning';
      case 'owner': return 'warning';
      case 'admin': return 'primary';
      default: return 'default';
    }
  };

  const canManageMembers = () => {
    return ['founder', 'owner', 'admin'].includes(userRole);
  };

  const canEditMember = (member) => {
    if (member.user_id === currentUser.id) return false; // Can't edit self
    if (userRole === 'founder') return true;
    if (userRole === 'admin' && !['founder', 'owner'].includes(member.role)) return true;
    return false;
  };

  const filteredMembers = members.filter(member => {
    const matchesSearch = !searchQuery || 
      member.users?.display_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.users?.email?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesRole = roleFilter === 'all' || member.role === roleFilter;
    const matchesStatus = statusFilter === 'all' || member.status === statusFilter;
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  const openMemberDetails = (member) => {
    setSelectedMember(member);
    onMemberOpen();
  };

  return (
    <div className="enhanced-member-management space-y-6">
      {/* Header and Controls */}
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
        <div>
          <h3 className="text-xl font-bold">Studio Members ({members.length})</h3>
          <p className="text-gray-600">Manage your studio team and permissions</p>
        </div>
        
        {canManageMembers() && (
          <Button color="primary" startContent={<UserPlus size={18} />} onPress={onInviteOpen}>
            Invite Members
          </Button>
        )}
      </div>

      {/* Search and Filters */}
      <Card>
        <CardBody className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <Input
              placeholder="Search members by name or email..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              startContent={<Search size={18} />}
              className="flex-1"
            />
            <Select
              placeholder="Filter by role"
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
              className="md:w-48"
            >
              <SelectItem key="all" value="all">All Roles</SelectItem>
              <SelectItem key="founder" value="founder">Founder</SelectItem>
              <SelectItem key="admin" value="admin">Admin</SelectItem>
              <SelectItem key="member" value="member">Member</SelectItem>
            </Select>
            <Select
              placeholder="Filter by status"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="md:w-48"
            >
              <SelectItem key="all" value="all">All Status</SelectItem>
              <SelectItem key="active" value="active">Active</SelectItem>
              <SelectItem key="inactive" value="inactive">Inactive</SelectItem>
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Members Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <AnimatePresence>
          {filteredMembers.map((member) => (
            <motion.div
              key={member.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              layout
            >
              <Card className="hover:shadow-md transition-shadow cursor-pointer" onPress={() => openMemberDetails(member)}>
                <CardBody className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-semibold">{member.users?.display_name || member.users?.email}</h4>
                      <p className="text-sm text-gray-600">{member.users?.email}</p>
                      <div className="flex items-center gap-1 mt-1">
                        {getRoleIcon(member.role)}
                        <Badge color={getRoleBadgeColor(member.role)} size="sm">
                          {member.role}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${member.status === 'active' ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                      {canEditMember(member) && (
                        <Button size="sm" variant="light" isIconOnly>
                          <MoreVertical size={16} />
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  {member.statistics && (
                    <div className="grid grid-cols-2 gap-2 mb-3 text-xs">
                      <div className="text-center p-2 bg-gray-50 rounded">
                        <div className="font-semibold">{member.statistics.total_contributions}</div>
                        <div className="text-gray-600">Contributions</div>
                      </div>
                      <div className="text-center p-2 bg-gray-50 rounded">
                        <div className="font-semibold">{member.statistics.total_hours}h</div>
                        <div className="text-gray-600">Hours</div>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex gap-2">
                    <Button size="sm" variant="light" startContent={<MessageCircle size={14} />}>
                      Message
                    </Button>
                    {canEditMember(member) && (
                      <Button size="sm" variant="light" startContent={<Settings size={14} />}>
                        Manage
                      </Button>
                    )}
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Pending Invitations */}
      {invitations.length > 0 && (
        <Card>
          <CardBody className="p-6">
            <h4 className="text-lg font-semibold mb-4">Pending Invitations ({invitations.length})</h4>
            <div className="space-y-3">
              {invitations.map((invitation) => (
                <div key={invitation.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Mail className="text-blue-500" size={20} />
                    <div>
                      <h5 className="font-medium">{invitation.email}</h5>
                      <p className="text-sm text-gray-600">
                        Invited as {invitation.role} • {new Date(invitation.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge color="warning" size="sm">Pending</Badge>
                    {canManageMembers() && (
                      <Button size="sm" variant="light" color="danger">
                        Cancel
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}

      {/* Invite Member Modal */}
      <Modal isOpen={isInviteOpen} onClose={onInviteClose} size="md">
        <ModalContent>
          <ModalHeader>
            <h3>Invite New Member</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Input
                label="Email Address"
                placeholder="Enter member's email"
                value={inviteEmail}
                onChange={(e) => setInviteEmail(e.target.value)}
                startContent={<Mail size={18} />}
                type="email"
                isRequired
              />

              <Select
                label="Role"
                placeholder="Select member role"
                value={inviteRole}
                onChange={(e) => setInviteRole(e.target.value)}
                isRequired
              >
                <SelectItem key="member" value="member">
                  <div className="flex items-center gap-2">
                    <User size={16} />
                    <span>Member - Basic access</span>
                  </div>
                </SelectItem>
                <SelectItem key="admin" value="admin">
                  <div className="flex items-center gap-2">
                    <Shield size={16} />
                    <span>Admin - Management access</span>
                  </div>
                </SelectItem>
                {userRole === 'founder' && (
                  <SelectItem key="owner" value="owner">
                    <div className="flex items-center gap-2">
                      <Crown size={16} />
                      <span>Owner - Full access</span>
                    </div>
                  </SelectItem>
                )}
              </Select>

              <Textarea
                label="Personal Message (Optional)"
                placeholder="Add a personal message to the invitation..."
                value={inviteMessage}
                onChange={(e) => setInviteMessage(e.target.value)}
                rows={3}
              />
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onInviteClose}>
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={sendInvitation}
              isLoading={isLoading}
              isDisabled={!inviteEmail || !inviteRole}
            >
              Send Invitation
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Member Details Modal */}
      <Modal isOpen={isMemberOpen} onClose={onMemberClose} size="lg">
        <ModalContent>
          <ModalHeader>
            <h3>Member Details</h3>
          </ModalHeader>
          <ModalBody>
            {selectedMember && (
              <div className="space-y-6">
                {/* Member Info */}
                <div className="flex items-start gap-4">
                  <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                    <User size={32} className="text-gray-500" />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-xl font-semibold">{selectedMember.users?.display_name || selectedMember.users?.email}</h4>
                    <p className="text-gray-600">{selectedMember.users?.email}</p>
                    <div className="flex items-center gap-2 mt-2">
                      {getRoleIcon(selectedMember.role)}
                      <Badge color={getRoleBadgeColor(selectedMember.role)}>
                        {selectedMember.role}
                      </Badge>
                      <Badge color={selectedMember.status === 'active' ? 'success' : 'default'}>
                        {selectedMember.status}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Member Statistics */}
                {selectedMember.statistics && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{selectedMember.statistics.total_contributions}</div>
                      <div className="text-sm text-gray-600">Contributions</div>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{selectedMember.statistics.total_hours}</div>
                      <div className="text-sm text-gray-600">Hours</div>
                    </div>
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">{selectedMember.statistics.average_difficulty.toFixed(1)}</div>
                      <div className="text-sm text-gray-600">Avg Difficulty</div>
                    </div>
                    <div className="text-center p-3 bg-orange-50 rounded-lg">
                      <div className="text-2xl font-bold text-orange-600">{selectedMember.statistics.validation_rate.toFixed(0)}%</div>
                      <div className="text-sm text-gray-600">Success Rate</div>
                    </div>
                  </div>
                )}

                {/* Member Actions */}
                {canEditMember(selectedMember) && (
                  <div className="border-t pt-4">
                    <h5 className="font-semibold mb-3">Member Actions</h5>
                    <div className="flex gap-2">
                      <Button
                        color="primary"
                        variant="light"
                        startContent={<Edit size={16} />}
                        onPress={() => {
                          // Handle role change
                          const newRole = selectedMember.role === 'admin' ? 'member' : 'admin';
                          updateMemberRole(selectedMember.id, newRole);
                        }}
                      >
                        {selectedMember.role === 'admin' ? 'Remove Admin' : 'Make Admin'}
                      </Button>
                      <Button
                        color="danger"
                        variant="light"
                        startContent={<Trash2 size={16} />}
                      >
                        Remove Member
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onMemberClose}>
              Close
            </Button>
            <Button color="primary" startContent={<MessageCircle size={16} />}>
              Send Message
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default EnhancedMemberManagement;

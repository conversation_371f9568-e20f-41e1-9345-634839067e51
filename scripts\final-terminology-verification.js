#!/usr/bin/env node

/**
 * Final Terminology Verification Script
 * Comprehensive verification that all terminology updates are complete and working
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Verification checklist
const VERIFICATION_CHECKLIST = {
  database: {
    name: 'Database Migration',
    checks: [
      'Teams table has studio_type column',
      'Tasks table has mission columns',
      'Team_members table has collaboration columns',
      'New terminology tables created'
    ]
  },
  routing: {
    name: 'Routing & Navigation',
    checks: [
      'New routes configured (/studios, /missions)',
      'Backward compatibility redirects',
      'Navigation menus updated',
      'Component imports correct'
    ]
  },
  components: {
    name: 'Component System',
    checks: [
      'Studio components created and functional',
      'Mission components created and functional',
      'Component terminology updated',
      'All imports and exports correct'
    ]
  },
  uiText: {
    name: 'UI Text Updates',
    checks: [
      'User-facing text updated',
      'Form labels and placeholders updated',
      'Error messages and notifications updated',
      'Help text and descriptions updated'
    ]
  },
  documentation: {
    name: 'Documentation',
    checks: [
      'System documentation updated',
      'API documentation updated',
      'README files updated',
      'Implementation guides updated'
    ]
  }
};

// Files to check for remaining old terminology
const CRITICAL_FILES = [
  'client/src/App.jsx',
  'client/src/components/navigation/ContentRenderer.jsx',
  'client/src/components/navigation/SimpleNavHeader.jsx',
  'client/src/components/studio/StudioList.jsx',
  'client/src/components/missions/MissionBoard.jsx',
  'README.md'
];

// Old terminology patterns that should NOT exist
const OLD_TERMINOLOGY_PATTERNS = [
  /\bAlliance(?!s?\s+→\s+Studio)/g,  // Alliance (except in migration docs)
  /\bVenture(?!s?\s+→\s+Project)/g,  // Venture (except in migration docs)
  /\bQuest(?!s?\s+→\s+Mission)/g,    // Quest (except in migration docs)
  /alliance(?!s?\s+→\s+studio)/g,    // alliance (except in migration docs)
  /venture(?!s?\s+→\s+project)/g,    // venture (except in migration docs)
  /quest(?!s?\s+→\s+mission)/g       // quest (except in migration docs)
];

function checkFileForOldTerminology(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    OLD_TERMINOLOGY_PATTERNS.forEach((pattern, index) => {
      const matches = content.match(pattern);
      if (matches) {
        const termNames = ['Alliance', 'Venture', 'Quest', 'alliance', 'venture', 'quest'];
        issues.push({
          term: termNames[index],
          count: matches.length,
          pattern: pattern.toString()
        });
      }
    });
    
    return issues;
  } catch (error) {
    return [{ error: error.message }];
  }
}

function checkComponentExists(componentPath) {
  const fullPath = path.join(__dirname, '..', componentPath);
  return fs.existsSync(fullPath);
}

function checkRouteConfiguration() {
  const appJsPath = path.join(__dirname, '..', 'client/src/App.jsx');
  const contentRendererPath = path.join(__dirname, '..', 'client/src/components/navigation/ContentRenderer.jsx');
  
  const results = {
    appJs: { exists: false, hasStudioRoutes: false, hasMissionRoutes: false, hasRedirects: false },
    contentRenderer: { exists: false, hasStudioRoutes: false, hasMissionRoutes: false, hasRedirects: false }
  };
  
  // Check App.jsx
  if (fs.existsSync(appJsPath)) {
    const content = fs.readFileSync(appJsPath, 'utf8');
    results.appJs.exists = true;
    results.appJs.hasStudioRoutes = content.includes('/studios');
    results.appJs.hasMissionRoutes = content.includes('/missions');
    results.appJs.hasRedirects = content.includes('Navigate to="/studios"') || content.includes('Navigate to="/missions"');
  }
  
  // Check ContentRenderer.jsx
  if (fs.existsSync(contentRendererPath)) {
    const content = fs.readFileSync(contentRendererPath, 'utf8');
    results.contentRenderer.exists = true;
    results.contentRenderer.hasStudioRoutes = content.includes('/studios');
    results.contentRenderer.hasMissionRoutes = content.includes('/missions');
    results.contentRenderer.hasRedirects = content.includes('Navigate to="/studios"') || content.includes('Navigate to="/missions"');
  }
  
  return results;
}

async function runVerification() {
  console.log('🔍 Final Terminology Verification');
  console.log('=====================================\n');

  let overallScore = 0;
  let maxScore = 0;

  // 1. Check critical files for old terminology
  console.log('1️⃣ Checking for remaining old terminology...');
  let terminologyIssues = 0;
  
  for (const filePath of CRITICAL_FILES) {
    const fullPath = path.join(__dirname, '..', filePath);
    if (fs.existsSync(fullPath)) {
      const issues = checkFileForOldTerminology(fullPath);
      if (issues.length > 0) {
        console.log(`   ⚠️ ${filePath}:`);
        issues.forEach(issue => {
          if (issue.error) {
            console.log(`      Error: ${issue.error}`);
          } else {
            console.log(`      ${issue.term}: ${issue.count} occurrences`);
            terminologyIssues += issue.count;
          }
        });
      } else {
        console.log(`   ✅ ${filePath}: Clean`);
      }
    } else {
      console.log(`   ❌ ${filePath}: File not found`);
    }
  }

  if (terminologyIssues === 0) {
    console.log('   🎉 No old terminology found in critical files!');
    overallScore += 20;
  } else {
    console.log(`   ⚠️ Found ${terminologyIssues} instances of old terminology`);
  }
  maxScore += 20;

  // 2. Check component existence
  console.log('\n2️⃣ Checking component existence...');
  const requiredComponents = [
    'client/src/components/studio/StudioList.jsx',
    'client/src/components/studio/StudioDashboard.jsx',
    'client/src/components/studio/StudioCreationWizard.jsx',
    'client/src/components/missions/MissionBoard.jsx',
    'client/src/components/missions/MissionCreator.jsx',
    'client/src/components/missions/MissionDetail.jsx'
  ];

  let componentsExist = 0;
  requiredComponents.forEach(component => {
    if (checkComponentExists(component)) {
      console.log(`   ✅ ${component}`);
      componentsExist++;
    } else {
      console.log(`   ❌ ${component}: Missing`);
    }
  });

  overallScore += (componentsExist / requiredComponents.length) * 20;
  maxScore += 20;

  // 3. Check routing configuration
  console.log('\n3️⃣ Checking routing configuration...');
  const routeConfig = checkRouteConfiguration();
  
  let routingScore = 0;
  if (routeConfig.appJs.exists && routeConfig.appJs.hasStudioRoutes && routeConfig.appJs.hasMissionRoutes) {
    console.log('   ✅ App.jsx: Studio and mission routes configured');
    routingScore += 10;
  } else {
    console.log('   ❌ App.jsx: Missing routes');
  }

  if (routeConfig.contentRenderer.exists && routeConfig.contentRenderer.hasStudioRoutes && routeConfig.contentRenderer.hasMissionRoutes) {
    console.log('   ✅ ContentRenderer.jsx: Studio and mission routes configured');
    routingScore += 10;
  } else {
    console.log('   ❌ ContentRenderer.jsx: Missing routes');
  }

  if (routeConfig.appJs.hasRedirects || routeConfig.contentRenderer.hasRedirects) {
    console.log('   ✅ Backward compatibility redirects configured');
    routingScore += 10;
  } else {
    console.log('   ❌ Backward compatibility redirects missing');
  }

  overallScore += routingScore;
  maxScore += 30;

  // 4. Check database migration file
  console.log('\n4️⃣ Checking database migration...');
  const migrationPath = path.join(__dirname, '..', 'supabase/migrations/20250625000002_terminology_update_targeted.sql');
  if (fs.existsSync(migrationPath)) {
    console.log('   ✅ Database migration file exists');
    overallScore += 15;
  } else {
    console.log('   ❌ Database migration file missing');
  }
  maxScore += 15;

  // 5. Check documentation updates
  console.log('\n5️⃣ Checking documentation...');
  const readmePath = path.join(__dirname, '..', 'README.md');
  const summaryPath = path.join(__dirname, '..', 'TERMINOLOGY_UPDATE_SUMMARY.md');
  
  let docScore = 0;
  if (fs.existsSync(readmePath)) {
    const readmeContent = fs.readFileSync(readmePath, 'utf8');
    if (readmeContent.includes('Studio') && readmeContent.includes('Mission')) {
      console.log('   ✅ README.md updated with new terminology');
      docScore += 7.5;
    } else {
      console.log('   ⚠️ README.md may need terminology updates');
    }
  }

  if (fs.existsSync(summaryPath)) {
    console.log('   ✅ Terminology update summary exists');
    docScore += 7.5;
  } else {
    console.log('   ❌ Terminology update summary missing');
  }

  overallScore += docScore;
  maxScore += 15;

  // Final score calculation
  const percentage = Math.round((overallScore / maxScore) * 100);

  console.log('\n📊 Verification Results:');
  console.log('=====================================');
  console.log(`   Overall Score: ${overallScore}/${maxScore} (${percentage}%)`);
  
  if (percentage >= 95) {
    console.log('   🎉 EXCELLENT: Terminology update is complete and ready for production!');
  } else if (percentage >= 85) {
    console.log('   ✅ GOOD: Terminology update is mostly complete, minor issues to address');
  } else if (percentage >= 70) {
    console.log('   ⚠️ FAIR: Terminology update needs some work before production');
  } else {
    console.log('   ❌ NEEDS WORK: Significant issues need to be addressed');
  }

  console.log('\n🚀 Ready for Testing:');
  console.log('=====================================');
  console.log('1. Start development server: npm run dev');
  console.log('2. Test new routes:');
  console.log('   • http://localhost:8888/studios');
  console.log('   • http://localhost:8888/missions');
  console.log('3. Test redirects:');
  console.log('   • http://localhost:8888/alliances → /studios');
  console.log('   • http://localhost:8888/quests → /missions');
  console.log('4. Verify UI text throughout the application');
  console.log('5. Test component functionality');

  return percentage;
}

// Main execution
runVerification().catch(console.error);

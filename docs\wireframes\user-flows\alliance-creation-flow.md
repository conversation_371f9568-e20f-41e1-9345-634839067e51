# Alliance Creation Flow Wireframe
**Simple, Intuitive Team Formation Process - INTEGRATED WITH EXISTING SYSTEM**

## 📋 Flow Information
- **Flow Type**: Alliance (Team) Creation - **Maps to existing team/contributor system**
- **User Types**: Team Leaders, Project Initiators
- **Entry Points**: Dashboard "Create Alliance" button, Before project creation
- **Integration**: **Enhances existing team creation, integrates with ProjectWizard TeamContributors.jsx**
- **Database**: **Uses existing teams/contributors tables with Alliance UI terminology**
- **Approach**: **One simple question at a time** for easy decision-making
- **Outcome**: Properly configured Alliance that integrates with project creation

## 🔄 **INTEGRATION WITH EXISTING SYSTEM**

### **Database Mapping**
- **Alliance** → Enhanced team management (existing teams table)
- **Alliance Members** → Contributors (existing contributors table)
- **Alliance Roles** → Project roles (existing role system)
- **Alliance Settings** → Team preferences (new fields in teams table)

### **Component Integration**
- **Alliance Creation** → Enhances team setup before project creation
- **Member Invitation** → Uses existing contributor invitation system
- **Role Assignment** → Integrates with TeamContributors.jsx component
- **Studio Dashboard** → Enhanced team management interface

---

## 🎯 **Design Philosophy**

### **Super Simple Questions Approach**
- **One question per screen** - No cognitive overload
- **Plain English** - No technical jargon
- **Visual examples** - Show what each choice means
- **Smart defaults** - Pre-select most common options
- **Progress indication** - Show how many steps remain
- **Easy back/forward** - Change answers anytime

### **Intelligent Question Flow**
The questions **adapt based on previous answers** to create the perfect Alliance configuration without overwhelming the user.

---

## 🔄 **Complete Alliance Creation Flow**

```mermaid
flowchart TD
    A[Create Alliance Button] --> B[Welcome Screen]
    B --> C[Q1: Project Type]
    
    C --> D{Project Type?}
    D -->|Personal Project| E[Q2A: Team Size]
    D -->|Business Project| F[Q2B: Company Status]
    D -->|Open Source| G[Q2C: Community Type]
    D -->|Creative Work| H[Q2D: Creative Type]
    
    E --> I[Q3: Start Date]
    F --> J[Q3: Official Company?]
    G --> K[Q3: License Type]
    H --> L[Q3: Collaboration Style]
    
    I --> M[Q4: Payment Model]
    J --> N[Q4: Business Structure]
    K --> O[Q4: Contribution Rules]
    L --> P[Q4: Revenue Sharing]
    
    M --> Q[Q5: Team Roles]
    N --> R[Q5: Leadership Structure]
    O --> S[Q5: Maintainer Roles]
    P --> T[Q5: Creative Roles]
    
    Q --> U[Q6: Alliance Name]
    R --> U
    S --> U
    T --> U
    
    U --> V[Q7: Invitation Method]
    V --> W[Review & Create]
    W --> X[Alliance Created!]
    X --> Y[Invite Members]
```

---

## 📱 **Question-by-Question Wireframes**

### **Welcome Screen**
```
┌─────────────────────────────────────────────────────────────┐
│                        ROYALTEA                             │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                   🏰 Create Your Alliance                   │
│                                                             │
│    Let's set up your team in just a few simple steps.      │
│    We'll ask you some easy questions to make sure          │
│    everything is configured perfectly for your project.    │
│                                                             │
│                        ┌─────────────┐                     │
│                        │ [Let's Go!] │                     │
│                        └─────────────┘                     │
│                                                             │
│    ● ● ● ● ● ● ●  (7 simple questions)                     │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Question 1: Project Type**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                            Step 1/7  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              What kind of project is this?                  │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  💼 Business Project                                │ │
│    │  Building something for a company or startup       │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  👤 Personal Project                                │ │
│    │  Working on your own idea or side project          │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🌍 Open Source                                     │ │
│    │  Contributing to public, community-driven work     │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🎨 Creative Work                                   │ │
│    │  Art, music, writing, or other creative projects   │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│                                                             │
│    ● ○ ○ ○ ○ ○ ○                                           │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Question 2A: Team Size (Personal Project Path)**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                            Step 2/7  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              How big will your team be?                     │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  👤 Just Me                                         │ │
│    │  Solo project, might invite others later           │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  👥 Small Team (2-5 people)                        │ │
│    │  Close friends or collaborators                     │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  👨‍👩‍👧‍👦 Medium Team (6-15 people)                    │ │
│    │  Organized group with different skills             │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🏢 Large Team (15+ people)                        │ │
│    │  Big project with many contributors                │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│                                                             │
│    ● ● ○ ○ ○ ○ ○                                           │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Question 2B: Company Status (Business Project Path)**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                            Step 2/7  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              Are you an official company?                   │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🏢 Yes, we're an established company               │ │
│    │  LLC, Corp, or other official business entity      │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🚀 Startup in formation                           │ │
│    │  Planning to become official, but not yet          │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🤝 Informal business partnership                  │ │
│    │  Working together but no official structure        │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  💡 Just an idea for now                           │ │
│    │  Exploring if this could become a business         │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│                                                             │
│    ● ● ○ ○ ○ ○ ○                                           │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Question 3: Start Date**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                            Step 3/7  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              When do you want to start?                     │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🚀 Right now!                                     │ │
│    │  We're ready to begin immediately                  │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  📅 Specific date: [Select Date]                   │ │
│    │  We have a planned start date                      │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🤔 When the team is ready                         │ │
│    │  Start once we have enough people                  │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  💰 When funding is secured                        │ │
│    │  Waiting for budget or investment                  │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│                                                             │
│    ● ● ● ○ ○ ○ ○                                           │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Question 4: Payment Model**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                            Step 4/7  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              How will people get paid?                      │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  💰 Fixed payments                                  │ │
│    │  Pay specific amounts for completed tasks          │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  📊 Revenue sharing                                 │ │
│    │  Split income based on contributions               │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🎯 Hybrid model                                    │ │
│    │  Some fixed payments + some revenue sharing        │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🤝 Volunteer/Equity only                          │ │
│    │  No immediate payment, future equity/profit share  │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  ❓ Not sure yet                                    │ │
│    │  We'll figure this out as we go                    │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ● ● ● ● ○ ○ ○                                           │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Question 5: Team Roles**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                            Step 5/7  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              What roles will your team have?                │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  👑 I'll be the main leader                         │ │
│    │  I make final decisions and manage the project     │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🤝 Shared leadership                               │ │
│    │  Multiple people will share decision-making        │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🎯 Skill-based roles                               │ │
│    │  People lead their areas of expertise              │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🌊 Flexible roles                                  │ │
│    │  Roles will evolve based on what's needed          │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│                                                             │
│    ● ● ● ● ● ○ ○                                           │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Question 6: Alliance Name**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                            Step 6/7  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              What should we call your Alliance?             │
│                                                             │
│    Alliance Name                                            │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ The Dream Team Alliance                             │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    💡 Suggestions based on your project:                   │
│    • [Project Name] Alliance                               │
│    • [Your Name]'s Team                                    │
│    • The [Industry] Collective                             │
│                                                             │
│    Description (optional)                                  │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ A group of passionate developers building the       │ │
│    │ future of creative collaboration...                 │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    🎨 Choose an icon:                                      │
│    🏰 🛡️ ⚔️ 🏆 🎯 🚀 💎 🌟                                │
│                                                             │
│                                                             │
│    ● ● ● ● ● ● ○                                           │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Question 7: Invitation Method**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                            Step 7/7  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              How do you want to invite people?              │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  📧 Send email invitations                          │ │
│    │  I have their email addresses                      │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🔗 Create invitation links                         │ │
│    │  I'll share links with people myself               │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🌍 Make it discoverable                            │ │
│    │  Let people find and request to join               │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  ⏳ I'll invite people later                        │ │
│    │  Just create the Alliance for now                  │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│                                                             │
│    ● ● ● ● ● ● ●                                           │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Review & Create**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                          Almost Done │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                   🏰 Review Your Alliance                   │
│                                                             │
│    Alliance Name: The Dream Team Alliance                  │
│    Project Type: Personal Project                          │
│    Team Size: Small Team (2-5 people)                      │
│    Start Date: Right now!                                  │
│    Payment Model: Revenue sharing                          │
│    Leadership: I'll be the main leader                     │
│    Invitations: Send email invitations                     │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │                [Create Alliance]                    │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    This will create your Alliance and set up the basic     │
│    structure. You can always change these settings later.  │
│                                                             │
│                                                             │
│    ● ● ● ● ● ● ●                                           │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

---

## 🧠 **Smart Question Logic**

### **Adaptive Question Flow**
The questions **change based on previous answers**:

- **Business Project** → Company status, business structure, formal roles
- **Personal Project** → Team size, informal structure, flexible roles  
- **Open Source** → License type, contribution rules, maintainer roles
- **Creative Work** → Collaboration style, creative roles, IP sharing

### **Smart Defaults**
- **Most common choices** are pre-selected
- **Previous user patterns** inform suggestions
- **Project type** determines default payment models
- **Team size** suggests appropriate role structures

### **Validation & Help**
- **Real-time validation** on names and descriptions
- **Helpful examples** for each choice
- **Tooltips** explaining complex concepts
- **"Not sure?" options** for uncertain users

---

## 📱 **Mobile Responsive Design**

### **Mobile Question Layout**
```
┌─────────────────────────┐
│ ← Back          Step 1/7│
├─────────────────────────┤
│                         │
│ What kind of project    │
│ is this?                │
│                         │
│ ┌─────────────────────┐ │
│ │ 💼 Business Project │ │
│ │ For a company or    │ │
│ │ startup             │ │
│ └─────────────────────┘ │
│                         │
│ ┌─────────────────────┐ │
│ │ 👤 Personal Project │ │
│ │ Your own idea or    │ │
│ │ side project        │ │
│ └─────────────────────┘ │
│                         │
│ [More Options ▼]        │
│                         │
│ ● ○ ○ ○ ○ ○ ○           │
│                         │
└─────────────────────────┘
```

---

## 🎯 **User Experience Goals**

### **Simplicity**
- **One decision at a time** - No cognitive overload
- **Plain language** - No technical jargon
- **Visual clarity** - Clear choices with icons
- **Quick completion** - 2-3 minutes total

### **Confidence**
- **Clear explanations** for each choice
- **Examples** of what each option means
- **Easy to change** answers if needed
- **Preview** of final configuration

### **Intelligence**
- **Learns from choices** to ask relevant follow-ups
- **Suggests names** based on project type
- **Recommends settings** based on similar projects
- **Skips irrelevant** questions when possible

---

**This Alliance Creation Flow transforms a complex team setup process into a simple, conversational experience that guides users naturally to the perfect configuration for their specific project type and needs.**

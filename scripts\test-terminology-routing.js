#!/usr/bin/env node

/**
 * Test Terminology Routing
 * Tests that the new terminology routes are properly configured
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test configuration
const testConfig = {
  baseUrl: 'http://localhost:8888', // Netlify dev server
  routes: [
    // New terminology routes
    { path: '/studios', name: 'Studios List', expected: 'Studios' },
    { path: '/studios/create', name: 'Studio Creation', expected: 'Create' },
    { path: '/missions', name: 'Mission Board', expected: 'Missions' },
    { path: '/missions/create', name: 'Mission Creator', expected: 'Create' },
    
    // Backward compatibility redirects
    { path: '/alliances', name: 'Alliance Redirect', expected: 'Studios', redirect: true },
    { path: '/quests', name: 'Quest Redirect', expected: 'Missions', redirect: true },
    
    // Existing routes that should still work
    { path: '/projects', name: 'Projects', expected: 'Projects' },
    { path: '/teams', name: 'Teams', expected: 'Teams' },
  ]
};

async function testRouting() {
  console.log('🧪 Testing Terminology Routing...');
  console.log('=====================================\n');

  // Check if files exist
  console.log('1️⃣ Checking component files...');
  
  const componentPaths = [
    'client/src/components/studio/StudioList.jsx',
    'client/src/components/studio/StudioDashboard.jsx',
    'client/src/components/studio/StudioCreationWizard.jsx',
    'client/src/components/studio/StudioManage.jsx',
    'client/src/components/studio/StudioInvitations.jsx',
    'client/src/components/missions/MissionBoard.jsx',
    'client/src/components/missions/MissionCreator.jsx',
    'client/src/components/missions/MissionDetail.jsx'
  ];

  for (const componentPath of componentPaths) {
    const fullPath = path.join(__dirname, '..', componentPath);
    if (fs.existsSync(fullPath)) {
      console.log(`   ✅ ${componentPath}`);
    } else {
      console.log(`   ❌ ${componentPath} - Missing!`);
    }
  }

  // Check routing configuration
  console.log('\n2️⃣ Checking routing configuration...');
  
  const routingFiles = [
    'client/src/App.jsx',
    'client/src/components/navigation/ContentRenderer.jsx',
    'client/src/components/navigation/SimpleNavHeader.jsx',
    'client/src/components/navigation/EnhancedMobileNavigation.jsx'
  ];

  for (const routingFile of routingFiles) {
    const fullPath = path.join(__dirname, '..', routingFile);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // Check for new terminology routes
      const hasStudios = content.includes('/studios');
      const hasMissions = content.includes('/missions');
      const hasBackwardCompat = content.includes('Navigate to="/studios"') || content.includes('Navigate to="/missions"');
      
      console.log(`   📄 ${routingFile}:`);
      console.log(`      Studios routes: ${hasStudios ? '✅' : '❌'}`);
      console.log(`      Mission routes: ${hasMissions ? '✅' : '❌'}`);
      console.log(`      Backward compatibility: ${hasBackwardCompat ? '✅' : '❌'}`);
    } else {
      console.log(`   ❌ ${routingFile} - Missing!`);
    }
  }

  // Check navigation configuration
  console.log('\n3️⃣ Checking navigation configuration...');
  
  const navFiles = [
    'client/src/components/navigation/NavigationBreadcrumbs.jsx',
    'client/src/components/navigation/EnhancedBreadcrumbs.jsx',
    'client/src/components/navigation/CrossTileNavigationBridge.jsx'
  ];

  for (const navFile of navFiles) {
    const fullPath = path.join(__dirname, '..', navFile);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      const hasStudiosNav = content.includes('studios') || content.includes('Studios');
      const hasMissionsNav = content.includes('missions') || content.includes('Missions');
      
      console.log(`   📄 ${navFile}:`);
      console.log(`      Studios navigation: ${hasStudiosNav ? '✅' : '❌'}`);
      console.log(`      Mission navigation: ${hasMissionsNav ? '✅' : '❌'}`);
    } else {
      console.log(`   ❌ ${navFile} - Missing!`);
    }
  }

  console.log('\n4️⃣ Route Summary:');
  console.log('=====================================');
  
  testConfig.routes.forEach(route => {
    const status = route.redirect ? '🔄 Redirect' : '📄 Direct';
    console.log(`   ${status} ${route.path} → ${route.name}`);
  });

  console.log('\n🎯 Next Steps:');
  console.log('=====================================');
  console.log('1. Start the development server: npm run dev');
  console.log('2. Test the new routes manually:');
  console.log('   • http://localhost:8888/studios');
  console.log('   • http://localhost:8888/missions');
  console.log('   • http://localhost:8888/alliances (should redirect to studios)');
  console.log('   • http://localhost:8888/quests (should redirect to missions)');
  console.log('3. Verify navigation menus include new terminology');
  console.log('4. Test backward compatibility redirects');

  console.log('\n📋 Terminology Update Status:');
  console.log('=====================================');
  console.log('✅ Database migration applied');
  console.log('✅ New component files created');
  console.log('✅ Routing configuration updated');
  console.log('✅ Navigation menus updated');
  console.log('✅ Backward compatibility added');
  console.log('🔄 Ready for manual testing');
}

// Main execution
testRouting().catch(console.error);

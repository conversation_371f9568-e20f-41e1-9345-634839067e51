# Venture Setup Flow Wireframe
**Simple, Intuitive Project Creation Process - INTEGRATED WITH EXISTING WIZARD**

## 📋 Flow Information
- **Flow Type**: Venture (Project) Creation
- **User Types**: Project Leaders, Alliance Members
- **Entry Points**: Studio Dashboard, "Create Venture" button
- **Integration**: **Replaces existing ProjectWizard.jsx with simplified approach**
- **Existing Steps**: 7-step wizard (ProjectBasics → TeamContributors → RoyaltyModel → RevenueTranches → ContributionTracking → Milestones → ReviewAgreement)
- **New Approach**: **Simple questions first, then detailed configuration**
- **Outcome**: Properly configured Venture with correct agreements and structure

## 🔄 **INTEGRATION WITH EXISTING SYSTEM**

### **Phase 1: Simple Questions (NEW)**
**Replaces complex ProjectBasics form with intuitive questioning:**
- What are you building? (maps to project_type)
- Who is it for? (maps to target audience fields)
- Timeline & scope (maps to estimated_duration, start_date)
- Budget approach (maps to royalty model selection)

### **Phase 2: Existing Wizard Steps (ENHANCED)**
**Keeps existing functionality but with better UX:**
- Team & Contributors (existing TeamContributors.jsx)
- Royalty Model (existing RoyaltyModel.jsx)
- Revenue Tranches (existing RevenueTranchesEnhanced.jsx)
- Contribution Tracking (existing ContributionTracking.jsx)
- Milestones (existing Milestones.jsx)
- Review Agreement (existing ReviewAgreement.jsx with enhanced generator)

---

## 🎯 **Design Philosophy**

### **Super Simple Questions Approach**
- **One question per screen** - Focus on single decisions
- **Context-aware** - Questions adapt based on Alliance settings
- **Visual examples** - Show what each choice creates
- **Smart suggestions** - Based on project type and industry
- **Agreement integration** - Automatically generates correct contracts
- **Mission-ready** - Sets up for immediate task creation

---

## 🔄 **Complete Venture Setup Flow**

```mermaid
flowchart TD
    A[Create Venture Button] --> B[Welcome Screen]
    B --> C[Q1: What Are You Building?]
    
    C --> D{Project Category?}
    D -->|Software/App| E[Q2A: Development Type]
    D -->|Creative Work| F[Q2B: Creative Medium]
    D -->|Business Service| G[Q2C: Service Type]
    D -->|Physical Product| H[Q2D: Product Type]
    D -->|Other| I[Q2E: Custom Description]
    
    E --> J[Q3: Target Audience]
    F --> J
    G --> J
    H --> J
    I --> J
    
    J --> K[Q4: Timeline & Scope]
    K --> L[Q5: Budget & Resources]
    L --> M[Q6: Success Metrics]
    M --> N[Q7: Venture Name & Details]
    N --> O[Q8: Initial Team Roles]
    O --> P[Review & Generate Agreement]
    P --> Q[Venture Created!]
    Q --> R[Create First Missions]
```

---

## 📱 **Question-by-Question Wireframes**

### **Welcome Screen**
```
┌─────────────────────────────────────────────────────────────┐
│                        ROYALTEA                             │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                   🚀 Create Your Venture                    │
│                                                             │
│    Let's set up your project! We'll ask some simple        │
│    questions to create the perfect structure and           │
│    automatically generate your collaboration agreement.    │
│                                                             │
│    Alliance: The Dream Team Alliance                       │
│    Members: 4 people ready to contribute                   │
│                                                             │
│                        ┌─────────────┐                     │
│                        │ [Start Now] │                     │
│                        └─────────────┘                     │
│                                                             │
│    ● ● ● ● ● ● ● ●  (8 simple questions)                   │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Question 1: What Are You Building?**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                            Step 1/8  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              What are you building?                         │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  💻 Software or App                                 │ │
│    │  Website, mobile app, or software tool             │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🎨 Creative Work                                   │ │
│    │  Art, music, video, writing, or design             │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🤝 Business Service                                │ │
│    │  Consulting, marketing, or professional service    │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  📦 Physical Product                                │ │
│    │  Something you can touch and ship                  │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  ❓ Something else                                   │ │
│    │  I'll describe it myself                           │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ● ○ ○ ○ ○ ○ ○ ○                                         │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Question 2A: Development Type (Software Path)**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                            Step 2/8  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              What kind of software?                         │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  📱 Mobile App                                      │ │
│    │  iOS, Android, or cross-platform mobile app        │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🌐 Web Application                                 │ │
│    │  Website or web-based software platform            │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🖥️ Desktop Software                                │ │
│    │  Windows, Mac, or Linux desktop application        │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🔧 Developer Tool                                  │ │
│    │  Library, framework, or tool for other developers  │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🎮 Game                                            │ │
│    │  Video game for any platform                       │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ● ● ○ ○ ○ ○ ○ ○                                         │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Question 3: Target Audience**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                            Step 3/8  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              Who is this for?                               │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  👥 General Public                                  │ │
│    │  Anyone can use it                                 │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🏢 Businesses                                      │ │
│    │  Companies and organizations                       │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  👨‍💻 Developers                                      │ │
│    │  Other programmers and technical people            │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🎯 Specific Industry                               │ │
│    │  Healthcare, education, finance, etc.              │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  👤 Just for us                                     │ │
│    │  Internal tool for our team/company                │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ● ● ● ○ ○ ○ ○ ○                                         │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Question 4: Timeline & Scope**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                            Step 4/8  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              What's your timeline?                          │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  ⚡ Quick Sprint (1-4 weeks)                        │ │
│    │  Small project, get it done fast                   │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🎯 Short Project (1-3 months)                     │ │
│    │  Focused scope with clear end date                 │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🏗️ Medium Project (3-12 months)                   │ │
│    │  Substantial project with multiple phases          │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🌟 Long-term Vision (1+ years)                    │ │
│    │  Big ambitious project, ongoing development        │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🤷 We'll see how it goes                           │ │
│    │  No fixed timeline, work at our own pace           │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ● ● ● ● ○ ○ ○ ○                                         │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Question 5: Budget & Resources**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                            Step 5/8  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              What's your budget situation?                  │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  💰 We have funding                                 │ │
│    │  Budget available for tools, services, payments    │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🎯 Bootstrapped                                    │ │
│    │  Self-funded, keeping costs minimal                │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🤝 Sweat Equity                                    │ │
│    │  Everyone contributes time, no money upfront       │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  📈 Revenue-First                                   │ │
│    │  Plan to make money quickly to fund development    │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🔍 Seeking Investment                              │ │
│    │  Looking for investors or grants                   │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ● ● ● ● ● ○ ○ ○                                         │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Question 6: Success Metrics**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                            Step 6/8  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              How will you know you've succeeded?            │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  💰 Revenue Target                                  │ │
│    │  Specific income or profit goals                   │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  👥 User Growth                                     │ │
│    │  Number of users, downloads, or customers          │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🎯 Feature Completion                              │ │
│    │  Building specific features or capabilities        │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🏆 Recognition                                     │ │
│    │  Awards, press coverage, industry recognition      │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  😊 Personal Satisfaction                           │ │
│    │  Learning, portfolio building, having fun          │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ● ● ● ● ● ● ○ ○                                         │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Question 7: Venture Name & Details**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                            Step 7/8  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              What should we call your Venture?              │
│                                                             │
│    Venture Name                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ TaskMaster Pro                                      │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    💡 Suggestions based on your project:                   │
│    • [Your App Name]                                       │
│    • [Alliance Name] [Project Type]                        │
│    • The [Target Audience] Solution                        │
│                                                             │
│    Short Description                                        │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ A mobile app that helps teams manage tasks and     │ │
│    │ track productivity with smart automation...        │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    🎨 Choose an icon:                                      │
│    📱 💻 🎯 🚀 ⚡ 🔧 💡 🌟                                │
│                                                             │
│    🏷️ Tags (help others find your project):               │
│    #mobile #productivity #automation #startup              │
│                                                             │
│    ● ● ● ● ● ● ● ○                                         │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Question 8: Initial Team Roles**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                            Step 8/8  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              Who will do what?                              │
│                                                             │
│    Based on your Alliance and project type, here are       │
│    suggested roles for your team:                          │
│                                                             │
│    👑 Project Lead: You                                    │
│    ├─ Overall project management                           │
│    └─ Final decision making                                │
│                                                             │
│    💻 Lead Developer: [Assign to team member]              │
│    ├─ Technical architecture                               │
│    └─ Code review and standards                            │
│                                                             │
│    🎨 UI/UX Designer: [Assign to team member]              │
│    ├─ User interface design                                │
│    └─ User experience optimization                         │
│                                                             │
│    🧪 QA Tester: [Assign to team member]                   │
│    ├─ Quality assurance                                    │
│    └─ Bug testing and reporting                            │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ [Assign Roles Later] │ [Customize Roles]            │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ● ● ● ● ● ● ● ●                                         │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Review & Generate Agreement**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                          Almost Done │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                   🚀 Review Your Venture                    │
│                                                             │
│    Venture: TaskMaster Pro                                 │
│    Type: Mobile App for General Public                     │
│    Timeline: Medium Project (3-12 months)                  │
│    Budget: Bootstrapped                                     │
│    Success: User Growth + Revenue Target                   │
│    Alliance: The Dream Team Alliance                       │
│                                                             │
│    🔧 Agreement Generator: Enhanced Generator               │
│    📋 Template: Software Development - Standard            │
│    ⚖️ Legal Framework: Small Business Compliant            │
│                                                             │
│    📄 Generated Agreement Preview:                          │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ COLLABORATION AGREEMENT                             │ │
│    │ TaskMaster Pro Development Venture                  │ │
│    │                                                     │ │
│    │ Parties: The Dream Team Alliance Members            │ │
│    │ Project: Mobile App Development                     │ │
│    │ Revenue Model: CoG (Tasks-Time-Difficulty)          │ │
│    │ IP Ownership: Shared among contributors             │ │
│    │                                                     │ │
│    │ [View Full Agreement] [Customize] [Download PDF]    │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ✅ Auto-Generated Components:                            │
│    ☑️ Collaboration Agreement (12 pages)                   │
│    ☑️ Revenue Sharing Structure (CoG model)                │
│    ☑️ IP and Ownership Terms (contributor-based)           │
│    ☑️ Role Definitions (based on team assignments)         │
│    ☑️ Project Milestones (timeline-based)                  │
│    ☑️ Dispute Resolution Process                           │
│    ☑️ Termination and Exit Clauses                        │
│                                                             │
│    🔍 Validation Status:                                    │
│    ✅ Legal terms validated                                │
│    ✅ Small creative business compliance checked                    │
│    ✅ Revenue model calculations verified                  │
│    ✅ Role assignments confirmed                           │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │              [Create Venture]                       │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    Agreement can be reviewed and modified after creation.  │
│                                                             │
│    ● ● ● ● ● ● ● ●                                         │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

---

## 🧠 **Smart Question Logic**

### **Context-Aware Questions**
- **Alliance settings** influence available options
- **Project type** determines relevant follow-up questions
- **Budget choice** affects payment and resource suggestions
- **Timeline** influences milestone and role recommendations

### **Intelligent Suggestions**
- **Name suggestions** based on project type and target audience
- **Role assignments** based on Alliance member skills
- **Success metrics** relevant to project category
- **Agreement templates** matching project and business structure

### **Agreement Integration**
- **Automatic generation** of collaboration agreements
- **Template selection** based on project type and structure
- **Revenue sharing** models based on budget and payment choices
- **IP ownership** terms appropriate for project category

---

## 📱 **Mobile Responsive Design**

### **Mobile Question Layout**
```
┌─────────────────────────┐
│ ← Back          Step 1/8│
├─────────────────────────┤
│                         │
│ What are you building?  │
│                         │
│ ┌─────────────────────┐ │
│ │ 💻 Software or App  │ │
│ │ Website, mobile app,│ │
│ │ or software tool    │ │
│ └─────────────────────┘ │
│                         │
│ ┌─────────────────────┐ │
│ │ 🎨 Creative Work    │ │
│ │ Art, music, video,  │ │
│ │ writing, or design  │ │
│ └─────────────────────┘ │
│                         │
│ [More Options ▼]        │
│                         │
│ ● ○ ○ ○ ○ ○ ○ ○         │
│                         │
└─────────────────────────┘
```

---

## 🎯 **Integration Points**

### **Enhanced Agreement Generator Integration**
- **Multi-generator support** - Enhanced, New, Template Manager, Legacy
- **Automatic template** selection based on project answers
- **Real-time preview** with live updates as user answers questions
- **Custom clauses** for specific project types and industries
- **Revenue sharing** calculations based on budget model and CoG algorithms
- **Role definitions** matching team structure and skill requirements
- **Compliance validation** for small business legal requirements
- **Version control** with approval workflows and change tracking

### **Mission Creation Preparation**
- **Initial mission** suggestions based on project type
- **Role-based** task templates
- **Timeline-based** milestone creation
- **Success metric** tracking setup

### **Alliance Connection**
- **Inherits settings** from parent Alliance
- **Member role** assignments from Alliance structure
- **Payment model** consistency with Alliance preferences
- **Communication** integration with Alliance channels

---

**This Venture Setup Flow transforms complex project planning into a simple, guided conversation that automatically generates the right structure, agreements, and initial setup for any type of collaborative project.**

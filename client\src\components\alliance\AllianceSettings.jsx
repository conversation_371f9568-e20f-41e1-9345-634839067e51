import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Input, Select, SelectItem, Switch, Textarea, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * AllianceSettings Component - Studio Configuration and Preferences
 * 
 * Features:
 * - Studio visibility and privacy settings
 * - Notification preferences and communication settings
 * - Integration configurations (Slack, Discord, etc.)
 * - Security and access control settings
 * - Data export and backup options
 */
const AllianceSettings = ({ allianceId, userRole, className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [settings, setSettings] = useState({
    visibility: 'private',
    allow_public_applications: false,
    require_approval: true,
    auto_accept_invites: false,
    notifications: {
      email_enabled: true,
      slack_enabled: false,
      discord_enabled: false,
      new_members: true,
      revenue_updates: true,
      project_updates: true
    },
    integrations: {
      slack_webhook: '',
      discord_webhook: '',
      github_org: '',
      custom_domain: ''
    },
    security: {
      two_factor_required: false,
      ip_restrictions: false,
      session_timeout: 24,
      audit_logging: true
    },
    data: {
      auto_backup: true,
      backup_frequency: 'weekly',
      data_retention: 365
    }
  });

  useEffect(() => {
    if (allianceId && currentUser) {
      fetchSettings();
    }
  }, [allianceId, currentUser]);

  // Fetch studio settings
  const fetchSettings = async () => {
    try {
      setLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      const response = await fetch(`/.netlify/functions/studio-management/${allianceId}/settings`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.settings) {
          setSettings(prev => ({ ...prev, ...data.settings }));
        }
      } else if (response.status !== 404) {
        throw new Error('Failed to fetch studio settings');
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      toast.error('Failed to load studio settings');
    } finally {
      setLoading(false);
    }
  };

  // Save studio settings
  const saveSettings = async () => {
    try {
      setSaving(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      const response = await fetch(`/.netlify/functions/studio-management/${allianceId}/settings`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ settings })
      });

      if (response.ok) {
        toast.success('Studio settings saved successfully!');
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error(error.message || 'Failed to save studio settings');
    } finally {
      setSaving(false);
    }
  };

  // Update setting field
  const updateSetting = (field, value) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Update nested setting
  const updateNestedSetting = (parent, field, value) => {
    setSettings(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value
      }
    }));
  };

  // Check if user can manage settings
  const canManageSettings = ['founder', 'owner'].includes(userRole);

  if (loading) {
    return (
      <Card className={className}>
        <CardBody className="p-6 text-center">
          <div className="animate-spin text-2xl mb-2">🔄</div>
          <div>Loading studio settings...</div>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className={`studio-settings ${className}`}>
      <Card className="bg-gradient-to-br from-slate-50 to-gray-100 dark:from-slate-900/20 dark:to-gray-800/20 border-2 border-slate-200 dark:border-slate-700">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">⚙️</span>
              <h3 className="text-lg font-semibold">Studio Settings</h3>
            </div>
            {canManageSettings && (
              <Button
                color="primary"
                onPress={saveSettings}
                isLoading={saving}
                isDisabled={saving}
              >
                Save Settings
              </Button>
            )}
          </div>
        </CardHeader>
        
        <CardBody className="pt-0 space-y-6">
          {!canManageSettings && (
            <Card className="bg-warning-50 dark:bg-warning-900/20 border border-warning-200">
              <CardBody className="p-4">
                <div className="flex items-center gap-2">
                  <span className="text-warning">⚠️</span>
                  <span className="text-sm">Only founders and owners can modify studio settings</span>
                </div>
              </CardBody>
            </Card>
          )}

          {/* Privacy & Visibility */}
          <div>
            <h4 className="text-lg font-semibold mb-4">🔒 Privacy & Visibility</h4>
            <div className="space-y-4">
              <Select
                label="Studio Visibility"
                selectedKeys={[settings.visibility]}
                onSelectionChange={(keys) => updateSetting('visibility', Array.from(keys)[0])}
                isDisabled={!canManageSettings}
              >
                <SelectItem key="public">Public - Visible to everyone</SelectItem>
                <SelectItem key="private">Private - Invitation only</SelectItem>
                <SelectItem key="unlisted">Unlisted - Hidden from search</SelectItem>
              </Select>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Allow Public Applications</div>
                    <div className="text-sm text-default-600">Let people apply to join your studio</div>
                  </div>
                  <Switch 
                    isSelected={settings.allow_public_applications}
                    onValueChange={(value) => updateSetting('allow_public_applications', value)}
                    isDisabled={!canManageSettings}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Require Approval</div>
                    <div className="text-sm text-default-600">Manually approve new members</div>
                  </div>
                  <Switch 
                    isSelected={settings.require_approval}
                    onValueChange={(value) => updateSetting('require_approval', value)}
                    isDisabled={!canManageSettings}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Notifications */}
          <div>
            <h4 className="text-lg font-semibold mb-4">🔔 Notifications</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Email Notifications</div>
                  <div className="text-sm text-default-600">Receive updates via email</div>
                </div>
                <Switch 
                  isSelected={settings.notifications.email_enabled}
                  onValueChange={(value) => updateNestedSetting('notifications', 'email_enabled', value)}
                  isDisabled={!canManageSettings}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">New Member Alerts</div>
                  <div className="text-sm text-default-600">Notify when members join</div>
                </div>
                <Switch 
                  isSelected={settings.notifications.new_members}
                  onValueChange={(value) => updateNestedSetting('notifications', 'new_members', value)}
                  isDisabled={!canManageSettings}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Revenue Updates</div>
                  <div className="text-sm text-default-600">Notify on revenue changes</div>
                </div>
                <Switch 
                  isSelected={settings.notifications.revenue_updates}
                  onValueChange={(value) => updateNestedSetting('notifications', 'revenue_updates', value)}
                  isDisabled={!canManageSettings}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Project Updates</div>
                  <div className="text-sm text-default-600">Notify on project changes</div>
                </div>
                <Switch 
                  isSelected={settings.notifications.project_updates}
                  onValueChange={(value) => updateNestedSetting('notifications', 'project_updates', value)}
                  isDisabled={!canManageSettings}
                />
              </div>
            </div>
          </div>

          {/* Integrations */}
          <div>
            <h4 className="text-lg font-semibold mb-4">🔗 Integrations</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Slack Webhook URL"
                placeholder="https://hooks.slack.com/..."
                value={settings.integrations.slack_webhook}
                onChange={(e) => updateNestedSetting('integrations', 'slack_webhook', e.target.value)}
                isDisabled={!canManageSettings}
              />

              <Input
                label="Discord Webhook URL"
                placeholder="https://discord.com/api/webhooks/..."
                value={settings.integrations.discord_webhook}
                onChange={(e) => updateNestedSetting('integrations', 'discord_webhook', e.target.value)}
                isDisabled={!canManageSettings}
              />

              <Input
                label="GitHub Organization"
                placeholder="your-org-name"
                value={settings.integrations.github_org}
                onChange={(e) => updateNestedSetting('integrations', 'github_org', e.target.value)}
                isDisabled={!canManageSettings}
              />

              <Input
                label="Custom Domain"
                placeholder="studio.yourdomain.com"
                value={settings.integrations.custom_domain}
                onChange={(e) => updateNestedSetting('integrations', 'custom_domain', e.target.value)}
                isDisabled={!canManageSettings}
              />
            </div>
          </div>

          {/* Security */}
          <div>
            <h4 className="text-lg font-semibold mb-4">🛡️ Security</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Two-Factor Authentication</div>
                  <div className="text-sm text-default-600">Require 2FA for all members</div>
                </div>
                <Switch 
                  isSelected={settings.security.two_factor_required}
                  onValueChange={(value) => updateNestedSetting('security', 'two_factor_required', value)}
                  isDisabled={!canManageSettings}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Audit Logging</div>
                  <div className="text-sm text-default-600">Log all studio activities</div>
                </div>
                <Switch 
                  isSelected={settings.security.audit_logging}
                  onValueChange={(value) => updateNestedSetting('security', 'audit_logging', value)}
                  isDisabled={!canManageSettings}
                />
              </div>

              <Select
                label="Session Timeout (hours)"
                selectedKeys={[settings.security.session_timeout.toString()]}
                onSelectionChange={(keys) => updateNestedSetting('security', 'session_timeout', parseInt(Array.from(keys)[0]))}
                isDisabled={!canManageSettings}
              >
                <SelectItem key="1">1 hour</SelectItem>
                <SelectItem key="8">8 hours</SelectItem>
                <SelectItem key="24">24 hours</SelectItem>
                <SelectItem key="168">1 week</SelectItem>
              </Select>
            </div>
          </div>

          {/* Data Management */}
          <div>
            <h4 className="text-lg font-semibold mb-4">💾 Data Management</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Auto Backup</div>
                  <div className="text-sm text-default-600">Automatically backup studio data</div>
                </div>
                <Switch 
                  isSelected={settings.data.auto_backup}
                  onValueChange={(value) => updateNestedSetting('data', 'auto_backup', value)}
                  isDisabled={!canManageSettings}
                />
              </div>

              <Select
                label="Backup Frequency"
                selectedKeys={[settings.data.backup_frequency]}
                onSelectionChange={(keys) => updateNestedSetting('data', 'backup_frequency', Array.from(keys)[0])}
                isDisabled={!canManageSettings || !settings.data.auto_backup}
              >
                <SelectItem key="daily">Daily</SelectItem>
                <SelectItem key="weekly">Weekly</SelectItem>
                <SelectItem key="monthly">Monthly</SelectItem>
              </Select>
            </div>

            <div className="flex gap-2 mt-4">
              <Button
                variant="bordered"
                onPress={() => toast.info('Data export coming soon!')}
                isDisabled={!canManageSettings}
              >
                📤 Export Data
              </Button>
              <Button
                variant="bordered"
                onPress={() => toast.info('Backup creation coming soon!')}
                isDisabled={!canManageSettings}
              >
                💾 Create Backup
              </Button>
            </div>
          </div>

          {/* Danger Zone */}
          {canManageSettings && userRole === 'founder' && (
            <div>
              <h4 className="text-lg font-semibold mb-4 text-danger">⚠️ Danger Zone</h4>
              <Card className="bg-danger-50 dark:bg-danger-900/20 border border-danger-200">
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-danger">Delete Studio</div>
                      <div className="text-sm text-default-600">
                        Permanently delete this studio and all associated data
                      </div>
                    </div>
                    <Button
                      color="danger"
                      variant="bordered"
                      onPress={() => setShowDeleteModal(true)}
                    >
                      Delete Studio
                    </Button>
                  </div>
                </CardBody>
              </Card>
            </div>
          )}

          {/* Action Buttons */}
          {canManageSettings && (
            <div className="flex gap-2">
              <Button
                color="primary"
                onPress={saveSettings}
                isLoading={saving}
                isDisabled={saving}
              >
                💾 Save All Settings
              </Button>
              <Button
                variant="bordered"
                onPress={fetchSettings}
              >
                🔄 Reset Changes
              </Button>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Delete Confirmation Modal */}
      <Modal isOpen={showDeleteModal} onClose={() => setShowDeleteModal(false)}>
        <ModalContent>
          <ModalHeader>
            <span className="text-xl text-danger">⚠️ Delete Studio</span>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <p>
                This action will permanently delete your studio and all associated data, including:
              </p>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>All member data and roles</li>
                <li>Revenue sharing configurations</li>
                <li>Project history and contributions</li>
                <li>Financial records and transactions</li>
                <li>Settings and integrations</li>
              </ul>
              <div className="bg-danger-50 dark:bg-danger-900/20 p-3 rounded-lg">
                <p className="text-sm text-danger font-medium">
                  This action cannot be undone. Please type "DELETE" to confirm.
                </p>
              </div>
              <Input
                placeholder="Type DELETE to confirm"
                variant="bordered"
              />
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={() => setShowDeleteModal(false)}>
              Cancel
            </Button>
            <Button 
              color="danger" 
              onPress={() => toast.error('Studio deletion not implemented yet')}
            >
              Delete Studio
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default AllianceSettings;

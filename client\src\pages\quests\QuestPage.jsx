import React, { useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import QuestBoard from '../../components/quests/QuestBoard';

/**
 * Mission Page - Gamified Mission Discovery and Management Interface
 * 
 * This page provides the complete mission experience including:
 * - Mission discovery and browsing
 * - Mission progression tracking
 * - Achievement and reward system
 * - Skill-based mission recommendations
 * - Story elements and narrative progression
 * - Social mission features and leaderboards
 * 
 * Features:
 * - Bento grid layout following exact wireframe specifications
 * - Real-time mission data integration with backend APIs
 * - Complete gamification system with XP, levels, and achievements
 * - Professional mission management and progression tracking
 */
const QuestPage = () => {
  const { currentUser, loading } = useContext(UserContext);

  // Show loading state while authentication is being verified
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-default-600">Loading mission system...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!currentUser) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-6xl mb-6">🗺️</div>
          <h2 className="text-2xl font-bold mb-4">Mission Access Required</h2>
          <p className="text-default-600 mb-8 max-w-md mx-auto">
            Please log in to access the mission system and embark on epic adventures to unlock your potential.
          </p>
          <button
            onClick={() => window.location.href = '/login'}
            className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-600 transition-colors"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="mission-page min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20">
      {/* Page Header */}
      <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-8 mb-8">
        <div className="container mx-auto px-6">
          <div className="flex items-center gap-4">
            <span className="text-5xl">🗺️</span>
            <div>
              <h1 className="text-4xl font-bold mb-2">Mission Board</h1>
              <p className="text-purple-100 text-lg">
                Embark on epic missions, unlock achievements, and level up your skills
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 pb-12">
        <QuestBoard className="w-full" />
      </div>

      {/* Footer Info */}
      <div className="bg-default-50 dark:bg-default-900 py-8 mt-12">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-3xl mb-2">⭐</div>
              <h3 className="font-semibold mb-2">Level Up System</h3>
              <p className="text-sm text-default-600">
                Gain experience points and level up by completing missions and achieving milestones
              </p>
            </div>
            <div>
              <div className="text-3xl mb-2">🏆</div>
              <h3 className="font-semibold mb-2">Achievement System</h3>
              <p className="text-sm text-default-600">
                Unlock achievements and earn rewards for completing challenging missions and objectives
              </p>
            </div>
            <div>
              <div className="text-3xl mb-2">🎯</div>
              <h3 className="font-semibold mb-2">Skill Development</h3>
              <p className="text-sm text-default-600">
                Develop your skills through targeted missions and receive personalized recommendations
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuestPage;

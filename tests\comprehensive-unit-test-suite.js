/**
 * Comprehensive Unit Test Suite (50+ Tests)
 * 
 * This suite tests edge cases, boundary conditions, and various scenarios
 * to ensure the agreement generation system is robust and accurate.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 COMPREHENSIVE UNIT TEST SUITE (50+ TESTS)');
console.log('=' .repeat(60));
console.log('Testing edge cases, boundary conditions, and various scenarios');
console.log('Comparing all results against lawyer-approved example agreement');
console.log('=' .repeat(60));

/**
 * Test scenarios covering various edge cases and business scenarios
 */
const TEST_SCENARIOS = {
  // Core VOTA scenario (baseline)
  VOTA_BASELINE: {
    name: 'Village of The Ages',
    description: 'A village simulation game where players guide communities through historical progressions and manage resource-based challenges',
    project_type: 'game',
    company_name: 'City of Gamers Inc.',
    address: '1205 43rd Street, Suite B, Orlando, Florida 32839',
    contact_email: '<EMAIL>',
    city: 'Orlando',
    state: 'Florida',
    zip: '32839',
    county: 'Orange',
    legal_entity_type: 'corporation',
    incorporation_state: 'Florida',
    features: 'The game features dynamic challenges based on resource scarcity and natural disasters, creating an engaging gameplay loop.',
    coreFeatures: `1. **Village Building & Management**
   - Resource gathering and management
   - Building placement and construction
   - Population growth and management
   - Cultural evolution systems

2. **Historical Progression**
   - Players guide their community through multiple historical eras
   - Technology tree advancement
   - Cultural and social development
   - Architectural evolution

3. **Resource Management**
   - Dynamic weather systems affecting resources
   - Natural disasters and seasonal challenges
   - Trading systems with neighboring villages
   - Resource scarcity mechanics

4. **Interface Requirements**
   - Intuitive building placement system
   - Resource management dashboard
   - Population statistics and management panels
   - Technology and progression trackers`,
    technicalRequirements: `- Platform: PC (Steam, Epic Games Store)
- Engine: Unreal Engine 5
- MinSpecs: Standard hardware requirements for the target platforms
- ArtStyle: Stylized, readable visuals with distinctive aesthetics
- Audio: Atmospheric soundtrack with appropriate sound design
- VersionControl: Git-based source control with proper branching strategy`,
    roadmapPhases: `**Core Gameplay Development (Months 1-2)**
- Basic village layout and building system
- Core resource gathering mechanics
- Initial AI for villagers
- Basic UI framework
- First playable prototype with one historical era

**Feature Expansion (Months 2-3)**
- Additional historical eras
- Enhanced resource management systems
- Weather and disaster systems
- Trading mechanics
- Technology progression system

**Polish and Enhancement (Month 4)**
- UI refinement
- Performance optimization
- Additional content (buildings, resources, etc.)
- Balancing and gameplay tuning
- Audio implementation`,
    milestones: [
      {
        title: 'Core Gameplay Development',
        description: 'Basic village layout and building system',
        dueDate: 'Months 1-2'
      },
      {
        title: 'Resource Management System',
        description: 'Implementation of resource scarcity mechanics',
        dueDate: 'Months 3-4'
      },
      {
        title: 'Historical Progression Features',
        description: 'Time-based progression and historical events, architectural evolution through eras',
        dueDate: 'Months 5-6'
      }
    ],
    revenueShare: 33,
    payoutThreshold: 100000,
    maxPayment: 1000000
  },

  // Edge Case 1: Minimal data
  MINIMAL_DATA: {
    name: 'Test Game',
    description: 'A simple test game',
    project_type: 'game',
    company_name: 'Test Company Inc.',
    address: '123 Test St, Test City, TS 12345',
    contact_email: '<EMAIL>',
    city: 'Test City',
    state: 'Texas',
    zip: '12345',
    county: 'Test',
    legal_entity_type: 'corporation',
    incorporation_state: 'Texas',
    features: 'Basic game features',
    coreFeatures: '1. **Basic Feature**\n   - Simple gameplay',
    technicalRequirements: '- Platform: PC',
    roadmapPhases: '**Phase 1**\n- Basic development',
    milestones: [{ title: 'Basic Milestone', description: 'Basic work', dueDate: 'Month 1' }],
    revenueShare: 25,
    payoutThreshold: 50000,
    maxPayment: 500000
  },

  // Edge Case 2: Maximum data lengths
  MAXIMUM_DATA: {
    name: 'The Ultimate Comprehensive Multi-Platform Cross-Genre Revolutionary Gaming Experience of the Century',
    description: 'An incredibly detailed and comprehensive gaming experience that combines multiple genres, platforms, and innovative technologies to create the most immersive and engaging entertainment product ever conceived, featuring cutting-edge graphics, revolutionary gameplay mechanics, and unprecedented levels of player customization and interaction.',
    project_type: 'game',
    company_name: 'Ultra Mega Supreme Gaming Entertainment Corporation International Limited',
    address: '9999 Ultra Long Street Name Boulevard, Suite 999999, Ultra Maximum City Name, Ultra State 99999-9999',
    contact_email: '<EMAIL>',
    city: 'Ultra Maximum City Name',
    state: 'California',
    zip: '99999-9999',
    county: 'Ultra Maximum County',
    legal_entity_type: 'corporation',
    incorporation_state: 'Delaware',
    features: 'This game features an unprecedented array of cutting-edge technologies, revolutionary gameplay mechanics, immersive storytelling elements, comprehensive character customization systems, dynamic world generation, advanced AI systems, and innovative multiplayer experiences.',
    coreFeatures: `1. **Revolutionary Gameplay Systems**
   - Advanced AI-driven dynamic storytelling
   - Procedural world generation with infinite possibilities
   - Real-time physics simulation with unprecedented accuracy
   - Comprehensive character progression and customization
   - Multi-dimensional combat systems with tactical depth

2. **Cutting-Edge Technology Integration**
   - Virtual reality and augmented reality support
   - Advanced machine learning for personalized experiences
   - Cloud-based processing for enhanced performance
   - Cross-platform synchronization and compatibility
   - Blockchain integration for digital asset ownership

3. **Immersive World Building**
   - Photorealistic graphics with ray tracing technology
   - Dynamic weather and environmental systems
   - Realistic ecosystem simulation and management
   - Cultural and historical accuracy in world design
   - Interactive narrative with branching storylines`,
    technicalRequirements: `- Platform: PC, PlayStation 5, Xbox Series X/S, Nintendo Switch, Mobile (iOS/Android), VR (Oculus, PSVR2)
- Engine: Custom-built proprietary engine with Unreal Engine 5 integration
- MinSpecs: High-end gaming PC with RTX 3080 or equivalent
- RecommendedSpecs: Latest generation hardware with RTX 4090 or equivalent
- ArtStyle: Photorealistic with stylized elements and dynamic lighting
- Audio: 3D spatial audio with Dolby Atmos support and dynamic soundtrack
- NetworkingRequirements: High-speed internet for cloud processing and multiplayer
- StorageRequirements: 500GB+ available space with SSD recommended`,
    roadmapPhases: `**Pre-Production and Concept Development (Months 1-6)**
- Comprehensive market research and competitive analysis
- Detailed game design document creation and refinement
- Technical feasibility studies and prototype development
- Art style guide creation and visual target establishment
- Core team assembly and role definition

**Production Phase Alpha (Months 7-18)**
- Core engine development and optimization
- Basic gameplay systems implementation
- Initial art asset creation and pipeline establishment
- Fundamental networking and multiplayer infrastructure
- Alpha testing with internal teams and focus groups

**Production Phase Beta (Months 19-30)**
- Advanced feature implementation and polish
- Comprehensive content creation and world building
- Extensive testing and quality assurance processes
- Performance optimization and platform-specific adaptations
- Beta testing with external user groups and feedback integration

**Launch Preparation and Post-Launch Support (Months 31-36)**
- Final polish and bug fixing processes
- Marketing campaign execution and community building
- Distribution platform integration and certification
- Launch day preparation and monitoring systems
- Post-launch content updates and community support`,
    milestones: [
      { title: 'Concept Completion', description: 'Complete game design and technical specifications', dueDate: 'Month 6' },
      { title: 'Alpha Build', description: 'Playable alpha version with core features', dueDate: 'Month 18' },
      { title: 'Beta Release', description: 'Feature-complete beta for external testing', dueDate: 'Month 30' },
      { title: 'Gold Master', description: 'Final release candidate ready for distribution', dueDate: 'Month 36' }
    ],
    revenueShare: 50,
    payoutThreshold: 1000000,
    maxPayment: 10000000
  },

  // Edge Case 3: Special characters and formatting
  SPECIAL_CHARACTERS: {
    name: 'Café & Dragons: The Ñoël Chronicles™',
    description: 'A fantasy RPG featuring café management with special characters: àáâãäåæçèéêë & symbols like @#$%^&*()[]{}',
    project_type: 'game',
    company_name: 'Spéciál Cháracters Gaming LLC',
    address: '123 Café Street, Montréal, QC H1A 1A1',
    contact_email: 'contact@spéciál-gaming.com',
    city: 'Montréal',
    state: 'Quebec',
    zip: 'H1A 1A1',
    county: 'Montréal',
    legal_entity_type: 'llc',
    incorporation_state: 'Quebec',
    features: 'Features include: café management, dragon taming, special characters (àáâãäåæçèéêë), and symbols (@#$%^&*)',
    coreFeatures: '1. **Café Management™**\n   - Special menu items with àáâãäåæçèéêë\n   - Currency: €$¥£',
    technicalRequirements: '- Platform: PC & Mac\n- Special: Unicode support required',
    roadmapPhases: '**Phase Ñoël**\n- Implement special characters\n- Add café features',
    milestones: [{ title: 'Café™ System', description: 'Implement café with special chars', dueDate: 'Décembre' }],
    revenueShare: 33,
    payoutThreshold: 100000,
    maxPayment: 1000000
  },

  // Edge Case 4: Different business types
  SOFTWARE_SAAS: {
    name: 'CloudFlow Enterprise',
    description: 'A comprehensive SaaS platform for enterprise workflow management',
    project_type: 'software',
    company_name: 'Enterprise Solutions Corp.',
    address: '456 Business Ave, Corporate City, NY 10001',
    contact_email: '<EMAIL>',
    city: 'Corporate City',
    state: 'New York',
    zip: '10001',
    county: 'Manhattan',
    legal_entity_type: 'corporation',
    incorporation_state: 'Delaware',
    features: 'Enterprise-grade workflow automation, real-time collaboration, advanced analytics',
    coreFeatures: '1. **Workflow Automation**\n   - Process management\n   - Task automation',
    technicalRequirements: '- Platform: Web-based (React/Node.js)\n- Database: PostgreSQL',
    roadmapPhases: '**MVP Development**\n- Core features\n- User interface',
    milestones: [{ title: 'MVP Launch', description: 'Minimum viable product', dueDate: 'Q2' }],
    revenueShare: 25,
    payoutThreshold: 250000,
    maxPayment: 2000000
  },

  // Edge Case 5: Different legal entities
  LLC_ENTITY: {
    name: 'Indie Game Studio',
    description: 'Small indie game development',
    project_type: 'game',
    company_name: 'Indie Developers LLC',
    address: '789 Indie Lane, Austin, TX 78701',
    contact_email: '<EMAIL>',
    city: 'Austin',
    state: 'Texas',
    zip: '78701',
    county: 'Travis',
    legal_entity_type: 'llc',
    incorporation_state: 'Texas',
    features: 'Indie game development with creative freedom',
    coreFeatures: '1. **Creative Gameplay**\n   - Unique mechanics',
    technicalRequirements: '- Platform: Steam\n- Engine: Unity',
    roadmapPhases: '**Development**\n- Core game creation',
    milestones: [{ title: 'Alpha', description: 'First playable version', dueDate: 'Month 3' }],
    revenueShare: 40,
    payoutThreshold: 75000,
    maxPayment: 750000
  }
};

/**
 * Generate additional test scenarios programmatically
 */
function generateAdditionalTestScenarios() {
  const additionalScenarios = {};
  
  // Revenue share edge cases
  const revenueShares = [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 60, 70, 80, 90, 99];
  revenueShares.forEach((share, index) => {
    additionalScenarios[`REVENUE_${share}PCT`] = {
      ...TEST_SCENARIOS.VOTA_BASELINE,
      name: `Revenue Test ${share}%`,
      revenueShare: share,
      payoutThreshold: 50000 + (index * 10000),
      maxPayment: 500000 + (index * 100000)
    };
  });

  // Payout threshold edge cases
  const thresholds = [1000, 5000, 10000, 25000, 50000, 75000, 100000, 250000, 500000, 1000000];
  thresholds.forEach((threshold, index) => {
    additionalScenarios[`THRESHOLD_${threshold}`] = {
      ...TEST_SCENARIOS.VOTA_BASELINE,
      name: `Threshold Test $${threshold.toLocaleString()}`,
      payoutThreshold: threshold,
      maxPayment: threshold * 10
    };
  });

  // Different states and jurisdictions
  const states = [
    { state: 'California', city: 'Los Angeles', county: 'Los Angeles', zip: '90210' },
    { state: 'New York', city: 'New York', county: 'New York', zip: '10001' },
    { state: 'Texas', city: 'Houston', county: 'Harris', zip: '77001' },
    { state: 'Florida', city: 'Miami', county: 'Miami-Dade', zip: '33101' },
    { state: 'Washington', city: 'Seattle', county: 'King', zip: '98101' },
    { state: 'Illinois', city: 'Chicago', county: 'Cook', zip: '60601' },
    { state: 'Massachusetts', city: 'Boston', county: 'Suffolk', zip: '02101' },
    { state: 'Colorado', city: 'Denver', county: 'Denver', zip: '80201' }
  ];
  
  states.forEach((location, index) => {
    additionalScenarios[`STATE_${location.state.toUpperCase().replace(' ', '_')}`] = {
      ...TEST_SCENARIOS.VOTA_BASELINE,
      name: `${location.state} Project`,
      city: location.city,
      state: location.state,
      county: location.county,
      zip: location.zip,
      address: `${100 + index} Main Street, ${location.city}, ${location.state} ${location.zip}`,
      incorporation_state: location.state
    };
  });

  // Different project types
  const projectTypes = ['game', 'software', 'mobile_app', 'web_platform', 'ai_system', 'blockchain'];
  projectTypes.forEach(type => {
    additionalScenarios[`TYPE_${type.toUpperCase()}`] = {
      ...TEST_SCENARIOS.VOTA_BASELINE,
      name: `${type.replace('_', ' ')} Project`,
      project_type: type,
      description: `A comprehensive ${type.replace('_', ' ')} development project`
    };
  });

  return additionalScenarios;
}

// Combine all test scenarios
const ALL_TEST_SCENARIOS = {
  ...TEST_SCENARIOS,
  ...generateAdditionalTestScenarios()
};

console.log(`📊 Total test scenarios generated: ${Object.keys(ALL_TEST_SCENARIOS).length}`);

/**
 * Load the agreement generator and template
 */
async function loadAgreementGenerator() {
  try {
    const { NewAgreementGenerator } = await import('../client/src/utils/agreement/newAgreementGenerator.js');
    const templatePath = path.join(__dirname, '../client/public/contributor-agreement-template.md');
    const templateText = fs.readFileSync(templatePath, 'utf8');
    
    return { NewAgreementGenerator, templateText };
  } catch (error) {
    console.error('❌ Failed to load agreement generator:', error);
    throw error;
  }
}

/**
 * Load the lawyer-approved example for comparison
 */
function loadLawyerExample() {
  const examplePath = path.join(__dirname, '../client/public/example-cog-contributor-agreement.md');
  return fs.readFileSync(examplePath, 'utf8');
}

/**
 * Run a single test scenario
 */
async function runTestScenario(scenarioName, projectData, generator, templateText) {
  try {
    const options = {
      contributors: [{
        id: 'test_contributor',
        email: '<EMAIL>',
        name: 'Test Contributor',
        address: '123 Test St, Test City, TS 12345'
      }],
      currentUser: { id: 'test_user', email: '<EMAIL>' },
      fullName: 'Test Contributor',
      agreementDate: new Date('2025-01-01'), // Fixed date for consistency

      // Include alliance information for proper company validation
      allianceInfo: {
        id: 'test_alliance_12345',
        name: 'Test Alliance',
        owner_id: 'test_user',
        contact_information: {
          primaryContact: {
            name: 'Test Signer',
            email: '<EMAIL>',
            title: 'President'
          }
        },
        company_info: {
          legal_name: projectData.company_name,
          address: projectData.address,
          state: projectData.state,
          city: projectData.city,
          zip: projectData.zip,
          county: projectData.county,
          billing_email: projectData.contact_email,
          legal_entity_type: projectData.legal_entity_type,
          incorporation_state: projectData.incorporation_state
        }
      }
    };

    const startTime = Date.now();
    const agreement = await generator.generateAgreement(templateText, projectData, options);
    const endTime = Date.now();

    return {
      success: true,
      agreement,
      generationTime: endTime - startTime,
      length: agreement.length
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      generationTime: 0,
      length: 0
    };
  }
}

/**
 * Compare generated agreement with lawyer-approved example
 */
function compareWithLawyerExample(agreement, lawyerExample, scenarioName) {
  const comparison = {
    scenarioName,
    structuralAccuracy: 0,
    contentAccuracy: 0,
    financialAccuracy: 0,
    legalAccuracy: 0,
    overallAccuracy: 0,
    issues: [],
    criticalIssues: [],
    warnings: []
  };

  // Essential structural elements
  const structuralElements = [
    'CONTRIBUTOR AGREEMENT',
    'Definitions',
    'Treatment of Confidential Information',
    'Ownership of Work Product',
    'Non-Disparagement',
    'Termination',
    'Equitable Remedies',
    'Assignment',
    'Waivers and Amendments',
    'Survival',
    'Status as Independent Contractor',
    'Representations and Warranties',
    'Indemnification',
    'Entire Agreement',
    'Governing Law',
    'Consent to Jurisdiction',
    'Settlement of Disputes',
    'SCHEDULE A',
    'SCHEDULE B',
    'EXHIBIT I',
    'EXHIBIT II'
  ];

  let structuralMatches = 0;
  structuralElements.forEach(element => {
    if (agreement.includes(element)) {
      structuralMatches++;
    } else {
      comparison.criticalIssues.push(`Missing critical structural element: ${element}`);
    }
  });
  comparison.structuralAccuracy = Math.round((structuralMatches / structuralElements.length) * 100);

  // Financial elements validation
  const financialElements = [
    'Revenue Tranch',
    'Contribution Points',
    'Payment Schedule',
    'Quarterly reports',
    'Revenue Share Percentage',
    'Minimum Threshold for Payout',
    'Maximum Individual Payment'
  ];

  let financialMatches = 0;
  financialElements.forEach(element => {
    if (agreement.includes(element)) {
      financialMatches++;
    } else {
      comparison.issues.push(`Missing financial element: ${element}`);
    }
  });
  comparison.financialAccuracy = Math.round((financialMatches / financialElements.length) * 100);

  // Legal terms validation
  const legalElements = [
    'Confidential Information',
    'Work Product',
    'intellectual property',
    'non-compete',
    'termination',
    'governing law',
    'jurisdiction',
    'arbitration',
    'indemnification',
    'representations and warranties'
  ];

  let legalMatches = 0;
  legalElements.forEach(element => {
    if (agreement.toLowerCase().includes(element.toLowerCase())) {
      legalMatches++;
    } else {
      comparison.warnings.push(`Missing or unclear legal element: ${element}`);
    }
  });
  comparison.legalAccuracy = Math.round((legalMatches / legalElements.length) * 100);

  // Content validation (project-specific)
  const hasProjectName = agreement.includes('Village of The Ages') || agreement.includes('Test') || agreement.includes('Game');
  const hasCompanyName = agreement.includes('City of Gamers') || agreement.includes('Company') || agreement.includes('Corp');
  const hasDescription = agreement.length > 10000; // Reasonable length check

  const contentMatches = [hasProjectName, hasCompanyName, hasDescription].filter(Boolean).length;
  comparison.contentAccuracy = Math.round((contentMatches / 3) * 100);

  // Overall accuracy
  comparison.overallAccuracy = Math.round(
    (comparison.structuralAccuracy + comparison.financialAccuracy + comparison.legalAccuracy + comparison.contentAccuracy) / 4
  );

  return comparison;
}

/**
 * Main test execution function
 */
async function runComprehensiveUnitTests() {
  console.log('\n🚀 Starting Comprehensive Unit Test Suite...\n');

  const results = {
    timestamp: new Date().toISOString(),
    totalTests: Object.keys(ALL_TEST_SCENARIOS).length,
    passedTests: 0,
    failedTests: 0,
    testResults: {},
    summary: {
      averageAccuracy: 0,
      averageGenerationTime: 0,
      criticalIssuesFound: 0,
      commonIssues: {},
      bestPerformingScenarios: [],
      worstPerformingScenarios: []
    }
  };

  try {
    // Load dependencies
    console.log('📚 Loading agreement generator and template...');
    const { NewAgreementGenerator, templateText } = await loadAgreementGenerator();
    const generator = new NewAgreementGenerator();
    const lawyerExample = loadLawyerExample();
    console.log('✅ Dependencies loaded successfully\n');

    // Run all test scenarios
    let testCount = 0;
    const totalTests = results.totalTests;

    for (const [scenarioName, projectData] of Object.entries(ALL_TEST_SCENARIOS)) {
      testCount++;
      process.stdout.write(`\r🧪 Running test ${testCount}/${totalTests}: ${scenarioName.padEnd(30)}`);

      // Run the test
      const testResult = await runTestScenario(scenarioName, projectData, generator, templateText);

      if (testResult.success) {
        // Compare with lawyer example
        const comparison = compareWithLawyerExample(testResult.agreement, lawyerExample, scenarioName);

        testResult.comparison = comparison;
        results.passedTests++;

        // Track common issues
        comparison.issues.forEach(issue => {
          results.summary.commonIssues[issue] = (results.summary.commonIssues[issue] || 0) + 1;
        });

        if (comparison.criticalIssues.length > 0) {
          results.summary.criticalIssuesFound++;
        }
      } else {
        results.failedTests++;
      }

      results.testResults[scenarioName] = testResult;
    }

    console.log('\n\n📊 Analyzing results...');

    // Calculate summary statistics
    const successfulTests = Object.values(results.testResults).filter(r => r.success);

    if (successfulTests.length > 0) {
      results.summary.averageAccuracy = Math.round(
        successfulTests.reduce((sum, test) => sum + (test.comparison?.overallAccuracy || 0), 0) / successfulTests.length
      );

      results.summary.averageGenerationTime = Math.round(
        successfulTests.reduce((sum, test) => sum + test.generationTime, 0) / successfulTests.length
      );

      // Find best and worst performing scenarios
      const sortedByAccuracy = successfulTests
        .filter(test => test.comparison)
        .sort((a, b) => (b.comparison.overallAccuracy || 0) - (a.comparison.overallAccuracy || 0));

      results.summary.bestPerformingScenarios = sortedByAccuracy.slice(0, 5).map(test => ({
        scenario: test.comparison.scenarioName,
        accuracy: test.comparison.overallAccuracy
      }));

      results.summary.worstPerformingScenarios = sortedByAccuracy.slice(-5).map(test => ({
        scenario: test.comparison.scenarioName,
        accuracy: test.comparison.overallAccuracy
      }));
    }

    // Display results
    displayTestResults(results);

    // Save detailed results
    const reportPath = path.join(__dirname, 'comprehensive-unit-test-results.json');
    fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
    console.log(`\n💾 Detailed results saved to: ${reportPath}`);

    // Generate VOTA agreement for display
    console.log('\n📄 Generating VOTA agreement for display...');
    const votaResult = await runTestScenario('VOTA_DISPLAY', ALL_TEST_SCENARIOS.VOTA_BASELINE, generator, templateText);
    if (votaResult.success) {
      const votaPath = path.join(__dirname, 'generated-vota-agreement.md');
      fs.writeFileSync(votaPath, votaResult.agreement);
      console.log(`✅ VOTA agreement saved to: ${votaPath}`);
    }

    return results;

  } catch (error) {
    console.error('\n❌ Test suite failed:', error);
    results.error = error.message;
    return results;
  }
}

/**
 * Display comprehensive test results
 */
function displayTestResults(results) {
  console.log('\n🎯 COMPREHENSIVE UNIT TEST RESULTS');
  console.log('=' .repeat(60));

  console.log(`📊 Total Tests: ${results.totalTests}`);
  console.log(`✅ Passed: ${results.passedTests}`);
  console.log(`❌ Failed: ${results.failedTests}`);
  console.log(`📈 Success Rate: ${Math.round((results.passedTests / results.totalTests) * 100)}%`);
  console.log(`🎯 Average Accuracy: ${results.summary.averageAccuracy}%`);
  console.log(`⚡ Average Generation Time: ${results.summary.averageGenerationTime}ms`);
  console.log(`⚠️  Critical Issues Found: ${results.summary.criticalIssuesFound} scenarios`);

  if (results.summary.bestPerformingScenarios.length > 0) {
    console.log('\n🏆 TOP 5 PERFORMING SCENARIOS:');
    results.summary.bestPerformingScenarios.forEach((scenario, index) => {
      console.log(`   ${index + 1}. ${scenario.scenario}: ${scenario.accuracy}%`);
    });
  }

  if (results.summary.worstPerformingScenarios.length > 0) {
    console.log('\n⚠️  BOTTOM 5 PERFORMING SCENARIOS:');
    results.summary.worstPerformingScenarios.forEach((scenario, index) => {
      console.log(`   ${index + 1}. ${scenario.scenario}: ${scenario.accuracy}%`);
    });
  }

  if (Object.keys(results.summary.commonIssues).length > 0) {
    console.log('\n🔍 MOST COMMON ISSUES:');
    const sortedIssues = Object.entries(results.summary.commonIssues)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10);

    sortedIssues.forEach(([issue, count]) => {
      console.log(`   • ${issue} (${count} occurrences)`);
    });
  }

  // Overall assessment
  console.log('\n🎯 OVERALL ASSESSMENT:');
  if (results.summary.averageAccuracy >= 95) {
    console.log('🎉 EXCELLENT: System performs exceptionally well across all scenarios');
  } else if (results.summary.averageAccuracy >= 85) {
    console.log('👍 GOOD: System performs well with minor issues to address');
  } else if (results.summary.averageAccuracy >= 70) {
    console.log('⚠️  FAIR: System needs improvements for production readiness');
  } else {
    console.log('❌ POOR: System requires significant improvements');
  }
}

// Run the comprehensive unit test suite
runComprehensiveUnitTests()
  .then(results => {
    const success = results.summary.averageAccuracy >= 85 && results.failedTests === 0;
    console.log(`\n${success ? '✅' : '❌'} Comprehensive unit test suite ${success ? 'PASSED' : 'FAILED'}`);
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('\n❌ Test suite failed:', error.message);
    process.exit(1);
  });

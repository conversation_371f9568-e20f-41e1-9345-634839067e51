#!/usr/bin/env node

/**
 * Lawyer Example Recreation Test
 * 
 * Tests the ability to recreate the lawyer-approved example agreement 1:1
 * using the exact data from that agreement through the platform's user input system.
 */

import fs from 'fs';
import { ExhibitGenerator } from './client/src/utils/agreement/v2/ExhibitGenerator.js';

class LawyerExampleRecreationTest {
  constructor() {
    this.templatePath = 'client/public/templates/v2/standard-contributor-agreement.md';
    this.lawyerExamplePath = 'client/public/example-cog-contributor-agreement.md';
    this.outputPath = 'recreated-lawyer-example.md';
    this.exhibitGenerator = new ExhibitGenerator();
  }

  async runRecreationTest() {
    console.log('🎯 Lawyer Example Recreation Test');
    console.log('=================================');
    console.log('Goal: Recreate the lawyer example 1:1 using platform user input');

    try {
      // Step 1: Extract exact data from lawyer example
      console.log('\n📋 Step 1: Extracting Data from Lawyer Example');
      const lawyerData = this.extractLawyerExampleData();
      this.displayExtractedData(lawyerData);

      // Step 2: Generate agreement using extracted data
      console.log('\n⚙️  Step 2: Generating Agreement with Lawyer Data');
      const recreatedAgreement = await this.generateAgreementFromLawyerData(lawyerData);

      // Step 3: Save recreated agreement
      console.log('\n💾 Step 3: Saving Recreated Agreement');
      fs.writeFileSync(this.outputPath, recreatedAgreement);
      console.log(`✅ Recreated agreement saved to: ${this.outputPath}`);

      // Step 4: Compare with original lawyer example
      console.log('\n🔍 Step 4: 1:1 Comparison Analysis');
      await this.compareWithOriginal(recreatedAgreement);

      console.log('\n🏁 Recreation Test Complete');

    } catch (error) {
      console.error('❌ Recreation test failed:', error.message);
      console.error(error.stack);
    }
  }

  extractLawyerExampleData() {
    // Extract the exact data that would be entered by a user to recreate the lawyer example
    return {
      company: {
        name: 'City of Gamers Inc.',
        legalName: 'City of Gamers Inc.',
        address: '1205 43rd Street, Suite B, Orlando, Florida 32839',
        state: 'Florida',
        city: 'Orlando',
        signerName: 'Gynell Journigan',
        signerTitle: 'Chief Executive Officer',
        billingEmail: '<EMAIL>' // Inferred
      },
      project: {
        name: 'Village of The Ages',
        description: 'Village of The Ages is a village simulation game where players guide communities through historical progressions and manage resource-based challenges. The game features dynamic challenges based on resource scarcity and natural disasters, creating an engaging gameplay loop.',
        projectType: 'game',
        specifications: {
          coreFeatures: [
            {
              name: 'Village Building & Management',
              details: [
                'Resource gathering and management',
                'Building placement and upgrade systems',
                'Population growth and happiness mechanics',
                'Economic systems and trade routes'
              ]
            },
            {
              name: 'Historical Progression',
              details: [
                'Era-based technology trees',
                'Cultural and social evolution',
                'Historical events and their impacts',
                'Adaptation to changing times'
              ]
            },
            {
              name: 'Resource-Based Challenges',
              details: [
                'Seasonal resource variations',
                'Natural disaster management',
                'Scarcity-driven decision making',
                'Environmental adaptation'
              ]
            },
            {
              name: 'Dynamic Challenge System',
              details: [
                'Procedurally generated events',
                'Adaptive difficulty based on player performance',
                'Multiple solution paths for challenges',
                'Long-term consequence systems'
              ]
            }
          ],
          technical: {
            'Engine': 'Unreal Engine 5',
            'Minimum Specs': '[To be detailed in technical documentation]',
            'Art Style': 'Stylized, readable visuals with distinctive era-appropriate aesthetics',
            'Audio': 'Atmospheric soundtrack that evolves with historical periods'
          },
          deliverables: [
            'Playable game build with all core features',
            'Complete source code and project files',
            'Art assets and technical documentation',
            'Audio implementation and sound design',
            'QA testing and bug reports'
          ]
        }
      },
      contributor: {
        name: '[Contributor Name]', // This would be filled in by actual contributor
        email: '[Contributor Email]',
        address: '[Contributor Address]'
      },
      milestones: {
        phases: [
          {
            name: 'Phase 1: Core Gameplay Development (Months 1-2)',
            tasks: [
              'Basic village layout and building system',
              'Core resource gathering mechanics',
              'Initial AI for villagers',
              'Basic UI framework',
              'First playable prototype with one historical era'
            ]
          },
          {
            name: 'Phase 2: Feature Expansion (Months 2-3)',
            tasks: [
              'Additional historical eras',
              'Enhanced resource management systems',
              'Weather and disaster systems',
              'Trading mechanics',
              'Technology progression system'
            ]
          },
          {
            name: 'Phase 3: Polish and Enhancement (Month 4)',
            tasks: [
              'UI refinement',
              'Performance optimization',
              'Additional content (buildings, resources, etc.)',
              'Balancing and gameplay tuning',
              'Audio implementation'
            ]
          },
          {
            name: 'Phase 4: Testing and Launch Preparation (Month 4)',
            tasks: [
              'QA testing',
              'Bug fixing',
              'Marketing assets preparation',
              'Steam/Epic Games Store setup',
              'Early access launch'
            ]
          }
        ]
      },
      scheduleB: {
        contributionPointSystem: {
          basePoints: 1,
          complexityMultiplier: '1.5x for complex tasks',
          qualityBonus: 'Up to 25% bonus for exceptional quality',
          timeCommitment: 'Points awarded based on actual time invested'
        },
        revenueTranch: {
          percentage: '15% of post-expense Revenue',
          minimumThreshold: '$100,000 in post-expense Revenue',
          maximumIndividualPayment: '$1,000,000 per Developer',
          paymentSchedule: 'Quarterly within 45 days after end of quarter'
        }
      }
    };
  }

  displayExtractedData(data) {
    console.log('  📊 Extracted Data Summary:');
    console.log(`    Company: ${data.company.name} (${data.company.state})`);
    console.log(`    Project: ${data.project.name} (${data.project.projectType})`);
    console.log(`    Core Features: ${data.project.specifications.coreFeatures.length}`);
    console.log(`    Development Phases: ${data.milestones.phases.length}`);
    console.log(`    Technical Requirements: ${Object.keys(data.project.specifications.technical).length}`);
  }

  async generateAgreementFromLawyerData(lawyerData) {
    console.log('  🔄 Loading template...');
    const template = fs.readFileSync(this.templatePath, 'utf8');

    console.log('  🔄 Processing variables...');
    let agreement = template;

    // Replace basic variables
    agreement = agreement.replace(/\{\{COMPANY_NAME\}\}/g, lawyerData.company.name.toUpperCase());
    agreement = agreement.replace(/\{\{COMPANY_LEGAL_NAME\}\}/g, lawyerData.company.legalName);
    agreement = agreement.replace(/\{\{COMPANY_ADDRESS\}\}/g, lawyerData.company.address);
    agreement = agreement.replace(/\{\{COMPANY_STATE\}\}/g, lawyerData.company.state);
    agreement = agreement.replace(/\{\{COMPANY_SIGNER_NAME\}\}/g, lawyerData.company.signerName);
    agreement = agreement.replace(/\{\{COMPANY_SIGNER_TITLE\}\}/g, lawyerData.company.signerTitle);
    agreement = agreement.replace(/\{\{COMPANY_BILLING_EMAIL\}\}/g, lawyerData.company.billingEmail);

    agreement = agreement.replace(/\{\{PROJECT_NAME\}\}/g, lawyerData.project.name);
    agreement = agreement.replace(/\{\{PROJECT_DESCRIPTION\}\}/g, lawyerData.project.description);

    agreement = agreement.replace(/\{\{CONTRIBUTOR_NAME\}\}/g, lawyerData.contributor.name);
    agreement = agreement.replace(/\{\{CONTRIBUTOR_EMAIL\}\}/g, lawyerData.contributor.email);
    agreement = agreement.replace(/\{\{CONTRIBUTOR_ADDRESS\}\}/g, lawyerData.contributor.address);

    // Process project type conditionals
    const projectType = lawyerData.project.projectType.toLowerCase();
    const projectTypeFlags = {
      software: projectType === 'software',
      game: projectType === 'game',
      music: projectType === 'music',
      film: projectType === 'film',
      art: projectType === 'art'
    };

    Object.entries(projectTypeFlags).forEach(([type, isActive]) => {
      const upperType = type.toUpperCase();
      const conditionalPattern = new RegExp(
        `\\{\\{#IF PROJECT_TYPE_${upperType}\\}\\}([\\s\\S]*?)\\{\\{/IF\\}\\}`,
        'g'
      );
      
      if (isActive) {
        agreement = agreement.replace(conditionalPattern, '$1');
      } else {
        agreement = agreement.replace(conditionalPattern, '');
      }
    });

    console.log('  🔄 Generating dynamic exhibits...');
    
    // Generate dynamic exhibits using the ExhibitGenerator
    const exhibitI = this.exhibitGenerator.generateExhibitI(lawyerData.project);
    const exhibitII = this.exhibitGenerator.generateExhibitII(lawyerData.project, lawyerData.milestones);

    // Replace the generic exhibit placeholders with the generated content
    agreement = agreement.replace(
      /## EXHIBIT I[\s\S]*?(?=## EXHIBIT II)/,
      exhibitI + '\n---\n\n'
    );
    
    agreement = agreement.replace(
      /## EXHIBIT II[\s\S]*$/,
      exhibitII
    );

    // Clean up formatting
    agreement = agreement.replace(/\n\s*\n\s*\n/g, '\n\n');
    agreement = agreement.trim();

    console.log(`  ✅ Agreement generated: ${agreement.length} characters`);
    return agreement;
  }

  async compareWithOriginal(recreatedAgreement) {
    const originalLawyer = fs.readFileSync(this.lawyerExamplePath, 'utf8');

    console.log('📊 1:1 Comparison Metrics:');
    console.log(`  Original Lawyer: ${originalLawyer.length} characters`);
    console.log(`  Recreated: ${recreatedAgreement.length} characters`);
    console.log(`  Size Ratio: ${Math.round((recreatedAgreement.length / originalLawyer.length) * 100)}%`);

    // Structure comparison
    console.log('\n🏗️  Structure Comparison:');
    this.compareStructuralElements(recreatedAgreement, originalLawyer);

    // Content comparison
    console.log('\n📝 Content Comparison:');
    this.compareSpecificContent(recreatedAgreement, originalLawyer);

    // Exhibit comparison
    console.log('\n📋 Exhibit Comparison:');
    this.compareExhibits(recreatedAgreement, originalLawyer);

    // Overall accuracy assessment
    console.log('\n🎯 1:1 Recreation Assessment:');
    this.assessRecreationAccuracy(recreatedAgreement, originalLawyer);
  }

  compareStructuralElements(recreated, original) {
    const structuralElements = [
      'CITY OF GAMERS INC.',
      'Village of The Ages',
      'Gynell Journigan',
      'Chief Executive Officer',
      'Florida',
      'Orlando',
      'SCHEDULE A',
      'SCHEDULE B',
      'EXHIBIT I',
      'EXHIBIT II'
    ];

    let matches = 0;
    structuralElements.forEach(element => {
      const inRecreated = recreated.includes(element);
      const inOriginal = original.includes(element);
      
      if (inRecreated && inOriginal) {
        console.log(`  ✅ ${element} - Present in both`);
        matches++;
      } else if (inOriginal && !inRecreated) {
        console.log(`  ❌ ${element} - Missing from recreation`);
      } else if (inRecreated && !inOriginal) {
        console.log(`  ⚠️  ${element} - In recreation but not original`);
      }
    });

    console.log(`  📊 Structural Match: ${matches}/${structuralElements.length} (${Math.round((matches/structuralElements.length)*100)}%)`);
  }

  compareSpecificContent(recreated, original) {
    // Check for key phrases that should match exactly
    const keyPhrases = [
      'village simulation game',
      'historical progressions',
      'resource-based challenges',
      'Unreal Engine 5',
      'Stylized, readable visuals'
    ];

    let contentMatches = 0;
    keyPhrases.forEach(phrase => {
      if (recreated.includes(phrase) && original.includes(phrase)) {
        console.log(`  ✅ "${phrase}" - Exact match`);
        contentMatches++;
      } else if (original.includes(phrase) && !recreated.includes(phrase)) {
        console.log(`  ❌ "${phrase}" - Missing from recreation`);
      }
    });

    console.log(`  📊 Content Match: ${contentMatches}/${keyPhrases.length} (${Math.round((contentMatches/keyPhrases.length)*100)}%)`);
  }

  compareExhibits(recreated, original) {
    // Check if exhibits contain the expected content
    const exhibitChecks = [
      { name: 'Village Building & Management', section: 'EXHIBIT I' },
      { name: 'Historical Progression', section: 'EXHIBIT I' },
      { name: 'Phase 1: Core Gameplay Development', section: 'EXHIBIT II' },
      { name: 'Basic village layout and building system', section: 'EXHIBIT II' }
    ];

    let exhibitMatches = 0;
    exhibitChecks.forEach(check => {
      if (recreated.includes(check.name)) {
        console.log(`  ✅ ${check.section}: "${check.name}" - Present`);
        exhibitMatches++;
      } else {
        console.log(`  ❌ ${check.section}: "${check.name}" - Missing`);
      }
    });

    console.log(`  📊 Exhibit Content: ${exhibitMatches}/${exhibitChecks.length} (${Math.round((exhibitMatches/exhibitChecks.length)*100)}%)`);
  }

  assessRecreationAccuracy(recreated, original) {
    // Calculate overall recreation accuracy
    let score = 0;
    let maxScore = 100;

    // Structure accuracy (40 points)
    const structuralElements = ['CITY OF GAMERS INC.', 'Village of The Ages', 'Gynell Journigan', 'Florida'];
    const structuralMatches = structuralElements.filter(el => recreated.includes(el) && original.includes(el)).length;
    const structuralScore = (structuralMatches / structuralElements.length) * 40;
    score += structuralScore;

    // Content accuracy (30 points)
    const keyPhrases = ['village simulation game', 'historical progressions', 'Unreal Engine 5'];
    const contentMatches = keyPhrases.filter(phrase => recreated.includes(phrase) && original.includes(phrase)).length;
    const contentScore = (contentMatches / keyPhrases.length) * 30;
    score += contentScore;

    // Exhibit accuracy (30 points)
    const exhibitElements = ['Village Building & Management', 'Phase 1: Core Gameplay Development'];
    const exhibitMatches = exhibitElements.filter(el => recreated.includes(el)).length;
    const exhibitScore = (exhibitMatches / exhibitElements.length) * 30;
    score += exhibitScore;

    const finalScore = Math.round(score);

    console.log(`📊 Overall Recreation Score: ${finalScore}%`);
    console.log(`  - Structural Elements: ${Math.round(structuralScore)}/40`);
    console.log(`  - Content Accuracy: ${Math.round(contentScore)}/30`);
    console.log(`  - Exhibit Generation: ${Math.round(exhibitScore)}/30`);

    if (finalScore >= 95) {
      console.log('🎉 EXCELLENT - Near-perfect 1:1 recreation achieved!');
    } else if (finalScore >= 85) {
      console.log('✅ GOOD - High accuracy recreation with minor differences');
    } else if (finalScore >= 70) {
      console.log('⚠️  FAIR - Reasonable recreation but improvements needed');
    } else {
      console.log('❌ POOR - Significant differences from original');
    }

    return finalScore;
  }
}

// Run test if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const test = new LawyerExampleRecreationTest();
  test.runRecreationTest().catch(console.error);
}

export { LawyerExampleRecreationTest };

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Input, Select, SelectItem, Card, CardBody, Chip, Switch, Slider } from '@heroui/react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';

/**
 * Business Model Configuration Component - Studio Business Model Setup
 * 
 * Features:
 * - Comprehensive business model configuration interface
 * - Revenue sharing model setup and customization
 * - Commission and fee structure management
 * - Real-time calculation and preview
 * - Integration with studio management APIs
 */
const BusinessModelConfig = ({ isOpen, onClose, studio, currentUser, onUpdate }) => {
  const [loading, setLoading] = useState(false);
  const [businessModel, setBusinessModel] = useState({
    revenue_sharing: {
      method: 'contribution_based',
      base_percentage: 70,
      role_multipliers: {
        founder: 1.5,
        owner: 1.3,
        admin: 1.1,
        member: 1.0,
        contributor: 0.8
      }
    },
    commission_rate: 15,
    recurring_fee: 0,
    billing_cycle: 'monthly',
    payment_terms: {
      net_days: 30,
      late_fee_percentage: 5,
      auto_payment: true
    },
    revenue_distribution: {
      platform_fee: 10,
      alliance_pool: 20,
      member_distribution: 70
    }
  });

  // Initialize with studio data
  useEffect(() => {
    if (studio?.business_model) {
      setBusinessModel(prev => ({
        ...prev,
        ...studio.business_model
      }));
    }
  }, [studio]);

  // Revenue sharing methods
  const revenueMethods = [
    {
      key: 'contribution_based',
      label: 'Contribution Based',
      description: 'Revenue shared based on individual contributions to projects'
    },
    {
      key: 'equal_split',
      label: 'Equal Split',
      description: 'Revenue divided equally among all active members'
    },
    {
      key: 'role_based',
      label: 'Role Based',
      description: 'Revenue shared based on member roles and responsibilities'
    },
    {
      key: 'hybrid',
      label: 'Hybrid Model',
      description: 'Combination of contribution and role-based sharing'
    }
  ];

  // Billing cycle options
  const billingCycles = [
    { key: 'weekly', label: 'Weekly' },
    { key: 'monthly', label: 'Monthly' },
    { key: 'quarterly', label: 'Quarterly' },
    { key: 'annually', label: 'Annually' }
  ];

  // Handle business model field changes
  const handleFieldChange = (field, value) => {
    const fieldPath = field.split('.');
    
    setBusinessModel(prev => {
      const updated = { ...prev };
      let current = updated;
      
      for (let i = 0; i < fieldPath.length - 1; i++) {
        if (!current[fieldPath[i]]) {
          current[fieldPath[i]] = {};
        }
        current = current[fieldPath[i]];
      }
      
      current[fieldPath[fieldPath.length - 1]] = value;
      return updated;
    });
  };

  // Calculate revenue example
  const calculateRevenueExample = () => {
    const exampleRevenue = 10000;
    const platformFee = (exampleRevenue * businessModel.revenue_distribution.platform_fee) / 100;
    const alliancePool = (exampleRevenue * businessModel.revenue_distribution.alliance_pool) / 100;
    const memberDistribution = (exampleRevenue * businessModel.revenue_distribution.member_distribution) / 100;
    
    return {
      total: exampleRevenue,
      platformFee,
      alliancePool,
      memberDistribution,
      perMember: memberDistribution / (studio?.members?.length || 1)
    };
  };

  // Handle save business model
  const handleSaveBusinessModel = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`/.netlify/functions/studios/${studio.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          business_model: businessModel
        })
      });

      if (response.ok) {
        toast.success('Business model updated successfully!');
        onUpdate(); // Refresh data
        onClose();
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update business model');
      }
      
    } catch (error) {
      console.error('Error updating business model:', error);
      toast.error(error.message || 'Failed to update business model');
    } finally {
      setLoading(false);
    }
  };

  const revenueExample = calculateRevenueExample();

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="4xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h2 className="text-2xl font-bold">Business Model Configuration</h2>
          <p className="text-default-600 font-normal">
            Configure revenue sharing, fees, and payment terms for {studio?.name}
          </p>
        </ModalHeader>
        
        <ModalBody className="space-y-6">
          {/* Revenue Sharing Configuration */}
          <Card>
            <CardBody className="p-6">
              <h3 className="text-lg font-semibold mb-4">Revenue Sharing Model</h3>
              
              <div className="space-y-4">
                <Select
                  label="Sharing Method"
                  selectedKeys={[businessModel.revenue_sharing.method]}
                  onSelectionChange={(keys) => 
                    handleFieldChange('revenue_sharing.method', Array.from(keys)[0])
                  }
                  variant="bordered"
                >
                  {revenueMethods.map((method) => (
                    <SelectItem key={method.key} value={method.key}>
                      <div>
                        <div className="font-medium">{method.label}</div>
                        <div className="text-xs text-default-500">{method.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </Select>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Base Percentage: {businessModel.revenue_sharing.base_percentage}%
                  </label>
                  <Slider
                    value={businessModel.revenue_sharing.base_percentage}
                    onChange={(value) => handleFieldChange('revenue_sharing.base_percentage', value)}
                    min={50}
                    max={95}
                    step={5}
                    className="w-full"
                    color="primary"
                  />
                  <div className="flex justify-between text-xs text-default-500 mt-1">
                    <span>50%</span>
                    <span>95%</span>
                  </div>
                </div>

                {businessModel.revenue_sharing.method === 'role_based' && (
                  <div>
                    <h4 className="font-medium mb-3">Role Multipliers</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {Object.entries(businessModel.revenue_sharing.role_multipliers).map(([role, multiplier]) => (
                        <div key={role} className="flex items-center justify-between p-3 border rounded-lg">
                          <span className="capitalize font-medium">{role}</span>
                          <Input
                            type="number"
                            value={multiplier}
                            onChange={(e) => 
                              handleFieldChange(`revenue_sharing.role_multipliers.${role}`, parseFloat(e.target.value))
                            }
                            className="w-20"
                            size="sm"
                            step={0.1}
                            min={0.1}
                            max={2.0}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardBody>
          </Card>

          {/* Commission and Fees */}
          <Card>
            <CardBody className="p-6">
              <h3 className="text-lg font-semibold mb-4">Commission & Fees</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  type="number"
                  label="Commission Rate"
                  value={businessModel.commission_rate}
                  onChange={(e) => handleFieldChange('commission_rate', parseInt(e.target.value))}
                  endContent={<span className="text-default-400">%</span>}
                  variant="bordered"
                />

                <Input
                  type="number"
                  label="Recurring Fee"
                  value={businessModel.recurring_fee}
                  onChange={(e) => handleFieldChange('recurring_fee', parseFloat(e.target.value))}
                  startContent={<span className="text-default-400">$</span>}
                  variant="bordered"
                />

                <Select
                  label="Billing Cycle"
                  selectedKeys={[businessModel.billing_cycle]}
                  onSelectionChange={(keys) => 
                    handleFieldChange('billing_cycle', Array.from(keys)[0])
                  }
                  variant="bordered"
                >
                  {billingCycles.map((cycle) => (
                    <SelectItem key={cycle.key} value={cycle.key}>
                      {cycle.label}
                    </SelectItem>
                  ))}
                </Select>

                <Input
                  type="number"
                  label="Payment Terms (Net Days)"
                  value={businessModel.payment_terms.net_days}
                  onChange={(e) => handleFieldChange('payment_terms.net_days', parseInt(e.target.value))}
                  variant="bordered"
                />
              </div>

              <div className="mt-4 space-y-3">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">Auto Payment</div>
                    <div className="text-sm text-default-600">Automatically process payments when due</div>
                  </div>
                  <Switch
                    isSelected={businessModel.payment_terms.auto_payment}
                    onValueChange={(value) => handleFieldChange('payment_terms.auto_payment', value)}
                  />
                </div>

                <Input
                  type="number"
                  label="Late Fee Percentage"
                  value={businessModel.payment_terms.late_fee_percentage}
                  onChange={(e) => handleFieldChange('payment_terms.late_fee_percentage', parseFloat(e.target.value))}
                  endContent={<span className="text-default-400">%</span>}
                  variant="bordered"
                />
              </div>
            </CardBody>
          </Card>

          {/* Revenue Distribution */}
          <Card>
            <CardBody className="p-6">
              <h3 className="text-lg font-semibold mb-4">Revenue Distribution</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Platform Fee: {businessModel.revenue_distribution.platform_fee}%
                  </label>
                  <Slider
                    value={businessModel.revenue_distribution.platform_fee}
                    onChange={(value) => handleFieldChange('revenue_distribution.platform_fee', value)}
                    min={5}
                    max={20}
                    step={1}
                    className="w-full"
                    color="warning"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Studio Pool: {businessModel.revenue_distribution.alliance_pool}%
                  </label>
                  <Slider
                    value={businessModel.revenue_distribution.alliance_pool}
                    onChange={(value) => {
                      handleFieldChange('revenue_distribution.alliance_pool', value);
                      handleFieldChange('revenue_distribution.member_distribution', 
                        100 - businessModel.revenue_distribution.platform_fee - value);
                    }}
                    min={10}
                    max={50}
                    step={5}
                    className="w-full"
                    color="secondary"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Member Distribution: {businessModel.revenue_distribution.member_distribution}%
                  </label>
                  <div className="text-sm text-default-600">
                    Automatically calculated based on platform fee and studio pool
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Revenue Example */}
          <Card className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
            <CardBody className="p-6">
              <h3 className="text-lg font-semibold mb-4">Revenue Distribution Example</h3>
              <p className="text-sm text-default-600 mb-4">
                Based on $10,000 project revenue with current settings:
              </p>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    ${revenueExample.platformFee.toLocaleString()}
                  </div>
                  <div className="text-sm text-default-600">Platform Fee</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    ${revenueExample.alliancePool.toLocaleString()}
                  </div>
                  <div className="text-sm text-default-600">Studio Pool</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    ${revenueExample.memberDistribution.toLocaleString()}
                  </div>
                  <div className="text-sm text-default-600">Member Distribution</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    ${revenueExample.perMember.toLocaleString()}
                  </div>
                  <div className="text-sm text-default-600">Per Member</div>
                </div>
              </div>
            </CardBody>
          </Card>
        </ModalBody>
        
        <ModalFooter>
          <Button color="danger" variant="flat" onPress={onClose}>
            Cancel
          </Button>
          <Button 
            color="primary" 
            onPress={handleSaveBusinessModel}
            isLoading={loading}
          >
            Save Configuration
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default BusinessModelConfig;

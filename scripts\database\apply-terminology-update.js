#!/usr/bin/env node

/**
 * Apply Terminology Update Migration
 * Updates database from Alliance/Venture/Quest to Studio/Project/Mission terminology
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  console.error('Required environment variables:');
  console.error('- VITE_SUPABASE_URL or SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyTerminologyUpdate() {
  console.log('🔄 Starting Terminology Update Migration...');
  console.log('📝 Alliance → Studio, Venture → Project, Quest → Mission\n');

  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, '../../supabase/migrations/20250625000001_terminology_update_studios_projects_missions.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('1️⃣ Applying terminology update migration...');
    
    // Execute the migration
    const { error: migrationError } = await supabase.rpc('exec', { 
      sql: migrationSQL 
    });

    if (migrationError) {
      console.error('❌ Migration failed:', migrationError.message);
      throw migrationError;
    }

    console.log('✅ Migration applied successfully');

    // Verify the changes
    console.log('\n2️⃣ Verifying terminology updates...');
    
    // Check studio system
    const { data: studioData, error: studioError } = await supabase
      .from('teams')
      .select('id, name, studio_type')
      .limit(5);

    if (studioError) {
      console.log('⚠️ Studio verification warning:', studioError.message);
    } else {
      console.log(`✅ Studio system: ${studioData.length} studios found`);
      if (studioData.length > 0) {
        console.log('   Sample studio types:', studioData.map(s => s.studio_type).filter(Boolean));
      }
    }

    // Check project system
    const { data: projectData, error: projectError } = await supabase
      .from('projects')
      .select('id, name, project_type, studio_id')
      .limit(5);

    if (projectError) {
      console.log('⚠️ Project verification warning:', projectError.message);
    } else {
      console.log(`✅ Project system: ${projectData.length} projects found`);
      if (projectData.length > 0) {
        console.log('   Sample project types:', projectData.map(p => p.project_type).filter(Boolean));
      }
    }

    // Check mission system
    const { data: missionData, error: missionError } = await supabase
      .from('tasks')
      .select('id, title, task_category, mission_type')
      .eq('task_category', 'mission')
      .limit(5);

    if (missionError) {
      console.log('⚠️ Mission verification warning:', missionError.message);
    } else {
      console.log(`✅ Mission system: ${missionData.length} missions found`);
      if (missionData.length > 0) {
        console.log('   Sample mission types:', missionData.map(m => m.mission_type).filter(Boolean));
      }
    }

    // Check people type system
    const { data: memberData, error: memberError } = await supabase
      .from('team_members')
      .select('collaboration_type')
      .limit(10);

    if (memberError) {
      console.log('⚠️ People type verification warning:', memberError.message);
    } else {
      const collaborationTypes = memberData.map(m => m.collaboration_type).filter(Boolean);
      const typeCounts = collaborationTypes.reduce((acc, type) => {
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {});
      console.log('✅ People type system updated');
      console.log('   Collaboration types:', typeCounts);
    }

    // Check renamed tables
    console.log('\n3️⃣ Verifying table renames...');
    
    const tablesToCheck = [
      'studio_invitations',
      'studio_preferences', 
      'user_missions'
    ];

    for (const tableName of tablesToCheck) {
      const { data, error } = await supabase
        .from(tableName)
        .select('id')
        .limit(1);

      if (error) {
        console.log(`⚠️ Table ${tableName}: ${error.message}`);
      } else {
        console.log(`✅ Table ${tableName}: accessible`);
      }
    }

    console.log('\n🎉 Terminology Update Migration Complete!');
    console.log('📋 Summary of changes:');
    console.log('   • Alliance → Studio (teams.studio_type)');
    console.log('   • Venture → Project (projects.project_type)');
    console.log('   • Quest → Mission (tasks.mission_type)');
    console.log('   • Added people type system (studio_member, contractor, specialist)');
    console.log('   • Renamed tables: alliance_* → studio_*, user_quests → user_missions');
    console.log('   • Updated indexes and constraints');

  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Test database connection first
async function testConnection() {
  console.log('🔌 Testing database connection...');
  
  try {
    const { data, error } = await supabase
      .from('teams')
      .select('id')
      .limit(1);

    if (error) {
      throw error;
    }

    console.log('✅ Database connection successful\n');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

// Main execution
async function main() {
  console.log('🏗️ Royaltea Terminology Update Migration');
  console.log('=====================================\n');

  const connected = await testConnection();
  if (!connected) {
    process.exit(1);
  }

  await applyTerminologyUpdate();
}

// Handle command line execution
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { applyTerminologyUpdate };

import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate, useParams } from 'react-router-dom';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { useAuth } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { Button, Card, CardBody } from '@heroui/react';

// Import question flow components
import VentureWelcomeScreen from './VentureWelcomeScreen';
import VentureQuestionFlow from './VentureQuestionFlow';
import VentureReviewScreen from './VentureReviewScreen';

// Import project mapping and agreement generation utilities
import { mapVentureAnswersToProjectData } from '../../utils/project-mapping';
import { generateLegalAgreement } from '../../utils/legal-agreement-generator';

// Import existing wizard components for Phase 2
import TeamContributors from '../project/wizard/TeamContributors';
import RoyaltyModel from '../project/wizard/RoyaltyModel';
import RevenueTranches from '../project/wizard/RevenueTranchesEnhanced';
import ContributionTracking from '../project/wizard/ContributionTracking';
import Milestones from '../project/wizard/Milestones';
import ReviewAgreement from '../project/wizard/ReviewAgreement';

/**
 * VentureSetupWizard Component
 * 
 * Enhanced project creation wizard that combines:
 * - Phase 1: Immersive question flow (8 simple questions)
 * - Phase 2: Existing detailed wizard steps
 * 
 * Follows immersive pattern for questions, then transitions to bento grid
 * for detailed configuration while maintaining all existing functionality.
 */
const VentureSetupWizard = ({
  mode = 'create', // 'create' or 'edit'
  allianceId = null,
  onComplete,
  onCancel
}) => {
  const { currentUser } = useContext(UserContext);
  // supabase is imported directly
  const navigate = useNavigate();
  const { id } = useParams();

  // State for studio requirement checking
  const [userAlliances, setUserAlliances] = useState([]);
  const [selectedAllianceId, setSelectedAllianceId] = useState(allianceId);
  const [showAllianceSelection, setShowAllianceSelection] = useState(false);
  const [checkingAlliances, setCheckingAlliances] = useState(true);
  
  // Wizard state management
  const [currentPhase, setCurrentPhase] = useState('studio-check'); // 'studio-check', 'studio-selection', 'welcome', 'questions', 'detailed', 'review'
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [completedSteps, setCompletedSteps] = useState([]);
  
  // Question flow data (Phase 1)
  const [questionData, setQuestionData] = useState({
    projectCategory: '',
    projectSubtype: '',
    targetAudience: '',
    timeline: '',
    budget: '',
    successMetrics: '',
    ventureName: '',
    ventureDescription: '',
    ventureIcon: '🚀',
    ventureTags: [],
    initialTeamRoles: {}
  });
  
  // Detailed wizard data (Phase 2) - matches existing ProjectWizard structure
  const [projectData, setProjectData] = useState({
    // Basic project info (populated from questions)
    name: '',
    description: '',
    project_type: '',
    start_date: new Date(),
    launch_date: null,
    estimated_duration: 6,
    thumbnail_url: '',
    is_public: true,
    
    // Studio connection
    team_id: selectedAllianceId,
    
    // Company Information
    company_name: '',
    company_address: '',
    company_state: '',
    company_county: '',
    
    // Project-Specific Fields
    engine: '',
    platforms: '',
    genre: '',
    technology_stack: '',
    distribution_platforms: '',
    
    // Team & Contributors
    contributors: [],
    
    // Royalty Model
    royalty_model: {
      model_type: 'custom',
      model_schema: 'cog',
      configuration: {
        tasks_weight: 33.33,
        hours_weight: 33.33,
        difficulty_weight: 33.34
      },
      is_pre_expense: true
    },
    
    // Revenue Tranches
    revenue_tranches: [],
    
    // Contribution Tracking
    contribution_tracking: {
      categories: ['Design', 'Development', 'Content', 'Management'],
      difficulty_scale: [1, 2, 3, 5, 8],
      task_types: []
    },
    
    // Milestones
    milestones: []
  });

  // Generated legal agreement
  const [generatedAgreement, setGeneratedAgreement] = useState(null);

  // Check user studios on component mount
  useEffect(() => {
    if (mode === 'create') {
      checkUserAlliances();
    } else if (mode === 'edit' && id) {
      loadExistingProject();
    }
  }, [mode, id, currentUser]);

  // Check if user has studios
  const checkUserAlliances = async () => {
    if (!currentUser) {
      setCheckingAlliances(false);
      return;
    }

    try {
      // Get user's studios
      const { data: studios, error } = await supabase
        .from('team_members')
        .select(`
          team_id,
          role,
          status,
          teams:team_id (
            id,
            name,
            description,
            studio_type,
            status
          )
        `)
        .eq('user_id', currentUser.id)
        .eq('status', 'active')
        .in('role', ['founder', 'owner', 'admin']); // Only studios where user can create projects

      if (error) throw error;

      const activeAlliances = studios?.filter(a => a.teams?.status === 'active') || [];
      setUserAlliances(activeAlliances);

      if (allianceId) {
        // Studio was provided, verify user has access
        const hasAccess = activeAlliances.some(a => a.teams.id === allianceId);
        if (hasAccess) {
          setSelectedAllianceId(allianceId);
          setCurrentPhase('welcome');
        } else {
          toast.error('You do not have permission to create projects in this studio');
          setCurrentPhase('studio-selection');
        }
      } else if (activeAlliances.length === 0) {
        // No studios, need to create one
        setCurrentPhase('studio-selection');
      } else if (activeAlliances.length === 1) {
        // Only one studio, use it automatically
        setSelectedAllianceId(activeAlliances[0].teams.id);
        setCurrentPhase('welcome');
      } else {
        // Multiple studios, let user choose
        setCurrentPhase('studio-selection');
      }
    } catch (error) {
      console.error('Error checking user studios:', error);
      toast.error('Failed to check studio access');
      setCurrentPhase('studio-selection');
    } finally {
      setCheckingAlliances(false);
    }
  };

  const loadExistingProject = async () => {
    setIsLoading(true);
    try {
      const { data: project, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      if (project) {
        setProjectData(project);
        // Skip question flow for existing projects
        setCurrentPhase('detailed');
      }
    } catch (error) {
      console.error('Error loading project:', error);
      toast.error('Failed to load project data');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle question flow completion with enhanced smart configuration
  const handleQuestionsComplete = async (answers) => {
    setQuestionData(answers);

    // Use the new project mapping utility for comprehensive project data
    const mappedProjectData = mapVentureAnswersToProjectData(answers);
    setProjectData(prev => ({ ...prev, ...mappedProjectData }));

    // Generate the legal agreement
    try {
      const ventureOwnerInfo = {
        companyName: answers.ventureName || 'Your Project',
        ownerName: currentUser?.name || '[Your Name]',
        ownerTitle: 'Founder',
        address: '[Your Business Address]',
        address2: '[City, State ZIP]',
        address3: '[Country]',
        state: 'Florida',
        entityType: 'LLC' // Default suggestion
      };

      const contributorInfo = {
        name: '[Contributor Name]',
        isCompany: false,
        address: '[Contributor Address]',
        address2: '[City, State ZIP]',
        address3: '[Country]'
      };

      const agreement = generateLegalAgreement(mappedProjectData, answers, ventureOwnerInfo, contributorInfo);
      setGeneratedAgreement(agreement);
    } catch (error) {
      console.error('Error generating agreement:', error);
      toast.error('Failed to generate agreement. Please try again.');
    }

    // Skip directly to review phase for better cohesion
    // Users can still access detailed configuration from review
    setCurrentPhase('review');
    setCompletedSteps([1, 2, 3, 4, 5, 6]); // Mark all steps as auto-configured
  };

  // Note: Using mapVentureAnswersToProjectData from project-mapping utility
  // This provides comprehensive smart defaults and legal agreement compatibility

  // Legacy helper functions removed - now using project-mapping utility

  // Legacy smart generation functions removed - now using project-mapping utility







  // Handle detailed wizard step navigation
  const handleDetailedNext = () => {
    if (currentStep < 7) {
      setCurrentStep(currentStep + 1);
      if (!completedSteps.includes(currentStep)) {
        setCompletedSteps([...completedSteps, currentStep]);
      }
    }
  };

  const handleDetailedPrevious = () => {
    if (currentStep > 2) {
      setCurrentStep(currentStep - 1);
    } else {
      // Go back to questions
      setCurrentPhase('questions');
    }
  };

  // Handle wizard completion
  const handleWizardComplete = async () => {
    setIsLoading(true);
    try {
      // Save the complete project
      const { data: project, error } = await supabase
        .from('projects')
        .insert([projectData])
        .select()
        .single();

      if (error) throw error;

      toast.success('Project created successfully!');
      
      if (onComplete) {
        onComplete({
          type: 'venture_created',
          project: project,
          action: 'create_mission',
          redirectTo: `/project/${project.id}`
        });
      } else {
        navigate(`/project/${project.id}`);
      }
    } catch (error) {
      console.error('Error creating project:', error);
      toast.error('Failed to create project. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Render studio selection screen
  const renderAllianceSelection = () => {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex items-center justify-center p-4">
        <div className="max-w-2xl mx-auto text-center">
          <div className="mb-8">
            <h1 className="text-6xl font-bold text-white mb-4">
              🏰 Choose Your Studio
            </h1>
            <p className="text-xl text-white/80 leading-relaxed">
              Projects must be created within an studio. This ensures proper legal structure
              and clear ownership for your collaboration agreements.
            </p>
          </div>

          {userAlliances.length > 0 ? (
            <div className="space-y-4 mb-8">
              <h2 className="text-2xl font-semibold text-white mb-4">Select an Studio</h2>
              {userAlliances.map((studio) => (
                <Card
                  key={studio.teams.id}
                  className="bg-white/10 backdrop-blur-md border border-white/20 cursor-pointer hover:bg-white/20 transition-colors"
                  isPressable
                  onPress={() => {
                    setSelectedAllianceId(studio.teams.id);
                    setCurrentPhase('welcome');
                  }}
                >
                  <CardBody className="p-6">
                    <div className="flex items-center justify-between text-white">
                      <div className="text-left">
                        <h3 className="text-lg font-semibold">{studio.teams.name}</h3>
                        <p className="text-white/70">{studio.teams.description}</p>
                        <p className="text-sm text-white/60 mt-1">
                          Role: {studio.role} • Type: {studio.teams.studio_type}
                        </p>
                      </div>
                      <div className="text-2xl">→</div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          ) : (
            <div className="bg-white/10 backdrop-blur-md rounded-lg p-8 border border-white/20 mb-8">
              <div className="text-4xl mb-4">🏗️</div>
              <h2 className="text-2xl font-semibold text-white mb-4">No Studio Found</h2>
              <p className="text-white/80 mb-6">
                You need to create or join an studio before creating projects.
                Studios provide the legal entity structure for your collaboration agreements.
              </p>
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-white text-primary font-semibold px-8 py-4 hover:bg-white/90"
              onPress={() => navigate('/studios/create')}
            >
              Create New Studio
            </Button>
            <Button
              size="lg"
              variant="bordered"
              className="border-white text-white font-semibold px-8 py-4 hover:bg-white/10"
              onPress={() => navigate('/studios')}
            >
              Browse Studios
            </Button>
            {onCancel && (
              <Button
                size="lg"
                variant="light"
                className="text-white font-semibold px-8 py-4 hover:bg-white/10"
                onPress={onCancel}
              >
                Cancel
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render current phase
  const renderCurrentPhase = () => {
    switch (currentPhase) {
      case 'studio-check':
        return (
          <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-lg text-white">Checking studio access...</p>
            </div>
          </div>
        );

      case 'studio-selection':
        return renderAllianceSelection();

      case 'welcome':
        return (
          <VentureWelcomeScreen
            allianceId={selectedAllianceId}
            onStart={() => setCurrentPhase('questions')}
            onCancel={onCancel}
          />
        );

      case 'questions':
        return (
          <VentureQuestionFlow
            initialData={questionData}
            onComplete={handleQuestionsComplete}
            onBack={() => setCurrentPhase('welcome')}
            onCancel={onCancel}
          />
        );

      case 'detailed':
        return renderDetailedWizard();

      case 'review':
        return (
          <VentureReviewScreen
            questionData={questionData}
            projectData={projectData}
            generatedAgreement={generatedAgreement}
            onConfirm={handleWizardComplete}
            onBack={() => setCurrentPhase('questions')}
            onEditDetails={() => setCurrentPhase('detailed')}
            isLoading={isLoading}
          />
        );

      default:
        return null;
    }
  };

  // Render detailed wizard steps (existing functionality)
  const renderDetailedWizard = () => {
    const renderStep = () => {
      switch (currentStep) {
        case 2:
          return (
            <TeamContributors
              projectData={projectData}
              setProjectData={setProjectData}
              projectId={id}
            />
          );
        case 3:
          return (
            <RoyaltyModel
              projectData={projectData}
              setProjectData={setProjectData}
            />
          );
        case 4:
          return (
            <RevenueTranches
              projectData={projectData}
              setProjectData={setProjectData}
            />
          );
        case 5:
          return (
            <ContributionTracking
              projectData={projectData}
              setProjectData={setProjectData}
            />
          );
        case 6:
          return (
            <Milestones
              projectData={projectData}
              setProjectData={setProjectData}
            />
          );
        case 7:
          return (
            <ReviewAgreement
              projectData={projectData}
              setProjectData={setProjectData}
              projectId={id}
            />
          );
        default:
          return null;
      }
    };

    return (
      <div className="container mx-auto mt-4 mb-5 px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-center mb-4 text-3xl font-bold text-foreground">
            Configure Your Project
          </h1>
          
          {/* Progress indicator */}
          <div className="mb-6">
            <div className="flex justify-center space-x-2">
              {[2, 3, 4, 5, 6, 7].map((step) => (
                <div
                  key={step}
                  className={`w-3 h-3 rounded-full ${
                    step === currentStep
                      ? 'bg-primary'
                      : completedSteps.includes(step)
                      ? 'bg-success'
                      : 'bg-default-300'
                  }`}
                />
              ))}
            </div>
            <p className="text-center text-sm text-default-500 mt-2">
              Step {currentStep - 1} of 6
            </p>
          </div>

          {/* Step content */}
          <div className="bg-content1 rounded-lg p-6">
            {renderStep()}
            
            {/* Navigation */}
            <div className="flex justify-between mt-6">
              <Button
                variant="flat"
                onPress={handleDetailedPrevious}
                disabled={isLoading}
              >
                Previous
              </Button>

              <div className="space-x-2">
                {currentStep < 7 ? (
                  <Button
                    onPress={handleDetailedNext}
                    disabled={isLoading}
                  >
                    Next
                  </Button>
                ) : (
                  <Button
                    onPress={() => setCurrentPhase('review')}
                    disabled={isLoading}
                    className="bg-success text-white"
                  >
                    Review & Create
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50">
      <AnimatePresence mode="wait">
        {renderCurrentPhase()}
      </AnimatePresence>
    </div>
  );
};

export default VentureSetupWizard;

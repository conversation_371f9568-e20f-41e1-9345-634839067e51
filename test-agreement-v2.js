#!/usr/bin/env node

/**
 * Quick Test Script for Agreement Generator V2
 * 
 * This script provides a quick way to test the new agreement generation system
 * and compare it against the old system.
 */

import { AgreementGeneratorV2 } from './client/src/utils/agreement/v2/AgreementGeneratorV2.js';
import fs from 'fs';
import path from 'path';

async function testAgreementGeneratorV2() {
  console.log('🧪 Testing Agreement Generator V2');
  console.log('=================================');

  const generator = new AgreementGeneratorV2();

  // Test data
  const testData = {
    company: {
      name: 'TechCorp Solutions Inc.',
      address: '123 Innovation Drive, Orlando, FL 32801',
      state: 'Florida',
      city: 'Orlando',
      signerName: '<PERSON>',
      signerTitle: 'CEO',
      billingEmail: '<EMAIL>'
    },
    project: {
      name: 'AI Analytics Platform',
      description: 'Advanced AI-powered analytics platform for enterprise data insights',
      projectType: 'software'
    },
    contributor: {
      name: '<PERSON>',
      email: '<EMAIL>',
      address: '456 Developer Lane, Orlando, FL 32802'
    }
  };

  try {
    console.log('\n📋 Test Data:');
    console.log(`Company: ${testData.company.name}`);
    console.log(`Project: ${testData.project.name}`);
    console.log(`Contributor: ${testData.contributor.name}`);

    console.log('\n⚙️  Generating Agreement...');
    const startTime = Date.now();
    
    const result = await generator.generateAgreement('standard', testData);
    
    const endTime = Date.now();
    const duration = endTime - startTime;

    if (result.success) {
      console.log('✅ Agreement Generated Successfully!');
      console.log(`⏱️  Generation Time: ${duration}ms`);
      console.log(`📊 Accuracy Score: ${result.metadata.accuracyScore}%`);
      console.log(`📄 Agreement Length: ${result.agreement.length} characters`);

      // Save the generated agreement
      const outputPath = path.join(process.cwd(), 'generated-agreement-v2.md');
      fs.writeFileSync(outputPath, result.agreement);
      console.log(`💾 Agreement saved to: ${outputPath}`);

      // Validation summary
      const validation = result.metadata.validationResults;
      console.log('\n🔍 Validation Summary:');
      console.log(`Critical Issues: ${validation.criticalIssues.length}`);
      console.log(`Warnings: ${validation.warnings.length}`);
      console.log(`Completeness Score: ${validation.completenessScore}%`);

      // Check for specific issues
      console.log('\n🔎 Quality Checks:');
      
      // Check for unreplaced placeholders
      const placeholders = validation.placeholders;
      if (placeholders.length === 0) {
        console.log('✅ No unreplaced placeholders');
      } else {
        console.log(`❌ Found ${placeholders.length} unreplaced placeholders:`);
        placeholders.forEach(p => console.log(`   - ${p}`));
      }

      // Check for required sections
      const missingSections = Object.entries(validation.sections)
        .filter(([section, present]) => !present)
        .map(([section]) => section);
      
      if (missingSections.length === 0) {
        console.log('✅ All required sections present');
      } else {
        console.log(`❌ Missing ${missingSections.length} required sections:`);
        missingSections.forEach(s => console.log(`   - ${s}`));
      }

      // Check for hardcoded content
      const hardcodedIssues = validation.criticalIssues.filter(i => i.type === 'HARDCODED_CONTENT');
      if (hardcodedIssues.length === 0) {
        console.log('✅ No hardcoded content found');
      } else {
        console.log(`❌ Found ${hardcodedIssues.length} hardcoded content issues:`);
        hardcodedIssues.forEach(i => console.log(`   - ${i.message}`));
      }

      // Check for data integration
      const dataIssues = validation.criticalIssues.filter(i => i.type === 'MISSING_DATA_INTEGRATION');
      if (dataIssues.length === 0) {
        console.log('✅ All user data properly integrated');
      } else {
        console.log(`❌ Found ${dataIssues.length} data integration issues:`);
        dataIssues.forEach(i => console.log(`   - ${i.message}`));
      }

      // Overall assessment
      console.log('\n🎯 OVERALL ASSESSMENT:');
      if (result.metadata.accuracyScore >= 95 && validation.criticalIssues.length === 0) {
        console.log('✅ SYSTEM READY FOR PRODUCTION');
        console.log('   - Accuracy score meets 95% threshold');
        console.log('   - No critical issues found');
        console.log('   - All quality checks passed');
      } else {
        console.log('❌ SYSTEM NOT READY FOR PRODUCTION');
        if (result.metadata.accuracyScore < 95) {
          console.log(`   - Accuracy score ${result.metadata.accuracyScore}% below 95% threshold`);
        }
        if (validation.criticalIssues.length > 0) {
          console.log(`   - ${validation.criticalIssues.length} critical issues found`);
        }
      }

    } else {
      console.log('❌ Agreement Generation Failed!');
      console.log(`Error Type: ${result.errorType}`);
      console.log(`Error Message: ${result.error}`);
      
      if (result.details && result.details.length > 0) {
        console.log('\nError Details:');
        result.details.forEach((detail, index) => {
          console.log(`  ${index + 1}. ${detail.message || detail}`);
        });
      }
    }

  } catch (error) {
    console.error('💥 Test Failed:', error.message);
    console.error(error.stack);
  }
}

// Additional test functions
async function testMultipleScenarios() {
  console.log('\n🎮 Testing Multiple Project Types');
  console.log('=================================');

  const generator = new AgreementGeneratorV2();
  const projectTypes = ['software', 'game', 'music', 'film', 'art'];

  for (const projectType of projectTypes) {
    console.log(`\n📋 Testing ${projectType.toUpperCase()} project...`);
    
    const testData = {
      company: {
        name: `${projectType.charAt(0).toUpperCase() + projectType.slice(1)} Company Inc.`,
        address: '123 Test Street, Test City, TX 75001',
        state: 'Texas',
        city: 'Test City',
        signerName: 'Test Signer',
        signerTitle: 'CEO',
        billingEmail: `test@${projectType}company.com`
      },
      project: {
        name: `Test ${projectType.charAt(0).toUpperCase() + projectType.slice(1)} Project`,
        description: `A test ${projectType} project for validation`,
        projectType: projectType
      },
      contributor: {
        name: 'Test Contributor',
        email: `contributor@${projectType}company.com`,
        address: '456 Contributor Ave, Test City, TX 75002'
      }
    };

    try {
      const result = await generator.generateAgreement('standard', testData);
      
      if (result.success) {
        console.log(`  ✅ ${projectType.toUpperCase()}: ${result.metadata.accuracyScore}% accuracy`);
        
        // Check for project-type specific content
        const expectedContent = {
          software: 'Software Development Services',
          game: 'Game Development Services',
          music: 'Music Production Services',
          film: 'Film Production Services',
          art: 'Artistic Services'
        };
        
        if (result.agreement.includes(expectedContent[projectType])) {
          console.log(`  ✅ Project-specific content found`);
        } else {
          console.log(`  ⚠️  Project-specific content missing`);
        }
        
      } else {
        console.log(`  ❌ ${projectType.toUpperCase()}: ${result.error}`);
      }
    } catch (error) {
      console.log(`  💥 ${projectType.toUpperCase()}: ${error.message}`);
    }
  }
}

async function testSystemInfo() {
  console.log('\n🔧 System Information');
  console.log('====================');

  const generator = new AgreementGeneratorV2();
  const info = generator.getSystemInfo();

  console.log(`Version: ${info.version}`);
  console.log(`Components:`);
  Object.entries(info.components).forEach(([key, value]) => {
    console.log(`  - ${key}: ${value}`);
  });
  console.log(`Configuration:`);
  Object.entries(info.config).forEach(([key, value]) => {
    console.log(`  - ${key}: ${value}`);
  });
}

// Run all tests
async function runAllTests() {
  await testAgreementGeneratorV2();
  await testMultipleScenarios();
  await testSystemInfo();
  
  console.log('\n🏁 All Tests Completed');
  console.log('======================');
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(console.error);
}

export { testAgreementGeneratorV2, testMultipleScenarios, testSystemInfo };

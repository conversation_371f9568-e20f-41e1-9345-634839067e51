import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>er, <PERSON>dalBody, <PERSON>dal<PERSON>ooter, Button, Card, CardBody, Chip, Progress, Avatar, Tabs, Tab } from '@heroui/react';
import { motion } from 'framer-motion';

/**
 * Progress Tracker Component - Mission Progression and Milestone Tracking
 * 
 * Features:
 * - Comprehensive mission progression tracking
 * - Achievement gallery and milestone visualization
 * - Experience and level progression analytics
 * - Skill development tracking and recommendations
 * - Integration with mission progression APIs
 */
const ProgressTracker = ({ isOpen, onClose, userProgress, activeQuests, currentUser }) => {
  const [activeTab, setActiveTab] = useState('overview');

  // Calculate next level progress
  const calculateLevelProgress = () => {
    const { experience, nextLevelXP } = userProgress;
    return Math.round((experience / nextLevelXP) * 100);
  };

  // Get achievement color
  const getAchievementColor = (achievement) => {
    const colors = ['warning', 'success', 'primary', 'secondary', 'danger'];
    return colors[achievement.length % colors.length];
  };

  // Format date
  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Mock skill progress data
  const skillProgress = [
    { skill: 'React', level: 4, progress: 75, experience: 1200 },
    { skill: 'TypeScript', level: 3, progress: 45, experience: 800 },
    { skill: 'UI/UX Design', level: 2, progress: 90, experience: 450 },
    { skill: 'Project Management', level: 3, progress: 60, experience: 900 },
    { skill: 'Leadership', level: 2, progress: 30, experience: 300 }
  ];

  // Mock recent activities
  const recentActivities = [
    {
      id: 1,
      type: 'quest_progress',
      title: 'Payment System Architect',
      description: 'Completed Chapter 4: Escrow Implementation',
      timestamp: new Date('2025-01-16T10:30:00'),
      experience: 150,
      icon: '⚔️'
    },
    {
      id: 2,
      type: 'achievement',
      title: 'Achievement Unlocked',
      description: 'Payment Master - Master of financial systems',
      timestamp: new Date('2025-01-15T16:45:00'),
      experience: 200,
      icon: '🏆'
    },
    {
      id: 3,
      type: 'level_up',
      title: 'Level Up!',
      description: 'Reached Level 3 - Intermediate Adventurer',
      timestamp: new Date('2025-01-14T14:20:00'),
      experience: 500,
      icon: '⭐'
    },
    {
      id: 4,
      type: 'quest_complete',
      title: 'Component Library Creator',
      description: 'Successfully completed mission and earned rewards',
      timestamp: new Date('2025-01-08T11:15:00'),
      experience: 400,
      icon: '✅'
    }
  ];

  // Render overview tab
  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Level Progress */}
      <Card>
        <CardBody className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-xl font-semibold">Level {userProgress.level}</h3>
              <p className="text-default-600">Intermediate Adventurer</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-primary">
                {userProgress.experience} XP
              </div>
              <div className="text-sm text-default-500">
                {userProgress.nextLevelXP - userProgress.experience} to next level
              </div>
            </div>
          </div>
          
          <Progress 
            value={calculateLevelProgress()} 
            color="primary" 
            size="lg"
            className="mb-2"
          />
          
          <div className="flex justify-between text-sm text-default-500">
            <span>Level {userProgress.level}</span>
            <span>Level {userProgress.level + 1}</span>
          </div>
        </CardBody>
      </Card>

      {/* Mission Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-800/20">
          <CardBody className="p-6 text-center">
            <div className="text-3xl mb-2">⚔️</div>
            <div className="text-2xl font-bold text-blue-600">
              {activeQuests.length}
            </div>
            <div className="text-sm text-default-600">Active Missions</div>
          </CardBody>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-800/20">
          <CardBody className="p-6 text-center">
            <div className="text-3xl mb-2">✅</div>
            <div className="text-2xl font-bold text-green-600">
              {userProgress.questsCompleted}
            </div>
            <div className="text-sm text-default-600">Completed Missions</div>
          </CardBody>
        </Card>

        <Card className="bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-800/20">
          <CardBody className="p-6 text-center">
            <div className="text-3xl mb-2">🏆</div>
            <div className="text-2xl font-bold text-yellow-600">
              {userProgress.achievements.length}
            </div>
            <div className="text-sm text-default-600">Achievements</div>
          </CardBody>
        </Card>
      </div>

      {/* Active Missions Progress */}
      <Card>
        <CardBody className="p-6">
          <h3 className="text-lg font-semibold mb-4">Active Mission Progress</h3>
          <div className="space-y-4">
            {activeQuests.map((mission) => (
              <div key={mission.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{mission.title}</h4>
                  <Chip color="primary" size="sm" variant="flat">
                    {mission.progress}%
                  </Chip>
                </div>
                
                <Progress 
                  value={mission.progress} 
                  color="success" 
                  size="sm"
                  className="mb-2"
                />
                
                <div className="flex justify-between text-sm text-default-500">
                  <span>Started {formatDate(mission.startedAt)}</span>
                  {mission.story && (
                    <span>Chapter {mission.story.currentChapter}/{mission.story.totalChapters}</span>
                  )}
                </div>
              </div>
            ))}
            
            {activeQuests.length === 0 && (
              <div className="text-center py-8 text-default-500">
                <div className="text-4xl mb-4">🎯</div>
                <p>No active missions. Start a new mission to begin your journey!</p>
              </div>
            )}
          </div>
        </CardBody>
      </Card>
    </div>
  );

  // Render achievements tab
  const renderAchievementsTab = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-semibold mb-2">Achievement Gallery</h3>
        <p className="text-default-600">
          {userProgress.achievements.length} achievements unlocked
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {userProgress.achievements.map((achievement, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card className="hover:shadow-lg transition-shadow">
              <CardBody className="p-6 text-center">
                <div className="text-4xl mb-3">🏆</div>
                <h4 className="font-semibold mb-2">{achievement}</h4>
                <Chip
                  color={getAchievementColor(achievement)}
                  variant="flat"
                  size="sm"
                >
                  Unlocked
                </Chip>
                <div className="text-xs text-default-500 mt-2">
                  Earned through mission completion
                </div>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>

      {userProgress.achievements.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🏆</div>
          <h3 className="text-xl font-semibold mb-2">No Achievements Yet</h3>
          <p className="text-default-600">
            Complete missions and reach milestones to unlock achievements
          </p>
        </div>
      )}
    </div>
  );

  // Render skills tab
  const renderSkillsTab = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-semibold mb-2">Skill Development</h3>
        <p className="text-default-600">
          Track your skill progression and development
        </p>
      </div>

      <div className="space-y-4">
        {skillProgress.map((skill, index) => (
          <motion.div
            key={skill.skill}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card>
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-semibold">{skill.skill}</h4>
                    <div className="text-sm text-default-600">
                      Level {skill.level} • {skill.experience} XP
                    </div>
                  </div>
                  <Chip
                    color="primary"
                    variant="flat"
                    size="sm"
                  >
                    {skill.progress}%
                  </Chip>
                </div>
                
                <Progress 
                  value={skill.progress} 
                  color="primary" 
                  size="sm"
                />
                
                <div className="flex justify-between text-xs text-default-500 mt-2">
                  <span>Level {skill.level}</span>
                  <span>Level {skill.level + 1}</span>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );

  // Render activity tab
  const renderActivityTab = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-semibold mb-2">Recent Activity</h3>
        <p className="text-default-600">
          Your mission journey and achievements
        </p>
      </div>

      <div className="space-y-4">
        {recentActivities.map((activity, index) => (
          <motion.div
            key={activity.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card className="hover:shadow-md transition-shadow">
              <CardBody className="p-4">
                <div className="flex items-start gap-4">
                  <div className="text-2xl">{activity.icon}</div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-medium">{activity.title}</h4>
                      <div className="text-sm text-green-600 font-medium">
                        +{activity.experience} XP
                      </div>
                    </div>
                    <p className="text-sm text-default-600 mb-2">
                      {activity.description}
                    </p>
                    <div className="text-xs text-default-500">
                      {formatDate(activity.timestamp)}
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="4xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h2 className="text-2xl font-bold">Progress Tracker</h2>
          <p className="text-default-600 font-normal">
            Monitor your mission progression and achievements
          </p>
        </ModalHeader>
        
        <ModalBody>
          <Tabs 
            selectedKey={activeTab} 
            onSelectionChange={setActiveTab}
            className="mb-6"
          >
            <Tab key="overview" title="Overview">
              {renderOverviewTab()}
            </Tab>
            <Tab key="achievements" title={`Achievements (${userProgress.achievements.length})`}>
              {renderAchievementsTab()}
            </Tab>
            <Tab key="skills" title="Skills">
              {renderSkillsTab()}
            </Tab>
            <Tab key="activity" title="Activity">
              {renderActivityTab()}
            </Tab>
          </Tabs>
        </ModalBody>
        
        <ModalFooter>
          <Button color="danger" variant="flat" onPress={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ProgressTracker;

import React from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Avatar } from '@heroui/react';
import { motion } from 'framer-motion';

/**
 * Mission Card Component - Mission Display Component for Bento Grid
 * 
 * Features:
 * - Compact mission information display with story elements
 * - Progress tracking and difficulty indicators
 * - Reward preview and skill requirements
 * - Action buttons for different mission states
 * - Responsive design for bento grid layout
 */
const QuestCard = ({
  mission,
  index = 0,
  type = 'available', // 'available', 'active', 'completed'
  onStart,
  onContinue,
  onReview,
  getDifficultyColor,
  className = ""
}) => {
  
  // Get mission type icon
  const getQuestTypeIcon = (missionType) => {
    const icons = {
      'skill_development': '📚',
      'leadership': '👑',
      'development': '💻',
      'design': '🎨',
      'research': '🔬',
      'collaboration': '🤝'
    };
    return icons[missionType] || '⚔️';
  };

  // Get story theme icon
  const getStoryThemeIcon = (theme) => {
    const icons = {
      'Ancient Code Temple': '🏛️',
      'Kingdom Building': '🏰',
      'Digital Treasury': '💰',
      'Mystic Forest': '🌲',
      'Space Station': '🚀',
      'Underwater City': '🌊'
    };
    return icons[theme] || '🗺️';
  };

  // Format time
  const formatTime = (date) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  // Get time remaining for active missions
  const getTimeRemaining = () => {
    if (type !== 'active' || !mission.estimatedCompletion) return '';
    
    const now = new Date();
    const completion = new Date(mission.estimatedCompletion);
    const diffTime = completion - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'Overdue';
    if (diffDays === 0) return 'Due today';
    if (diffDays === 1) return '1 day left';
    return `${diffDays} days left`;
  };

  // Render action button based on mission type
  const renderActionButton = () => {
    switch (type) {
      case 'available':
        return (
          <Button
            color="primary"
            size="sm"
            className="w-full"
            onClick={onStart}
          >
            Start Mission
          </Button>
        );
      case 'active':
        return (
          <Button
            color="success"
            size="sm"
            className="w-full"
            onClick={onContinue}
          >
            Continue
          </Button>
        );
      case 'completed':
        return (
          <Button
            color="default"
            variant="bordered"
            size="sm"
            className="w-full"
            onClick={onReview}
          >
            Review
          </Button>
        );
      default:
        return null;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      className={className}
    >
      <Card className={`h-full hover:shadow-lg transition-all duration-300 ${
        type === 'active' ? 'ring-2 ring-primary' : ''
      }`}>
        <CardHeader className="pb-2">
          <div className="flex items-start justify-between w-full">
            <div className="flex items-center gap-3">
              <div className="text-2xl">
                {getQuestTypeIcon(mission.type)}
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-lg line-clamp-2">
                  {mission.title}
                </h3>
                {mission.story && (
                  <div className="flex items-center gap-1 mt-1">
                    <span className="text-sm">
                      {getStoryThemeIcon(mission.story.theme)}
                    </span>
                    <span className="text-xs text-default-500">
                      {mission.story.theme}
                    </span>
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex flex-col items-end gap-1">
              <Chip
                color={getDifficultyColor(mission.difficulty)}
                size="sm"
                variant="flat"
              >
                {mission.difficulty}
              </Chip>
              {type === 'active' && mission.progress !== undefined && (
                <Chip color="primary" size="sm" variant="flat">
                  {mission.progress}%
                </Chip>
              )}
            </div>
          </div>
        </CardHeader>

        <CardBody className="pt-0">
          {/* Mission Description */}
          <p className="text-sm text-default-600 mb-4 line-clamp-3">
            {mission.description}
          </p>

          {/* Story Narrative (if available) */}
          {mission.story?.narrative && (
            <div className="mb-4 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="text-xs text-purple-600 dark:text-purple-400 italic line-clamp-2">
                "{mission.story.narrative}"
              </div>
              {mission.story.chapters && (
                <div className="text-xs text-default-500 mt-1">
                  {type === 'active' && mission.story.currentChapter 
                    ? `Chapter ${mission.story.currentChapter}/${mission.story.chapters}`
                    : `${mission.story.chapters} chapters`
                  }
                </div>
              )}
            </div>
          )}

          {/* Progress Bar (for active missions) */}
          {type === 'active' && mission.progress !== undefined && (
            <div className="mb-4">
              <div className="flex justify-between text-sm mb-1">
                <span>Progress</span>
                <span>{mission.progress}%</span>
              </div>
              <Progress 
                value={mission.progress} 
                color="success" 
                size="sm"
              />
              {getTimeRemaining() && (
                <div className="text-xs text-default-500 mt-1">
                  {getTimeRemaining()}
                </div>
              )}
            </div>
          )}

          {/* Mission Metrics */}
          <div className="grid grid-cols-2 gap-3 mb-4">
            <div className="text-center p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-sm font-bold text-blue-600">
                {mission.experience} XP
              </div>
              <div className="text-xs text-default-600">
                Experience
              </div>
            </div>
            
            <div className="text-center p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-sm font-bold text-green-600">
                {mission.rewards?.orbs || 0}
              </div>
              <div className="text-xs text-default-600">
                ORBs
              </div>
            </div>
          </div>

          {/* Time Information */}
          <div className="flex justify-between text-xs text-default-500 mb-4">
            <span>
              {type === 'available' && mission.estimatedTime && `Duration: ${mission.estimatedTime}`}
              {type === 'active' && mission.startedAt && `Started: ${formatTime(mission.startedAt)}`}
              {type === 'completed' && mission.completedAt && `Completed: ${formatTime(mission.completedAt)}`}
            </span>
            {mission.participants && type === 'available' && (
              <span>{mission.participants} participants</span>
            )}
          </div>

          {/* Required Skills */}
          {mission.requirements?.skills && mission.requirements.skills.length > 0 && (
            <div className="mb-4">
              <div className="text-xs text-default-500 mb-2">Required Skills:</div>
              <div className="flex flex-wrap gap-1">
                {mission.requirements.skills.slice(0, 3).map((skill) => (
                  <Chip
                    key={skill}
                    size="sm"
                    variant="bordered"
                    className="text-xs"
                  >
                    {skill}
                  </Chip>
                ))}
                {mission.requirements.skills.length > 3 && (
                  <Chip
                    size="sm"
                    variant="bordered"
                    className="text-xs"
                  >
                    +{mission.requirements.skills.length - 3}
                  </Chip>
                )}
              </div>
            </div>
          )}

          {/* Rewards Preview */}
          {mission.rewards?.achievements && mission.rewards.achievements.length > 0 && (
            <div className="mb-4">
              <div className="text-xs text-default-500 mb-2">Rewards:</div>
              <div className="flex flex-wrap gap-1">
                {mission.rewards.achievements.slice(0, 2).map((achievement) => (
                  <Chip
                    key={achievement}
                    size="sm"
                    color="warning"
                    variant="flat"
                    className="text-xs"
                    startContent={<span>🏆</span>}
                  >
                    {achievement}
                  </Chip>
                ))}
              </div>
            </div>
          )}

          {/* Completion Rate (for available missions) */}
          {type === 'available' && mission.completionRate && (
            <div className="mb-4">
              <div className="flex justify-between text-xs mb-1">
                <span>Success Rate</span>
                <span>{mission.completionRate}%</span>
              </div>
              <Progress 
                value={mission.completionRate} 
                color="warning" 
                size="sm"
              />
            </div>
          )}

          {/* Action Button */}
          {renderActionButton()}
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default QuestCard;

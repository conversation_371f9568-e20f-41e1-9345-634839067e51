import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button, Input, Textarea, Card, CardBody } from '@heroui/react';

/**
 * AllianceQuestionStep Component
 * 
 * Renders individual question steps in the studio creation flow
 * Handles different question types: selection, details
 * Follows wireframe design with large touch targets and clear hierarchy
 */
const AllianceQuestionStep = ({ 
  question, 
  answer, 
  onAnswer, 
  onNext, 
  isAnswered 
}) => {
  const [localValues, setLocalValues] = useState({
    allianceName: answer.allianceName || '',
    allianceDescription: answer.allianceDescription || '',
    allianceIcon: answer.allianceIcon || '🏰'
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    },
    exit: {
      opacity: 0,
      y: -30,
      transition: { duration: 0.3 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 }
    }
  };

  // Handle option selection
  const handleOptionSelect = (value) => {
    onAnswer(question.id, value);
    // Auto-advance for selection questions
    setTimeout(() => {
      onNext();
    }, 300);
  };

  // Handle studio details form
  const handleDetailsChange = (field, value) => {
    const updated = { ...localValues, [field]: value };
    setLocalValues(updated);
    onAnswer(question.id, updated);
  };

  // Render different question types
  const renderQuestionContent = () => {
    switch (question.type) {
      case 'project_type_selection':
      case 'adaptive_selection':
      case 'start_date_selection':
      case 'payment_model_selection':
      case 'team_roles_selection':
      case 'invitation_method_selection':
        return renderSelectionOptions();
      
      case 'alliance_details':
        return renderAllianceDetails();
      
      default:
        return null;
    }
  };

  // Render selection options
  const renderSelectionOptions = () => (
    <div className="space-y-4 max-w-2xl mx-auto">
      {question.options?.map((option, index) => (
        <motion.div
          key={option.value}
          variants={itemVariants}
          custom={index}
        >
          <Card
            isPressable
            onPress={() => handleOptionSelect(option.value)}
            className="bg-content1 border border-default-200 hover:border-primary-300 hover:bg-primary-50 transition-colors cursor-pointer"
          >
            <CardBody className="p-6">
              <div className="flex items-center space-x-4">
                <div className="text-4xl">{option.icon}</div>
                <div className="flex-1 text-left">
                  <h3 className="text-xl font-semibold text-foreground mb-1">
                    {option.title}
                  </h3>
                  <p className="text-default-600">
                    {option.description}
                  </p>
                </div>
                <div className="text-default-400">
                  <i className="bi bi-chevron-right text-xl"></i>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      ))}
    </div>
  );

  // Render studio details form
  const renderAllianceDetails = () => (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Studio Name */}
      <motion.div variants={itemVariants}>
        <label className="block text-foreground text-lg font-medium mb-3">
          Studio Name
        </label>
        <Input
          value={localValues.allianceName}
          onChange={(e) => handleDetailsChange('allianceName', e.target.value)}
          placeholder="The Dream Team Studio"
          size="lg"
          variant="bordered"
          classNames={{
            input: "text-foreground text-lg",
            inputWrapper: "h-14 border-default-300 hover:border-primary-400 focus-within:border-primary-500"
          }}
        />
      </motion.div>

      {/* Suggestions */}
      <motion.div variants={itemVariants}>
        <div className="text-default-600 text-sm">
          💡 Suggestions based on your project:
          <div className="mt-2 space-x-2">
            {getSuggestions().map((suggestion, index) => (
              <Button
                key={index}
                size="sm"
                variant="bordered"
                className="text-default-600 border-default-300 hover:bg-default-100"
                onPress={() => handleDetailsChange('allianceName', suggestion)}
              >
                {suggestion}
              </Button>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Description */}
      <motion.div variants={itemVariants}>
        <label className="block text-foreground text-lg font-medium mb-3">
          Description (optional)
        </label>
        <Textarea
          value={localValues.allianceDescription}
          onChange={(e) => handleDetailsChange('allianceDescription', e.target.value)}
          placeholder="A group of passionate developers building the future of creative collaboration..."
          minRows={3}
          variant="bordered"
          classNames={{
            input: "text-foreground",
            inputWrapper: "border-default-300 hover:border-primary-400 focus-within:border-primary-500"
          }}
        />
      </motion.div>

      {/* Icon Picker */}
      <motion.div variants={itemVariants}>
        <label className="block text-foreground text-lg font-medium mb-3">
          🎨 Choose an icon:
        </label>
        <div className="flex space-x-3">
          {question.fields.find(f => f.name === 'allianceIcon')?.options.map((icon) => (
            <Button
              key={icon}
              size="lg"
              variant={localValues.allianceIcon === icon ? "solid" : "bordered"}
              className={`text-2xl ${
                localValues.allianceIcon === icon 
                  ? "bg-primary text-white" 
                  : "text-foreground border-default-300 hover:bg-default-100"
              }`}
              onPress={() => handleDetailsChange('allianceIcon', icon)}
            >
              {icon}
            </Button>
          ))}
        </div>
      </motion.div>

      {/* Continue Button */}
      <motion.div variants={itemVariants} className="pt-6">
        <Button
          size="lg"
          className="w-full bg-primary text-white font-semibold py-4 text-lg"
          onPress={onNext}
          isDisabled={!localValues.allianceName}
        >
          Continue
        </Button>
      </motion.div>
    </div>
  );

  // Get name suggestions based on previous answers
  const getSuggestions = () => {
    const suggestions = [];
    
    if (answer.projectType === 'business') {
      suggestions.push('Business Studio', 'Professional Collective', 'Enterprise Team');
    } else if (answer.projectType === 'personal') {
      suggestions.push('Dream Team Studio', 'Creative Collective', 'Innovation Squad');
    } else if (answer.projectType === 'opensource') {
      suggestions.push('Open Source Studio', 'Community Collective', 'Developer Guild');
    } else if (answer.projectType === 'creative') {
      suggestions.push('Creative Studio', 'Artist Collective', 'Creative Guild');
    }
    
    return suggestions.slice(0, 3);
  };

  return (
    <motion.div
      className="text-center"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      {/* Question Title */}
      <motion.h1 
        variants={itemVariants}
        className="text-4xl md:text-5xl font-bold text-foreground mb-12"
      >
        {question.title}
      </motion.h1>

      {/* Question Content */}
      {renderQuestionContent()}
    </motion.div>
  );
};

export default AllianceQuestionStep;

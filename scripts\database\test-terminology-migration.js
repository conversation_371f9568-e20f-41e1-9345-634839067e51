#!/usr/bin/env node

/**
 * Test Terminology Migration
 * Tests the new terminology migration safely
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Use the known Supabase URL and service key
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testMigration() {
  console.log('🧪 Testing Terminology Migration...');
  console.log('=====================================\n');

  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, '../../supabase/migrations/20250625000001_terminology_update_studios_projects_missions.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('1️⃣ Applying terminology migration...');
    
    // Execute the migration using the rpc function
    const { data, error: migrationError } = await supabase.rpc('exec', { 
      sql: migrationSQL 
    });

    if (migrationError) {
      console.error('❌ Migration failed:', migrationError.message);
      throw migrationError;
    }

    console.log('✅ Migration applied successfully');

    // Test the new columns and tables
    console.log('\n2️⃣ Testing new terminology columns...');
    
    // Test teams.studio_type
    console.log('📋 Testing teams.studio_type...');
    const { data: teamsTest, error: teamsError } = await supabase
      .from('teams')
      .select('id, name, studio_type')
      .limit(1);

    if (teamsError) {
      console.log(`   ❌ Error: ${teamsError.message}`);
    } else {
      console.log('   ✅ teams.studio_type column accessible');
    }

    // Test projects.project_type and studio_id
    console.log('📋 Testing projects.project_type and studio_id...');
    const { data: projectsTest, error: projectsError } = await supabase
      .from('projects')
      .select('id, name, project_type, studio_id')
      .limit(1);

    if (projectsError) {
      console.log(`   ❌ Error: ${projectsError.message}`);
    } else {
      console.log('   ✅ projects.project_type and studio_id columns accessible');
    }

    // Test tasks mission columns
    console.log('📋 Testing tasks mission columns...');
    const { data: tasksTest, error: tasksError } = await supabase
      .from('tasks')
      .select('id, title, task_category, mission_type, mission_requirements, mission_rewards')
      .limit(1);

    if (tasksError) {
      console.log(`   ❌ Error: ${tasksError.message}`);
    } else {
      console.log('   ✅ tasks mission columns accessible');
    }

    // Test team_members collaboration columns
    console.log('📋 Testing team_members collaboration columns...');
    const { data: membersTest, error: membersError } = await supabase
      .from('team_members')
      .select('id, user_id, collaboration_type, engagement_duration, specialization')
      .limit(1);

    if (membersError) {
      console.log(`   ❌ Error: ${membersError.message}`);
    } else {
      console.log('   ✅ team_members collaboration columns accessible');
    }

    // Test new tables
    console.log('\n3️⃣ Testing new terminology tables...');
    
    const newTables = [
      'studio_invitations',
      'studio_preferences',
      'user_missions'
    ];

    for (const tableName of newTables) {
      console.log(`📋 Testing ${tableName} table...`);
      const { data, error } = await supabase
        .from(tableName)
        .select('id')
        .limit(1);

      if (error) {
        console.log(`   ❌ Error: ${error.message}`);
      } else {
        console.log(`   ✅ ${tableName} table accessible`);
      }
    }

    // Test inserting sample data
    console.log('\n4️⃣ Testing data insertion with new terminology...');
    
    // Test inserting a team with studio_type
    console.log('📋 Testing team insertion with studio_type...');
    const { data: newTeam, error: teamInsertError } = await supabase
      .from('teams')
      .insert([{
        name: 'Test Studio',
        description: 'A test studio for terminology migration',
        studio_type: 'emerging'
      }])
      .select()
      .single();

    if (teamInsertError) {
      console.log(`   ❌ Error: ${teamInsertError.message}`);
    } else {
      console.log('   ✅ Team with studio_type inserted successfully');
      
      // Test inserting a project with project_type and studio_id
      console.log('📋 Testing project insertion with new terminology...');
      const { data: newProject, error: projectInsertError } = await supabase
        .from('projects')
        .insert([{
          name: 'Test Project',
          description: 'A test project for terminology migration',
          project_type: 'software',
          studio_id: newTeam.id
        }])
        .select()
        .single();

      if (projectInsertError) {
        console.log(`   ❌ Error: ${projectInsertError.message}`);
      } else {
        console.log('   ✅ Project with new terminology inserted successfully');
        
        // Test inserting a task with mission terminology
        console.log('📋 Testing task insertion with mission terminology...');
        const { data: newTask, error: taskInsertError } = await supabase
          .from('tasks')
          .insert([{
            title: 'Test Mission',
            description: 'A test mission for terminology migration',
            project_id: newProject.id,
            task_category: 'mission',
            mission_type: 'skill',
            mission_requirements: { complete_tasks: { count: 1 } },
            mission_rewards: { points: 100 }
          }])
          .select()
          .single();

        if (taskInsertError) {
          console.log(`   ❌ Error: ${taskInsertError.message}`);
        } else {
          console.log('   ✅ Task with mission terminology inserted successfully');
        }
      }
      
      // Clean up test data
      console.log('\n5️⃣ Cleaning up test data...');
      await supabase.from('tasks').delete().eq('title', 'Test Mission');
      await supabase.from('projects').delete().eq('name', 'Test Project');
      await supabase.from('teams').delete().eq('name', 'Test Studio');
      console.log('   ✅ Test data cleaned up');
    }

    console.log('\n🎉 Terminology Migration Test Complete!');
    console.log('✅ All new terminology columns and tables are working correctly');

  } catch (error) {
    console.error('\n❌ Migration test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Test database connection first
async function testConnection() {
  console.log('🔌 Testing database connection...');
  
  try {
    const { data, error } = await supabase
      .from('teams')
      .select('id')
      .limit(1);

    if (error) {
      throw error;
    }

    console.log('✅ Database connection successful\n');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

// Main execution
async function main() {
  const connected = await testConnection();
  if (!connected) {
    process.exit(1);
  }

  await testMigration();
}

// Handle command line execution
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { testMigration };

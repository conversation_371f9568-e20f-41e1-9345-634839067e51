// Test Studio & Project System
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_KEY);

// Test user IDs
const TEST_USER_1 = '2a033231-d173-4292-aa36-90f4d735bcf3';
const TEST_USER_2 = '2a033231-d173-4292-aa36-90f4d735bcf3'; // Same for testing

async function testStudioProjectSystem() {
  console.log('🧪 Testing Studio & Project System...');

  let testStudioId, testProjectId, testInvitationId, testRevenueId;
  
  try {
    // Test 1: Create Studio
    console.log('\n1️⃣ Testing studio creation...');

    const studioData = {
      name: 'Test Tech Studio',
      description: 'A test studio for software development and innovation',
      studio_type: 'established',
      industry: 'Technology',
      business_model: {
        revenue_sharing: {
          method: 'contribution_based',
          base_percentage: 70
        },
        commission_rate: 15,
        recurring_fee: 99.99,
        billing_cycle: 'monthly'
      },
      max_members: 25,
      is_public: true,
      status: 'active',
      created_by: TEST_USER_1
    };

    const { data: studio, error: studioError } = await supabase
      .from('teams')
      .insert([studioData])
      .select()
      .single();

    if (studioError) {
      console.log('❌ Studio creation failed:', studioError.message);
    } else {
      testStudioId = studio.id;
      console.log('✅ Studio created:', testStudioId);
      console.log(`   Name: ${studio.name}`);
      console.log(`   Type: ${studio.studio_type}`);
      console.log(`   Industry: ${studio.industry}`);
      console.log(`   Max Members: ${studio.max_members}`);
    }
    
    // Test 2: Add Alliance Member
    console.log('\n2️⃣ Testing alliance member addition...');
    
    const memberData = {
      team_id: testAllianceId,
      user_id: TEST_USER_1,
      role: 'founder',
      status: 'active',
      permissions: {
        can_invite_members: true,
        can_create_ventures: true,
        can_manage_revenue: true,
        can_edit_alliance: true
      },
      joined_at: new Date().toISOString()
    };
    
    const { data: member, error: memberError } = await supabase
      .from('team_members')
      .insert([memberData])
      .select()
      .single();
    
    if (memberError) {
      console.log('❌ Member addition failed:', memberError.message);
    } else {
      console.log('✅ Alliance member added:', member.id);
      console.log(`   Role: ${member.role}`);
      console.log(`   Status: ${member.status}`);
      console.log(`   Permissions: ${Object.keys(member.permissions || {}).length} permissions set`);
    }
    
    // Test 3: Create Alliance Invitation
    console.log('\n3️⃣ Testing alliance invitation...');
    
    const invitationData = {
      alliance_id: testAllianceId,
      email: '<EMAIL>',
      role: 'member',
      invited_by: TEST_USER_1,
      message: 'Join our tech alliance for exciting collaboration opportunities!',
      status: 'pending',
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
    };
    
    const { data: invitation, error: invitationError } = await supabase
      .from('alliance_invitations')
      .insert([invitationData])
      .select()
      .single();
    
    if (invitationError) {
      console.log('❌ Invitation creation failed:', invitationError.message);
    } else {
      testInvitationId = invitation.id;
      console.log('✅ Alliance invitation created:', testInvitationId);
      console.log(`   Email: ${invitation.email}`);
      console.log(`   Role: ${invitation.role}`);
      console.log(`   Status: ${invitation.status}`);
      console.log(`   Expires: ${new Date(invitation.expires_at).toLocaleDateString()}`);
    }
    
    // Test 4: Create Alliance Preferences
    console.log('\n4️⃣ Testing alliance preferences...');
    
    const preferencesData = {
      alliance_id: testAllianceId,
      invite_members_enabled: true,
      shared_workspace_enabled: true,
      auto_venture_creation: false,
      notification_settings: {
        new_member_notifications: true,
        revenue_notifications: true,
        venture_updates: true,
        weekly_digest: true
      },
      privacy_settings: {
        public_member_list: true,
        public_venture_list: false,
        public_revenue_stats: false
      }
    };
    
    const { data: preferences, error: preferencesError } = await supabase
      .from('alliance_preferences')
      .insert([preferencesData])
      .select()
      .single();
    
    if (preferencesError) {
      console.log('❌ Preferences creation failed:', preferencesError.message);
    } else {
      console.log('✅ Alliance preferences created:', preferences.id);
      console.log(`   Invite members: ${preferences.invite_members_enabled}`);
      console.log(`   Shared workspace: ${preferences.shared_workspace_enabled}`);
      console.log(`   Notification settings: ${Object.keys(preferences.notification_settings || {}).length} configured`);
    }
    
    // Test 5: Create Venture
    console.log('\n5️⃣ Testing venture creation...');
    
    const ventureData = {
      name: 'AI-Powered Analytics Platform',
      title: 'Revolutionary AI Analytics for Business Intelligence',
      description: 'A cutting-edge platform that uses machine learning to provide actionable business insights',
      alliance_id: testAllianceId,
      venture_type: 'software',
      revenue_model: {
        type: 'subscription',
        pricing: {
          base_price: 99.99,
          currency: 'USD',
          billing_cycle: 'monthly'
        },
        distribution: {
          alliance_share: 60,
          platform_share: 25,
          contributor_share: 15
        },
        tiers: [
          { name: 'Starter', price: 99.99, features: ['Basic Analytics', '5 Users'] },
          { name: 'Professional', price: 299.99, features: ['Advanced Analytics', '25 Users', 'API Access'] },
          { name: 'Enterprise', price: 999.99, features: ['Full Suite', 'Unlimited Users', 'Custom Integration'] }
        ]
      },
      milestone_config: {
        phases: [
          { name: 'MVP Development', duration: 90, budget: 50000 },
          { name: 'Beta Testing', duration: 30, budget: 15000 },
          { name: 'Launch', duration: 15, budget: 10000 }
        ],
        success_criteria: [
          'Complete core analytics engine',
          'Achieve 95% uptime',
          'Onboard 100 beta users'
        ]
      },
      status: 'planning',
      is_active: true,
      created_by: TEST_USER_1,
      start_date: new Date().toISOString(),
      target_completion_date: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000).toISOString() // 6 months
    };
    
    const { data: venture, error: ventureError } = await supabase
      .from('projects')
      .insert([ventureData])
      .select()
      .single();
    
    if (ventureError) {
      console.log('❌ Venture creation failed:', ventureError.message);
    } else {
      testVentureId = venture.id;
      console.log('✅ Venture created:', testVentureId);
      console.log(`   Name: ${venture.name}`);
      console.log(`   Type: ${venture.venture_type}`);
      console.log(`   Status: ${venture.status}`);
      console.log(`   Revenue model: ${venture.revenue_model.type}`);
      console.log(`   Target completion: ${new Date(venture.target_completion_date).toLocaleDateString()}`);
    }
    
    // Test 6: Add Venture Contributor
    console.log('\n6️⃣ Testing venture contributor addition...');
    
    const contributorData = {
      project_id: testVentureId,
      user_id: TEST_USER_1,
      role: 'lead',
      status: 'active',
      contribution_percentage: 100, // 100% for single contributor test
      joined_at: new Date().toISOString()
    };
    
    const { data: contributor, error: contributorError } = await supabase
      .from('project_contributors')
      .insert([contributorData])
      .select()
      .single();
    
    if (contributorError) {
      console.log('❌ Contributor addition failed:', contributorError.message);
    } else {
      console.log('✅ Venture contributor added:', contributor.id);
      console.log(`   Role: ${contributor.role}`);
      console.log(`   Contribution: ${contributor.contribution_percentage}%`);
      console.log(`   Status: ${contributor.status}`);
    }
    
    // Test 7: Create Revenue Record
    console.log('\n7️⃣ Testing revenue tracking...');
    
    const revenueData = {
      venture_id: testVentureId,
      alliance_id: testAllianceId,
      amount: 2500.00,
      currency: 'USD',
      source: 'subscription_payment',
      source_reference: 'stripe_pi_1234567890',
      transaction_date: new Date().toISOString(),
      description: 'Monthly subscription revenue from 25 Professional tier customers',
      recorded_by: TEST_USER_1,
      distribution_data: {
        alliance_share: 1500.00,
        platform_share: 625.00,
        contributor_shares: {
          [TEST_USER_1]: 375.00
        },
        total_distributed: 2500.00
      },
      status: 'pending_distribution'
    };
    
    const { data: revenue, error: revenueError } = await supabase
      .from('revenue_tracking')
      .insert([revenueData])
      .select()
      .single();
    
    if (revenueError) {
      console.log('❌ Revenue tracking failed:', revenueError.message);
    } else {
      testRevenueId = revenue.id;
      console.log('✅ Revenue record created:', testRevenueId);
      console.log(`   Amount: $${revenue.amount} ${revenue.currency}`);
      console.log(`   Source: ${revenue.source}`);
      console.log(`   Alliance share: $${revenue.distribution_data.alliance_share}`);
      console.log(`   Platform share: $${revenue.distribution_data.platform_share}`);
      console.log(`   Status: ${revenue.status}`);
    }
    
    // Test 8: Create Revenue Distributions
    console.log('\n8️⃣ Testing revenue distribution...');
    
    const distributionRecords = [
      {
        revenue_id: testRevenueId,
        recipient_type: 'alliance',
        recipient_id: testAllianceId,
        amount: 1500.00,
        currency: 'USD',
        status: 'pending'
      },
      {
        revenue_id: testRevenueId,
        recipient_type: 'platform',
        recipient_id: null,
        amount: 625.00,
        currency: 'USD',
        status: 'pending'
      },
      {
        revenue_id: testRevenueId,
        recipient_type: 'user',
        recipient_id: TEST_USER_1,
        amount: 375.00,
        currency: 'USD',
        status: 'pending'
      }
    ];
    
    const { data: distributions, error: distributionError } = await supabase
      .from('revenue_distributions')
      .insert(distributionRecords)
      .select();
    
    if (distributionError) {
      console.log('❌ Revenue distribution failed:', distributionError.message);
    } else {
      console.log(`✅ Revenue distributions created: ${distributions.length} records`);
      distributions.forEach((dist, index) => {
        console.log(`   ${index + 1}. ${dist.recipient_type}: $${dist.amount} (${dist.status})`);
      });
    }
    
    // Test 9: Create Mission/Bounty Task
    console.log('\n9️⃣ Testing mission/bounty creation...');
    
    const taskData = {
      project_id: testVentureId,
      title: 'Implement User Authentication System',
      description: 'Design and implement a secure user authentication system with OAuth2 support',
      task_category: 'bounty',
      bounty_amount: 1500.00,
      bounty_currency: 'USD',
      is_public: true,
      required_skills: ['JavaScript', 'Node.js', 'OAuth2', 'Security'],
      deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
      priority: 'high',
      status: 'open',
      created_by: TEST_USER_1
    };
    
    const { data: task, error: taskError } = await supabase
      .from('tasks')
      .insert([taskData])
      .select()
      .single();
    
    if (taskError) {
      console.log('❌ Task creation failed:', taskError.message);
    } else {
      console.log('✅ Mission/bounty task created:', task.id);
      console.log(`   Title: ${task.title}`);
      console.log(`   Category: ${task.task_category}`);
      console.log(`   Bounty: $${task.bounty_amount} ${task.bounty_currency}`);
      console.log(`   Priority: ${task.priority}`);
      console.log(`   Required skills: ${task.required_skills.length} skills`);
      console.log(`   Deadline: ${new Date(task.deadline).toLocaleDateString()}`);
    }
    
    // Test 10: Create Bounty Application
    console.log('\n🔟 Testing bounty application...');
    
    const applicationData = {
      task_id: task.id,
      applicant_id: TEST_USER_1,
      application_message: 'I have 5+ years of experience in authentication systems and have implemented OAuth2 in multiple projects.',
      proposed_timeline: '2-3 weeks for complete implementation and testing',
      proposed_approach: 'Use Passport.js with OAuth2 strategy, implement JWT tokens, add rate limiting and security headers',
      status: 'pending'
    };
    
    const { data: application, error: applicationError } = await supabase
      .from('bounty_applications')
      .insert([applicationData])
      .select()
      .single();
    
    if (applicationError) {
      console.log('❌ Bounty application failed:', applicationError.message);
    } else {
      console.log('✅ Bounty application created:', application.id);
      console.log(`   Applicant: ${application.applicant_id}`);
      console.log(`   Status: ${application.status}`);
      console.log(`   Timeline: ${application.proposed_timeline}`);
    }
    
    // Test 11: Test Complex Queries
    console.log('\n1️⃣1️⃣ Testing complex alliance queries...');
    
    // Test alliance with members and ventures
    const { data: allianceDetails, error: detailsError } = await supabase
      .from('teams')
      .select(`
        id,
        name,
        alliance_type,
        industry,
        business_model,
        members:team_members(
          id,
          role,
          status,
          joined_at,
          user:users(
            id,
            display_name,
            email
          )
        ),
        ventures:projects(
          id,
          name,
          venture_type,
          status,
          revenue_model
        ),
        invitations:alliance_invitations(
          id,
          email,
          role,
          status
        )
      `)
      .eq('id', testAllianceId)
      .single();
    
    if (detailsError) {
      console.log('❌ Alliance details query failed:', detailsError.message);
    } else {
      console.log('✅ Alliance details query successful:');
      console.log(`   Alliance: ${allianceDetails.name}`);
      console.log(`   Members: ${allianceDetails.members?.length || 0}`);
      console.log(`   Ventures: ${allianceDetails.ventures?.length || 0}`);
      console.log(`   Pending invitations: ${allianceDetails.invitations?.filter(i => i.status === 'pending').length || 0}`);
    }
    
    // Test revenue analytics query
    const { data: revenueAnalytics, error: analyticsError } = await supabase
      .from('revenue_tracking')
      .select(`
        id,
        amount,
        currency,
        source,
        transaction_date,
        status,
        distributions:revenue_distributions(
          id,
          recipient_type,
          amount,
          status
        )
      `)
      .eq('alliance_id', testAllianceId)
      .order('transaction_date', { ascending: false });
    
    if (analyticsError) {
      console.log('❌ Revenue analytics query failed:', analyticsError.message);
    } else {
      console.log('✅ Revenue analytics query successful:');
      console.log(`   Revenue records: ${revenueAnalytics?.length || 0}`);
      
      if (revenueAnalytics && revenueAnalytics.length > 0) {
        const totalRevenue = revenueAnalytics.reduce((sum, r) => sum + r.amount, 0);
        const totalDistributions = revenueAnalytics.reduce((sum, r) => 
          sum + (r.distributions?.reduce((dSum, d) => dSum + d.amount, 0) || 0), 0);
        
        console.log(`   Total revenue: $${totalRevenue.toFixed(2)}`);
        console.log(`   Total distributions: $${totalDistributions.toFixed(2)}`);
      }
    }
    
    // Test 12: Test Performance
    console.log('\n1️⃣2️⃣ Testing query performance...');
    
    const startTime = Date.now();
    
    // Complex join query for alliance dashboard
    const { data: dashboardData, error: dashboardError } = await supabase
      .from('teams')
      .select(`
        id,
        name,
        alliance_type,
        industry,
        created_at,
        member_count:team_members(count),
        venture_count:projects(count),
        total_revenue:revenue_tracking(amount.sum())
      `)
      .eq('id', testAllianceId)
      .single();
    
    const queryTime = Date.now() - startTime;
    
    if (dashboardError) {
      console.log('❌ Dashboard query failed:', dashboardError.message);
    } else {
      console.log(`✅ Dashboard query completed in ${queryTime}ms`);
      console.log(`   Alliance: ${dashboardData.name}`);
      console.log(`   Type: ${dashboardData.alliance_type}`);
      console.log(`   Industry: ${dashboardData.industry}`);
    }
    
    console.log('\n🎉 Alliance & Venture System tests completed!');
    console.log('✅ Alliance creation and management working');
    console.log('✅ Member management and invitations functioning');
    console.log('✅ Venture creation and configuration working');
    console.log('✅ Revenue tracking and distribution operational');
    console.log('✅ Mission/bounty system functioning');
    console.log('✅ Complex queries performing well');
    console.log('✅ Business model validation working');
    console.log('✅ Permission system operational');
    console.log('✅ Data relationships maintained');
    console.log('✅ Query performance acceptable');
    
  } catch (error) {
    console.error('❌ Alliance & Venture system test failed:', error);
  } finally {
    // Cleanup test data
    console.log('\n🧹 Cleaning up test data...');
    
    // Clean up in reverse order of dependencies
    if (testRevenueId) {
      await supabase.from('revenue_distributions').delete().eq('revenue_id', testRevenueId);
      await supabase.from('revenue_tracking').delete().eq('id', testRevenueId);
    }
    
    if (testVentureId) {
      await supabase.from('bounty_applications').delete().eq('task_id', 
        supabase.from('tasks').select('id').eq('project_id', testVentureId)
      );
      await supabase.from('tasks').delete().eq('project_id', testVentureId);
      await supabase.from('project_contributors').delete().eq('project_id', testVentureId);
      await supabase.from('projects').delete().eq('id', testVentureId);
    }
    
    if (testInvitationId) {
      await supabase.from('alliance_invitations').delete().eq('id', testInvitationId);
    }
    
    if (testAllianceId) {
      await supabase.from('alliance_preferences').delete().eq('alliance_id', testAllianceId);
      await supabase.from('team_members').delete().eq('team_id', testAllianceId);
      await supabase.from('teams').delete().eq('id', testAllianceId);
    }
    
    console.log('✅ Test data cleaned up');
  }
}

// Run tests
testAllianceVentureSystem();

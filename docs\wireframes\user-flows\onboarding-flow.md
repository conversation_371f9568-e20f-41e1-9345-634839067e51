# Onboarding Flow Wireframe
**Complete New User Journey - IMMERSIVE PATTERN**

## 📋 Flow Information
- **Flow Type**: New User Onboarding
- **User Types**: All new users (post-signup)
- **Entry Points**: After email verification, OAuth completion
- **Success Outcome**: User reaches first meaningful action in <5 minutes
- **Pattern**: Full-screen immersive experience with minimal UI
- **Integration**: Connects to Alliance/Venture creation flows

---

## 🎯 **Design Philosophy**

### **<5 Minutes to First Meaningful Action**
- **Skip complex setup** - Get users doing something valuable immediately
- **Progressive disclosure** - Reveal features as needed
- **Template shortcuts** - Quick paths for experienced users
- **Context-aware** - Adapt based on user's stated goals

### **Immersive Experience Principles**
- **One question at a time** - No cognitive overload
- **Large, simple controls** - Easy interaction
- **Visual examples** - Show what each choice creates
- **Graceful exit** - Save progress, allow return
- **Template shortcuts** - "Skip to dashboard" for power users

---

## 🔄 **Complete Onboarding Flow**

```mermaid
flowchart TD
    A[Email Verified/OAuth Complete] --> B[Welcome Screen]
    B --> C[Q1: What brings you here?]
    
    C --> D{User Goal?}
    D -->|Start a Project| E[Q2A: Project Type Quick]
    D -->|Find Work| F[Q2B: Skills Quick]
    D -->|Learn Platform| G[Q2C: Tutorial Path]
    D -->|Skip Setup| H[Template Dashboard]
    
    E --> I[Q3A: Team or Solo?]
    F --> J[Q3B: Experience Level?]
    G --> K[Interactive Tutorial]
    H --> L[Dashboard with Overlay Tips]
    
    I --> M{Team Choice?}
    M -->|Team| N[Quick Alliance Setup]
    M -->|Solo| O[Quick Venture Setup]
    
    J --> P[Skill-Matched Opportunities]
    K --> Q[Tutorial Complete]
    
    N --> R[First Meaningful Action: Alliance Created]
    O --> S[First Meaningful Action: Venture Created]
    P --> T[First Meaningful Action: Applied to Bounty]
    Q --> U[First Meaningful Action: Understanding Complete]
    L --> V[First Meaningful Action: Dashboard Navigation]
    
    R --> W[Dashboard with Success Celebration]
    S --> W
    T --> W
    U --> W
    V --> W
```

---

## 📱 **Step-by-Step Wireframes**

### **Step 1: Welcome Screen**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✕                                                                           │
│                                                                             │
│                                                                             │
│                            Welcome to Royaltea                             │
│                                                                             │
│                     Where collaboration meets compensation                  │
│                                                                             │
│                                                                             │
│                              [Continue]                                     │
│                                                                             │
│                                ● ○ ○ ○                                     │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Step 2: What brings you here?**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✕                                                                           │
│                                                                             │
│                        What brings you here today?                         │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │       🚀        │  │       💼        │  │       📚        │            │
│  │                 │  │                 │  │                 │            │
│  │  Start a        │  │  Find Work &    │  │  Learn How      │            │
│  │  Project        │  │  Opportunities  │  │  It Works       │            │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘            │
│                                                                             │
│                              [Skip Setup]                                  │
│                                ○ ● ○ ○                                     │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Step 3A: Project Type (if "Start a Project")**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✕                                                                           │
│                                                                             │
│                          What are you building?                            │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │       💻        │  │       🎨        │  │       🏢        │            │
│  │                 │  │                 │  │                 │            │
│  │   Software/     │  │   Creative      │  │   Business      │            │
│  │     App         │  │    Project      │  │   Service       │            │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘            │
│                                                                             │
│                    [Other] [Use Template] [Back]                           │
│                                ○ ○ ● ○                                     │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Step 4A: Team or Solo?**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✕                                                                           │
│                                                                             │
│                        Working solo or with a team?                        │
│                                                                             │
│           ┌─────────────────────────┐  ┌─────────────────────────┐         │
│           │           👤           │  │           👥           │         │
│           │                        │  │                        │         │
│           │      Solo Project      │  │     Team Project       │         │
│           │                        │  │                        │         │
│           │   I'll handle this     │  │   I need collaborators │         │
│           │      myself            │  │                        │         │
│           └─────────────────────────┘  └─────────────────────────┘         │
│                                                                             │
│                              [Back]                                        │
│                                ○ ○ ○ ●                                     │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Step 5A: Quick Venture Setup (Solo Path)**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✕                                                                           │
│                                                                             │
│                         Let's create your venture!                         │
│                                                                             │
│                    ┌─────────────────────────────────┐                     │
│                    │                                 │                     │
│                    │         Project Name            │                     │
│                    │    [My Awesome Software]        │                     │
│                    │                                 │                     │
│                    │         Quick Start             │                     │
│                    │    ☑ Use standard settings      │                     │
│                    │    ☑ Set up basic tracking      │                     │
│                    │    ☑ Create first mission       │                     │
│                    │                                 │                     │
│                    └─────────────────────────────────┘                     │
│                                                                             │
│                    [Customize] [Create Venture]                            │
│                                ○ ○ ○ ●                                     │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Step 5B: Quick Alliance Setup (Team Path)**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✕                                                                           │
│                                                                             │
│                        Let's create your alliance!                         │
│                                                                             │
│                    ┌─────────────────────────────────┐                     │
│                    │                                 │                     │
│                    │        Alliance Name            │                     │
│                    │    [Dream Team Studios]         │                     │
│                    │                                 │                     │
│                    │         Quick Start             │                     │
│                    │    ☑ Invite team members        │                     │
│                    │    ☑ Set up shared workspace    │                     │
│                    │    ☑ Create first venture       │                     │
│                    │                                 │                     │
│                    └─────────────────────────────────┘                     │
│                                                                             │
│                    [Customize] [Create Alliance]                           │
│                                ○ ○ ○ ●                                     │
└─────────────────────────────────────────────────────────────────────────────┘
```

---

## 🎯 **Success Celebration Screens**

### **Venture Created Success**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│                                   🎉                                        │
│                                                                             │
│                         Venture Created Successfully!                      │
│                                                                             │
│                    Your "My Awesome Software" venture is                    │
│                         ready for your first mission                       │
│                                                                             │
│                         ┌─────────────────────┐                           │
│                         │   Create Mission    │                           │
│                         └─────────────────────┘                           │
│                                                                             │
│                              [Go to Dashboard]                             │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Alliance Created Success**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│                                   🎉                                        │
│                                                                             │
│                        Alliance Created Successfully!                      │
│                                                                             │
│                   Your "Dream Team Studios" alliance is                     │
│                        ready for collaboration                             │
│                                                                             │
│                         ┌─────────────────────┐                           │
│                         │   Invite Members    │                           │
│                         └─────────────────────┘                           │
│                                                                             │
│                              [Go to Dashboard]                             │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

---

## 🔍 **Alternative Paths**

### **Path B: Find Work (Skills-Based)**

#### **Step 3B: Experience Level**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✕                                                                           │
│                                                                             │
│                      What's your experience level?                         │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │       🌱        │  │       ⚡        │  │       🏆        │            │
│  │                 │  │                 │  │                 │            │
│  │   Just Getting  │  │   Experienced   │  │     Expert      │            │
│  │    Started      │  │   Professional  │  │   Specialist    │            │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘            │
│                                                                             │
│                              [Back]                                        │
│                                ○ ○ ● ○                                     │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### **Step 4B: Skill Matching**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✕                                                                           │
│                                                                             │
│                        Great! Here are perfect matches                     │
│                                                                             │
│                    ┌─────────────────────────────────┐                     │
│                    │  🎯 Frontend Developer Needed   │                     │
│                    │     React • TypeScript          │                     │
│                    │     $2,500 • Remote             │                     │
│                    │     [Apply Now]                 │                     │
│                    └─────────────────────────────────┘                     │
│                                                                             │
│                    ┌─────────────────────────────────┐                     │
│                    │  🎨 UI/UX Designer Wanted       │                     │
│                    │     Figma • Design Systems      │                     │
│                    │     $1,800 • Flexible          │                     │
│                    │     [Apply Now]                 │                     │
│                    └─────────────────────────────────┘                     │
│                                                                             │
│                              [Browse More]                                 │
│                                ○ ○ ○ ●                                     │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Path C: Learn Platform**

#### **Interactive Tutorial**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✕                                                                           │
│                                                                             │
│                         How Royaltea Works                                 │
│                                                                             │
│                    ┌─────────────────────────────────┐                     │
│                    │                                 │                     │
│                    │    👥 → 🚀 → 💰 → 🎉          │                     │
│                    │                                 │                     │
│                    │  Team up • Build • Earn • Win   │                     │
│                    │                                 │                     │
│                    │  ▶ Watch 2-minute overview      │                     │
│                    │                                 │                     │
│                    └─────────────────────────────────┘                     │
│                                                                             │
│                    [Watch Video] [Try Interactive Demo]                    │
│                                ○ ○ ● ○                                     │
└─────────────────────────────────────────────────────────────────────────────┘
```

---

## ⚙️ **Technical Implementation**

### **State Management**
```javascript
const onboardingState = {
  currentStep: 1,
  userGoal: null, // 'project', 'work', 'learn', 'skip'
  projectType: null,
  teamChoice: null, // 'solo', 'team'
  experienceLevel: null,
  completedActions: [],
  timeStarted: timestamp,
  templates: {
    quickVenture: {...},
    quickAlliance: {...}
  }
}
```

### **Success Metrics Tracking**
- **Time to first meaningful action** (target: <5 minutes)
- **Path completion rates** by user goal
- **Template usage** vs custom setup
- **Exit points** and return rates

### **Integration Points**
- **Authentication Flow** → Onboarding Flow
- **Onboarding Flow** → Alliance Creation (if team)
- **Onboarding Flow** → Project Creation (if solo)
- **Onboarding Flow** → Mission Board (if work-seeking)
- **Onboarding Flow** → Dashboard (all paths)

---

## 🎯 **Success Criteria**

### **Primary Goals**
- ✅ **<5 minutes to first meaningful action** (PRD requirement)
- ✅ **80% completion rate** for chosen path
- ✅ **Template shortcuts** for power users
- ✅ **Graceful exit** with progress saving

### **User Experience Goals**
- **Effortless progression** - no cognitive overload
- **Clear value proposition** - understand benefits immediately
- **Immediate gratification** - see results of choices
- **Flexible paths** - accommodate different user goals

---

**This onboarding flow transforms the complex Royaltea platform into an approachable, guided experience that gets users to their first meaningful action in under 5 minutes while preserving the full power of the platform for when they're ready.**

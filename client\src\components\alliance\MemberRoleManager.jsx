import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Modal, Modal<PERSON>ontent, ModalHeader, ModalBody, ModalFooter, Input, Select, SelectItem, Textarea, Avatar, Progress } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * MemberRoleManager Component - Advanced Role Assignment and Permissions
 * 
 * Features:
 * - Comprehensive member management with detailed statistics
 * - Role-based permissions and access control
 * - Member invitation system with custom messages
 * - Real-time member activity tracking
 * - Performance metrics and contribution analytics
 */
const MemberRoleManager = ({ allianceId, userRole, className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [members, setMembers] = useState([]);
  const [statistics, setStatistics] = useState({});
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [selectedMember, setSelectedMember] = useState(null);
  
  // Invitation state
  const [inviteData, setInviteData] = useState({
    email: '',
    role: 'member',
    message: ''
  });

  // Role definitions with permissions
  const roleDefinitions = {
    founder: {
      icon: '👑',
      name: 'Founder',
      description: 'Full studio control and ownership',
      permissions: ['all'],
      color: 'warning'
    },
    owner: {
      icon: '🏰',
      name: 'Owner',
      description: 'Studio leadership and management',
      permissions: ['manage_members', 'manage_ventures', 'financial_access'],
      color: 'primary'
    },
    admin: {
      icon: '🛡️',
      name: 'Admin',
      description: 'Administrative privileges and member management',
      permissions: ['manage_members', 'manage_ventures'],
      color: 'secondary'
    },
    member: {
      icon: '⚔️',
      name: 'Member',
      description: 'Standard studio member',
      permissions: ['view_ventures', 'contribute'],
      color: 'default'
    },
    contributor: {
      icon: '🔨',
      name: 'Contributor',
      description: 'Project contributor with limited access',
      permissions: ['contribute'],
      color: 'success'
    }
  };

  useEffect(() => {
    if (allianceId && currentUser) {
      fetchMembers();
    }
  }, [allianceId, currentUser]);

  // Fetch studio members
  const fetchMembers = async () => {
    try {
      setLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      const response = await fetch(`/.netlify/functions/studio-management/${allianceId}/members`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setMembers(data.members || []);
        setStatistics(data.statistics || {});
      } else {
        throw new Error('Failed to fetch studio members');
      }
    } catch (error) {
      console.error('Error fetching members:', error);
      toast.error('Failed to load studio members');
    } finally {
      setLoading(false);
    }
  };

  // Handle member invitation
  const handleInviteMember = async () => {
    if (!inviteData.email.trim()) {
      toast.error('Email is required');
      return;
    }

    try {
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      const response = await fetch(`/.netlify/functions/studio-management/${allianceId}/invite`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(inviteData)
      });

      if (response.ok) {
        toast.success('Invitation sent successfully!');
        setShowInviteModal(false);
        setInviteData({ email: '', role: 'member', message: '' });
        await fetchMembers();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send invitation');
      }
    } catch (error) {
      console.error('Error inviting member:', error);
      toast.error(error.message || 'Failed to send invitation');
    }
  };

  // Handle role update
  const handleUpdateRole = async (newRole) => {
    if (!selectedMember) return;

    try {
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      const response = await fetch(`/.netlify/functions/studio-management/${allianceId}/members/${selectedMember.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ role: newRole })
      });

      if (response.ok) {
        toast.success('Member role updated successfully!');
        setShowRoleModal(false);
        setSelectedMember(null);
        await fetchMembers();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update role');
      }
    } catch (error) {
      console.error('Error updating role:', error);
      toast.error(error.message || 'Failed to update member role');
    }
  };

  // Check if user can manage members
  const canManageMembers = ['founder', 'owner', 'admin'].includes(userRole);

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardBody className="p-6 text-center">
          <div className="animate-spin text-2xl mb-2">🔄</div>
          <div>Loading studio members...</div>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className={`member-role-manager ${className}`}>
      <Card className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-800/20 border-2 border-blue-200 dark:border-blue-700">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">👥</span>
              <h3 className="text-lg font-semibold">Studio Members</h3>
            </div>
            <div className="flex items-center gap-2">
              <Chip color="primary" variant="flat" size="sm">
                {members.length} Members
              </Chip>
              {canManageMembers && (
                <Button
                  color="primary"
                  size="sm"
                  onPress={() => setShowInviteModal(true)}
                >
                  + Invite Member
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          {/* Studio Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <Card className="bg-white/50 dark:bg-slate-800/50">
              <CardBody className="p-3 text-center">
                <div className="text-lg font-bold text-primary">{statistics.active_members || 0}</div>
                <div className="text-xs text-default-600">Active Members</div>
              </CardBody>
            </Card>
            <Card className="bg-white/50 dark:bg-slate-800/50">
              <CardBody className="p-3 text-center">
                <div className="text-lg font-bold text-success">{statistics.total_contributions || 0}</div>
                <div className="text-xs text-default-600">Total Contributions</div>
              </CardBody>
            </Card>
            <Card className="bg-white/50 dark:bg-slate-800/50">
              <CardBody className="p-3 text-center">
                <div className="text-lg font-bold text-warning">{Math.round(statistics.total_hours || 0)}h</div>
                <div className="text-xs text-default-600">Total Hours</div>
              </CardBody>
            </Card>
            <Card className="bg-white/50 dark:bg-slate-800/50">
              <CardBody className="p-3 text-center">
                <div className="text-lg font-bold text-secondary">
                  {Object.keys(statistics.roles_distribution || {}).length}
                </div>
                <div className="text-xs text-default-600">Role Types</div>
              </CardBody>
            </Card>
          </div>

          {/* Members List */}
          <div className="space-y-3">
            {members.map((member, index) => {
              const roleInfo = roleDefinitions[member.role] || roleDefinitions.member;
              
              return (
                <motion.div
                  key={member.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <Card className="border border-default-200 hover:border-primary-300 transition-colors">
                    <CardBody className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <Avatar 
                            src={member.users?.avatar_url}
                            name={member.users?.display_name || member.users?.email}
                            size="md"
                          />
                          <div>
                            <div className="font-semibold">
                              {member.users?.display_name || member.users?.email}
                            </div>
                            <div className="text-sm text-default-600">
                              {member.users?.email}
                            </div>
                            <div className="text-xs text-default-500">
                              Joined {formatDate(member.joined_at)}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-4">
                          {/* Member Statistics */}
                          <div className="text-right text-sm">
                            <div className="font-medium">
                              {member.statistics?.total_contributions || 0} contributions
                            </div>
                            <div className="text-default-600">
                              {Math.round(member.statistics?.total_hours || 0)}h • 
                              {Math.round(member.statistics?.validation_rate || 0)}% validated
                            </div>
                          </div>

                          {/* Role Badge */}
                          <Chip 
                            color={roleInfo.color} 
                            variant="flat" 
                            size="sm"
                            startContent={<span>{roleInfo.icon}</span>}
                          >
                            {roleInfo.name}
                          </Chip>

                          {/* Actions */}
                          {canManageMembers && member.user_id !== currentUser.id && (
                            <Button
                              size="sm"
                              variant="bordered"
                              onPress={() => {
                                setSelectedMember(member);
                                setShowRoleModal(true);
                              }}
                            >
                              Edit Role
                            </Button>
                          )}
                        </div>
                      </div>

                      {/* Performance Progress */}
                      {member.statistics && (
                        <div className="mt-3 pt-3 border-t border-default-200">
                          <div className="grid grid-cols-3 gap-4 text-xs">
                            <div>
                              <div className="text-default-500 mb-1">Activity Level</div>
                              <Progress 
                                value={Math.min((member.statistics.total_contributions / 10) * 100, 100)} 
                                color="primary" 
                                size="sm"
                              />
                            </div>
                            <div>
                              <div className="text-default-500 mb-1">Quality Score</div>
                              <Progress 
                                value={member.statistics.validation_rate || 0} 
                                color="success" 
                                size="sm"
                              />
                            </div>
                            <div>
                              <div className="text-default-500 mb-1">Difficulty Level</div>
                              <Progress 
                                value={(member.statistics.average_difficulty || 0) * 20} 
                                color="warning" 
                                size="sm"
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </CardBody>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </CardBody>
      </Card>

      {/* Invite Member Modal */}
      <Modal isOpen={showInviteModal} onClose={() => setShowInviteModal(false)}>
        <ModalContent>
          <ModalHeader>
            <span className="text-xl">👥 Invite Studio Member</span>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Input
                label="Email Address"
                placeholder="<EMAIL>"
                value={inviteData.email}
                onChange={(e) => setInviteData(prev => ({ ...prev, email: e.target.value }))}
                type="email"
              />
              
              <Select
                label="Role"
                selectedKeys={[inviteData.role]}
                onSelectionChange={(keys) => setInviteData(prev => ({ 
                  ...prev, 
                  role: Array.from(keys)[0] 
                }))}
              >
                {Object.entries(roleDefinitions)
                  .filter(([key]) => key !== 'founder') // Can't invite as founder
                  .map(([key, role]) => (
                    <SelectItem key={key} startContent={<span>{role.icon}</span>}>
                      {role.name} - {role.description}
                    </SelectItem>
                  ))}
              </Select>

              <Textarea
                label="Personal Message (Optional)"
                placeholder="Welcome to our studio! We're excited to have you join our team..."
                value={inviteData.message}
                onChange={(e) => setInviteData(prev => ({ ...prev, message: e.target.value }))}
                minRows={3}
              />
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={() => setShowInviteModal(false)}>
              Cancel
            </Button>
            <Button 
              color="primary" 
              onPress={handleInviteMember}
              isDisabled={!inviteData.email.trim()}
            >
              Send Invitation
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Role Update Modal */}
      <Modal isOpen={showRoleModal} onClose={() => setShowRoleModal(false)}>
        <ModalContent>
          <ModalHeader>
            <span className="text-xl">🛡️ Update Member Role</span>
          </ModalHeader>
          <ModalBody>
            {selectedMember && (
              <div className="space-y-4">
                <div className="text-center">
                  <Avatar 
                    src={selectedMember.users?.avatar_url}
                    name={selectedMember.users?.display_name || selectedMember.users?.email}
                    size="lg"
                    className="mb-2"
                  />
                  <h3 className="font-semibold">
                    {selectedMember.users?.display_name || selectedMember.users?.email}
                  </h3>
                  <p className="text-sm text-default-600">
                    Current role: {roleDefinitions[selectedMember.role]?.name}
                  </p>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Select New Role:</h4>
                  {Object.entries(roleDefinitions)
                    .filter(([key]) => key !== 'founder') // Can't assign founder role
                    .map(([key, role]) => (
                      <Card 
                        key={key}
                        isPressable
                        onPress={() => handleUpdateRole(key)}
                        className={`cursor-pointer transition-all ${
                          selectedMember.role === key 
                            ? 'border-2 border-primary bg-primary-50' 
                            : 'border border-default-200 hover:border-primary-300'
                        }`}
                      >
                        <CardBody className="p-3">
                          <div className="flex items-center gap-3">
                            <span className="text-xl">{role.icon}</span>
                            <div>
                              <div className="font-medium">{role.name}</div>
                              <div className="text-sm text-default-600">{role.description}</div>
                            </div>
                          </div>
                        </CardBody>
                      </Card>
                    ))}
                </div>
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={() => setShowRoleModal(false)}>
              Cancel
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default MemberRoleManager;

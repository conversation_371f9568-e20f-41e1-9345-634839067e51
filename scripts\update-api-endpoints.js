#!/usr/bin/env node

/**
 * Update API Endpoints Script
 * Updates all API endpoint references from old terminology to new terminology
 * Alliance → Studio, Venture → Project, Quest → Mission
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define the terminology mappings
const TERMINOLOGY_MAPPINGS = {
  // API endpoints
  '/api/alliances': '/api/studios',
  '/api/ventures': '/api/projects', 
  '/api/quest-system': '/api/missions',
  
  // Function names
  'getAlliances': 'getStudios',
  'getAlliance': 'getStudio',
  'createAlliance': 'createStudio',
  'updateAlliance': 'updateStudio',
  'deleteAlliance': 'deleteStudio',
  'getVentures': 'getProjects',
  'getVenture': 'getProject',
  'createVenture': 'createProject',
  'updateVenture': 'updateProject',
  'deleteVenture': 'deleteProject',
  'getQuests': 'getMissions',
  'getQuest': 'getMission',
  'startQuest': 'startMission',
  'completeQuest': 'completeMission',
  
  // Database references
  'alliance_id': 'studio_id',
  'venture_id': 'project_id',
  'quest_id': 'mission_id',
  'alliance_type': 'studio_type',
  'venture_type': 'project_type',
  'quest_type': 'mission_type',
  'quest_requirements': 'mission_requirements',
  'quest_rewards': 'mission_rewards',
  'alliance_invitations': 'studio_invitations',
  'alliance_preferences': 'studio_preferences',
  'user_quests': 'user_missions',
  
  // UI terminology
  'Alliance': 'Studio',
  'Venture': 'Project', 
  'Quest': 'Mission',
  'alliance': 'studio',
  'venture': 'project',
  'quest': 'mission',
  'alliances': 'studios',
  'ventures': 'projects',
  'quests': 'missions'
};

// Files to exclude from updates (to avoid breaking existing functionality)
const EXCLUDE_FILES = [
  'node_modules',
  '.git',
  'package-lock.json',
  'yarn.lock',
  '.env',
  'README.md',
  'CHANGELOG.md'
];

// File extensions to process
const INCLUDE_EXTENSIONS = ['.js', '.jsx', '.ts', '.tsx', '.json', '.md', '.sql'];

/**
 * Check if file should be processed
 */
function shouldProcessFile(filePath) {
  // Check if file is in exclude list
  if (EXCLUDE_FILES.some(exclude => filePath.includes(exclude))) {
    return false;
  }
  
  // Check file extension
  const ext = path.extname(filePath);
  return INCLUDE_EXTENSIONS.includes(ext);
}

/**
 * Update terminology in file content
 */
function updateFileContent(content, filePath) {
  let updatedContent = content;
  let changeCount = 0;
  
  // Apply terminology mappings
  for (const [oldTerm, newTerm] of Object.entries(TERMINOLOGY_MAPPINGS)) {
    const regex = new RegExp(oldTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
    const matches = updatedContent.match(regex);
    if (matches) {
      updatedContent = updatedContent.replace(regex, newTerm);
      changeCount += matches.length;
    }
  }
  
  return { content: updatedContent, changeCount };
}

/**
 * Process a single file
 */
async function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const { content: updatedContent, changeCount } = updateFileContent(content, filePath);
    
    if (changeCount > 0) {
      fs.writeFileSync(filePath, updatedContent, 'utf8');
      console.log(`✅ Updated ${filePath} (${changeCount} changes)`);
      return { file: filePath, changes: changeCount };
    }
    
    return null;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return null;
  }
}

/**
 * Process directory recursively
 */
async function processDirectory(dirPath, results = []) {
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory()) {
      await processDirectory(itemPath, results);
    } else if (stat.isFile() && shouldProcessFile(itemPath)) {
      const result = await processFile(itemPath);
      if (result) {
        results.push(result);
      }
    }
  }
  
  return results;
}

/**
 * Main execution function
 */
async function main() {
  console.log('🔄 Starting API Endpoint Terminology Update...');
  console.log('📝 Alliance → Studio, Venture → Project, Quest → Mission\n');
  
  const projectRoot = path.resolve(__dirname, '..');
  const startTime = Date.now();
  
  // Directories to process
  const directoriesToProcess = [
    path.join(projectRoot, 'client/src'),
    path.join(projectRoot, 'netlify/functions'),
    path.join(projectRoot, 'scripts'),
    path.join(projectRoot, 'tests'),
    path.join(projectRoot, 'docs')
  ];
  
  let totalResults = [];
  
  for (const dir of directoriesToProcess) {
    if (fs.existsSync(dir)) {
      console.log(`📁 Processing ${path.relative(projectRoot, dir)}...`);
      const results = await processDirectory(dir);
      totalResults = totalResults.concat(results);
    } else {
      console.log(`⚠️ Directory not found: ${dir}`);
    }
  }
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  console.log('\n🎉 API Endpoint Update Complete!');
  console.log(`⏱️ Duration: ${duration} seconds`);
  console.log(`📊 Files updated: ${totalResults.length}`);
  console.log(`🔧 Total changes: ${totalResults.reduce((sum, r) => sum + r.changes, 0)}`);
  
  if (totalResults.length > 0) {
    console.log('\n📋 Updated files:');
    totalResults.forEach(result => {
      console.log(`   • ${result.file} (${result.changes} changes)`);
    });
  }
  
  console.log('\n⚠️ Important Notes:');
  console.log('   • Test all functionality after these changes');
  console.log('   • Update any hardcoded URLs in configuration files');
  console.log('   • Check for any missed references in comments or documentation');
  console.log('   • Verify API endpoint routing is working correctly');
}

// Handle command line execution
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { updateFileContent, processFile, processDirectory };

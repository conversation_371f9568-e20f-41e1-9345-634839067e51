/**
 * Comprehensive Legal Agreement Test
 * 
 * This test validates ALL aspects of the legal agreement generation system:
 * 1. Complete 1:1 recreation of lawyer-approved template (99% accuracy target)
 * 2. All financial/revenue sharing sections (SCHEDULE B)
 * 3. All legal definitions and clauses
 * 4. All exhibits and specifications
 * 5. Variable-based generation for multiple business types
 * 6. Cross-contamination prevention
 * 7. Complete structural integrity
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🎯 COMPREHENSIVE LEGAL AGREEMENT TEST');
console.log('=' .repeat(60));
console.log('Complete analysis of ALL agreement components including financial terms');
console.log('Target: 99% accuracy for lawyer-approved template recreation');
console.log('=' .repeat(60));

/**
 * Complete test configuration
 */
const TEST_CONFIG = {
  MINIMUM_ACCURACY: 99,
  ZERO_CONTAMINATION_REQUIRED: true,
  COMPREHENSIVE_VALIDATION: true
};

/**
 * ALL critical elements that must be present for complete legal agreement
 */
const COMPREHENSIVE_CRITICAL_ELEMENTS = {
  // Header and Basic Structure
  HEADER: [
    'CITY OF GAMERS INC.',
    'CONTRIBUTOR AGREEMENT',
    'This Contributor Agreement (this "Agreement")',
    'Florida corporation',
    '1205 43rd Street, Suite B, Orlando, Florida 32839'
  ],

  // Legal Definitions (Section 1)
  DEFINITIONS: [
    'Background IP',
    'Confidential Documents',
    'Confidential Information',
    'Contribution',
    'Developed IP',
    'Governmental Authority',
    'Launch',
    'Milestones',
    'Person',
    'Programs',
    'Specification',
    'Termination Date',
    'Work Product',
    'Work Product Management',
    'Revenue Tranch',
    'Contribution Points'
  ],

  // Project-Specific Content (VOTA)
  PROJECT_CONTENT: [
    'Village of The Ages',
    'village simulation game',
    'historical progressions',
    'resource-based challenges',
    'Village Building & Management',
    'Historical Progression',
    'Resource Management',
    'Interface Requirements',
    'Technology tree advancement',
    'Architectural evolution',
    'Dynamic weather systems',
    'Natural disasters and seasonal challenges',
    'Trading systems with neighboring villages',
    'Resource scarcity mechanics',
    'Intuitive building placement system',
    'Resource management dashboard',
    'Population statistics and management panels',
    'Technology and progression trackers'
  ],

  // Financial/Revenue Sections (SCHEDULE B)
  FINANCIAL_TERMS: [
    'SCHEDULE B',
    'Description of Consideration',
    'Contribution Points',
    'Revenue Tranch',
    'Revenue',
    'Revenue Share Percentage',
    'Minimum Threshold for Payout',
    'Maximum Individual Payment',
    'Payment Schedule',
    'Quarterly reports',
    'Revenue Tranch Parameters',
    'Contribution Point System',
    'Variable Costs',
    'Payments on Termination',
    'Sequel Rights',
    'Audit Rights'
  ],

  // Specific Financial Details
  FINANCIAL_DETAILS: [
    '33% of post-expense Revenue',
    '$100,000 in post-expense Revenue',
    '$1,000,000 per Developer',
    'quarterly within 45 days',
    'Platform fees',
    'Payment processing fees',
    'Marketing expenses',
    'Third-party licensing fees'
  ],

  // Legal Clauses
  LEGAL_CLAUSES: [
    'Treatment of Confidential Information',
    'Ownership of Work Product',
    'Non-Disparagement',
    'Termination',
    'Equitable Remedies',
    'Assignment',
    'Waivers and Amendments',
    'Survival',
    'Status as Independent Contractor',
    'Representations and Warranties',
    'Indemnification',
    'Entire Agreement',
    'Governing Law',
    'Consent to Jurisdiction',
    'Settlement of Disputes'
  ],

  // Exhibits and Schedules
  EXHIBITS: [
    'SCHEDULE A',
    'Description of Services',
    'EXHIBIT I',
    'SPECIFICATIONS',
    'EXHIBIT II',
    'PRODUCT ROADMAP'
  ],

  // Milestones and Roadmap
  MILESTONES: [
    'Core Gameplay Development',
    'Resource Management System',
    'Historical Progression Features',
    'Basic village layout and building system',
    'Implementation of resource scarcity mechanics',
    'Time-based progression and historical events'
  ],

  // Technical Requirements
  TECHNICAL: [
    'Platform: PC (Steam, Epic Games Store)',
    'Engine: Unreal Engine 5',
    'Minimum Specs: Standard hardware requirements',
    'Art Style: Stylized, readable visuals',
    'Audio: Atmospheric soundtrack',
    'Version Control: Git-based source control'
  ]
};

/**
 * Test scenarios for different business types
 */
const BUSINESS_SCENARIOS = {
  VOTA_EXACT: {
    name: 'Village of The Ages',
    description: 'A village simulation game where players guide communities through historical progressions and manage resource-based challenges',
    project_type: 'game',
    company_name: 'City of Gamers Inc.',
    address: '1205 43rd Street, Suite B, Orlando, Florida 32839',
    contact_email: '<EMAIL>',
    revenueShare: 33,
    payoutThreshold: 100000,
    maxPayment: 1000000,
    features: 'The game features dynamic challenges based on resource scarcity and natural disasters, creating an engaging gameplay loop.',
    coreFeatures: `1. **Village Building & Management**
   - Resource gathering and management
   - Building placement and construction
   - Population growth and management
   - Cultural evolution systems

2. **Historical Progression**
   - Players guide their community through multiple historical eras
   - Technology tree advancement
   - Cultural and social development
   - Architectural evolution

3. **Resource Management**
   - Dynamic weather systems affecting resources
   - Natural disasters and seasonal challenges
   - Trading systems with neighboring villages
   - Resource scarcity mechanics

4. **Interface Requirements**
   - Intuitive building placement system
   - Resource management dashboard
   - Population statistics and management panels
   - Technology and progression trackers`,
    technicalRequirements: `- Platform: PC (Steam, Epic Games Store)
- Engine: Unreal Engine 5
- Minimum Specs: Standard hardware requirements for the target platforms
- Art Style: Stylized, readable visuals with distinctive aesthetics
- Audio: Atmospheric soundtrack with appropriate sound design
- Version Control: Git-based source control with proper branching strategy`,
    roadmapPhases: `**Phase 1: Core Gameplay Development (Months 1-2)**
- Basic village layout and building system
- Core resource gathering mechanics
- Initial AI for villagers
- Basic UI framework
- First playable prototype with one historical era

**Phase 2: Feature Expansion (Months 2-3)**
- Additional historical eras
- Enhanced resource management systems
- Weather and disaster systems
- Trading mechanics
- Technology progression system

**Phase 3: Polish and Enhancement (Month 4)**
- UI refinement
- Performance optimization
- Additional content (buildings, resources, etc.)
- Balancing and gameplay tuning
- Audio implementation`,
    milestones: [
      {
        title: 'Core Gameplay Development',
        description: 'Basic village layout and building system',
        dueDate: 'Months 1-2'
      },
      {
        title: 'Resource Management System',
        description: 'Implementation of resource scarcity mechanics',
        dueDate: 'Months 3-4'
      },
      {
        title: 'Historical Progression Features',
        description: 'Time-based progression and historical events, architectural evolution through eras',
        dueDate: 'Months 5-6'
      }
    ]
  },

  SOFTWARE_SAAS: {
    name: 'CloudFlow Enterprise',
    description: 'A comprehensive enterprise workflow automation platform for large organizations',
    project_type: 'software',
    company_name: 'Enterprise Solutions Corp',
    address: '500 Corporate Plaza, San Francisco, CA 94105',
    contact_email: '<EMAIL>',
    revenueShare: 25,
    payoutThreshold: 250000,
    maxPayment: 2000000,
    features: 'Advanced enterprise-grade workflow automation with AI-powered process optimization and compliance tracking.',
    coreFeatures: `1. **Workflow Automation**
   - Visual workflow designer
   - Automated task routing
   - Conditional logic processing
   - Integration with enterprise systems

2. **AI-Powered Optimization**
   - Process efficiency analysis
   - Bottleneck identification
   - Predictive resource allocation
   - Performance recommendations

3. **Compliance & Security**
   - Audit trail management
   - Role-based access control
   - Data encryption and privacy
   - Regulatory compliance reporting`,
    technicalRequirements: `- Platform: Cloud-native SaaS (AWS/Azure)
- Architecture: Microservices with Kubernetes
- Database: PostgreSQL with Redis caching
- AI/ML: TensorFlow and custom ML models
- Security: SOC 2 Type II compliance
- API: RESTful and GraphQL endpoints`,
    roadmapPhases: `**Phase 1: Core Platform (Months 1-4)**
- User management and authentication
- Basic workflow designer
- Core automation engine
- Initial integrations

**Phase 2: AI Integration (Months 5-8)**
- Machine learning model development
- Process optimization algorithms
- Predictive analytics dashboard
- Advanced reporting features

**Phase 3: Enterprise Features (Months 9-12)**
- Advanced security features
- Compliance reporting
- Enterprise integrations
- Performance optimization`,
    milestones: [
      {
        title: 'Core Platform MVP',
        description: 'Basic workflow automation and user management',
        dueDate: 'Month 4'
      },
      {
        title: 'AI-Powered Features',
        description: 'Machine learning integration and process optimization',
        dueDate: 'Month 8'
      },
      {
        title: 'Enterprise Ready',
        description: 'Full compliance, security, and enterprise integrations',
        dueDate: 'Month 12'
      }
    ]
  }
};

/**
 * Enhanced template processor with financial sections
 */
function processCompleteTemplate(template, data) {
  let processed = template;
  
  // Replace basic project information
  processed = processed.replace(/\[Project Name\]/g, data.name || '[Project Name]');
  processed = processed.replace(/\[Project Description\]/g, data.description || '[Project Description]');
  processed = processed.replace(/\[Project Features\]/g, data.features || '[Project Features]');
  processed = processed.replace(/\[Project Core Features\]/g, data.coreFeatures || '[Project Core Features]');
  processed = processed.replace(/\[Project Technical Requirements\]/g, data.technicalRequirements || '[Project Technical Requirements]');
  processed = processed.replace(/\[Project Roadmap Phases\]/g, data.roadmapPhases || '[Project Roadmap Phases]');
  
  // Replace financial information with new variable placeholders
  if (data.revenueShare) {
    processed = processed.replace(/\[Revenue Share\]/g, data.revenueShare);
  }
  if (data.payoutThreshold) {
    processed = processed.replace(/\[Payout Threshold\]/g, data.payoutThreshold.toLocaleString());
  }
  if (data.maxPayment) {
    processed = processed.replace(/\[Max Payment\]/g, data.maxPayment.toLocaleString());
  }

  // Also handle legacy format for backward compatibility
  if (data.revenueShare) {
    processed = processed.replace(/33% of post-expense Revenue/g, `${data.revenueShare}% of post-expense Revenue`);
  }
  if (data.payoutThreshold) {
    processed = processed.replace(/\$100,000 in post-expense Revenue/g, `$${data.payoutThreshold.toLocaleString()} in post-expense Revenue`);
  }
  if (data.maxPayment) {
    processed = processed.replace(/\$1,000,000 per Developer/g, `$${data.maxPayment.toLocaleString()} per Developer`);
  }
  
  // Replace company information only if different from default
  if (data.company_name && data.company_name !== 'City of Gamers Inc.') {
    processed = processed.replace(/City of Gamers Inc\./g, data.company_name);
  }
  
  if (data.address && data.address !== '1205 43rd Street, Suite B, Orlando, Florida 32839') {
    processed = processed.replace(/1205 43rd Street, Suite B, Orlando, Florida 32839/g, data.address);
  }
  
  if (data.contact_email && data.contact_email !== '<EMAIL>') {
    processed = processed.replace(/billing@cogfuture\.com/g, data.contact_email);
  }
  
  // Format milestones
  if (data.milestones && data.milestones.length > 0) {
    const formattedMilestones = data.milestones.map((milestone, index) => {
      const title = milestone.title || `Milestone ${index + 1}`;
      const description = milestone.description || '';
      const dueDate = milestone.dueDate || '';

      let formatted = `${index + 1}. **${title}**`;
      if (dueDate) formatted += ` (${dueDate})`;
      if (description) formatted += `\n   - ${description}`;

      return formatted;
    }).join('\n\n');
    
    processed = processed.replace(/\[Project Milestones\]/g, formattedMilestones);
  }
  
  return processed;
}

/**
 * Comprehensive validation of all agreement components
 */
function validateComprehensiveAgreement(lawyerTemplate, generatedAgreement, scenario) {
  const results = {
    categories: {},
    overallAccuracy: 0,
    totalElements: 0,
    foundElements: 0,
    missingElements: [],
    criticalIssues: []
  };

  // Validate each category
  for (const [categoryName, elements] of Object.entries(COMPREHENSIVE_CRITICAL_ELEMENTS)) {
    const categoryResult = {
      found: [],
      missing: [],
      accuracy: 0
    };

    for (const element of elements) {
      if (generatedAgreement.includes(element)) {
        categoryResult.found.push(element);
        results.foundElements++;
      } else {
        categoryResult.missing.push(element);
        results.missingElements.push(`${categoryName}: ${element}`);

        // Mark as critical issue if it's a financial or legal term
        if (categoryName === 'FINANCIAL_TERMS' || categoryName === 'FINANCIAL_DETAILS' || categoryName === 'LEGAL_CLAUSES') {
          results.criticalIssues.push(`Missing critical ${categoryName.toLowerCase()}: ${element}`);
        }
      }
      results.totalElements++;
    }

    categoryResult.accuracy = Math.round((categoryResult.found.length / elements.length) * 100);
    results.categories[categoryName] = categoryResult;
  }

  // Calculate overall accuracy
  results.overallAccuracy = Math.round((results.foundElements / results.totalElements) * 100);

  // Additional validation for financial consistency
  results.financialConsistency = validateFinancialConsistency(generatedAgreement, scenario);

  return results;
}

/**
 * Validate financial consistency and completeness
 */
function validateFinancialConsistency(agreement, scenario) {
  const issues = [];

  // Check if revenue share percentage is consistent
  if (scenario.revenueShare) {
    const expectedRevShare = `${scenario.revenueShare}% of post-expense Revenue`;
    if (!agreement.includes(expectedRevShare)) {
      issues.push(`Revenue share should be ${expectedRevShare}`);
    }
  }

  // Check if payout threshold is consistent
  if (scenario.payoutThreshold) {
    const expectedThreshold = `$${scenario.payoutThreshold.toLocaleString()} in post-expense Revenue`;
    if (!agreement.includes(expectedThreshold)) {
      issues.push(`Payout threshold should be ${expectedThreshold}`);
    }
  }

  // Check if maximum payment is consistent
  if (scenario.maxPayment) {
    const expectedMaxPayment = `$${scenario.maxPayment.toLocaleString()} per Developer`;
    if (!agreement.includes(expectedMaxPayment)) {
      issues.push(`Maximum payment should be ${expectedMaxPayment}`);
    }
  }

  // Check for presence of key financial sections
  const requiredFinancialSections = [
    'SCHEDULE B',
    'Revenue Tranch Parameters',
    'Contribution Point System',
    'Payment Schedule',
    'Audit Rights'
  ];

  for (const section of requiredFinancialSections) {
    if (!agreement.includes(section)) {
      issues.push(`Missing required financial section: ${section}`);
    }
  }

  return {
    isConsistent: issues.length === 0,
    issues: issues
  };
}

/**
 * Check for cross-contamination between different business scenarios
 */
function checkComprehensiveCrossContamination(agreements) {
  const contaminationDetails = [];

  // Define unique business identifiers
  const businessIdentifiers = {
    VOTA_EXACT: [
      'Village of The Ages',
      'village simulation',
      'historical progressions',
      'architectural evolution',
      'City of Gamers Inc.',
      'Orlando, Florida'
    ],
    SOFTWARE_SAAS: [
      'CloudFlow Enterprise',
      'Enterprise Solutions Corp',
      'workflow automation',
      'San Francisco, CA',
      'enterprise-grade',
      'microservices'
    ]
  };

  // Check each agreement against others
  for (const [sourceScenario, sourceAgreement] of Object.entries(agreements)) {
    for (const [targetScenario, targetIdentifiers] of Object.entries(businessIdentifiers)) {
      if (sourceScenario !== targetScenario) {
        for (const identifier of targetIdentifiers) {
          if (sourceAgreement.includes(identifier)) {
            contaminationDetails.push({
              source: sourceScenario,
              target: targetScenario,
              content: identifier,
              severity: identifier.includes('Inc.') || identifier.includes('Corp') ? 'HIGH' : 'MEDIUM'
            });
          }
        }
      }
    }
  }

  return {
    totalContamination: contaminationDetails.length,
    highSeverityCount: contaminationDetails.filter(d => d.severity === 'HIGH').length,
    details: contaminationDetails
  };
}

/**
 * Generate comprehensive analysis report
 */
function generateComprehensiveReport(results, outputDir) {
  const report = {
    timestamp: new Date().toISOString(),
    testType: 'Comprehensive Legal Agreement Analysis',
    summary: {
      overallScore: 0,
      criticalIssuesCount: 0,
      financialAccuracy: 0,
      crossContaminationScore: 0,
      productionReady: false
    },
    detailed: results,
    recommendations: []
  };

  // Calculate summary scores
  let totalAccuracy = 0;
  let scenarioCount = 0;
  let totalCriticalIssues = 0;
  let totalFinancialIssues = 0;

  for (const [scenarioName, scenarioResult] of Object.entries(results.scenarios || {})) {
    totalAccuracy += scenarioResult.overallAccuracy || 0;
    totalCriticalIssues += scenarioResult.criticalIssues?.length || 0;
    totalFinancialIssues += scenarioResult.financialConsistency?.issues?.length || 0;
    scenarioCount++;
  }

  report.summary.overallScore = scenarioCount > 0 ? Math.round(totalAccuracy / scenarioCount) : 0;
  report.summary.criticalIssuesCount = totalCriticalIssues;
  report.summary.financialAccuracy = totalFinancialIssues === 0 ? 100 : Math.max(0, 100 - (totalFinancialIssues * 10));
  report.summary.crossContaminationScore = (results.crossContamination?.totalContamination || 0) === 0 ? 100 : 0;

  // Determine if production ready
  report.summary.productionReady = (
    report.summary.overallScore >= TEST_CONFIG.MINIMUM_ACCURACY &&
    report.summary.criticalIssuesCount === 0 &&
    report.summary.financialAccuracy >= 95 &&
    report.summary.crossContaminationScore === 100
  );

  // Generate recommendations
  if (report.summary.overallScore < TEST_CONFIG.MINIMUM_ACCURACY) {
    report.recommendations.push({
      priority: 'CRITICAL',
      category: 'Overall Accuracy',
      issue: `Overall accuracy is ${report.summary.overallScore}%, below required ${TEST_CONFIG.MINIMUM_ACCURACY}%`,
      action: 'Review template variable mapping and ensure all critical sections are properly substituted'
    });
  }

  if (report.summary.criticalIssuesCount > 0) {
    report.recommendations.push({
      priority: 'CRITICAL',
      category: 'Critical Elements',
      issue: `${report.summary.criticalIssuesCount} critical elements missing`,
      action: 'Implement missing legal clauses and financial terms'
    });
  }

  if (report.summary.financialAccuracy < 95) {
    report.recommendations.push({
      priority: 'HIGH',
      category: 'Financial Terms',
      issue: `Financial accuracy is ${report.summary.financialAccuracy}%`,
      action: 'Fix financial term substitution and ensure all revenue sharing details are accurate'
    });
  }

  if (report.summary.crossContaminationScore < 100) {
    report.recommendations.push({
      priority: 'HIGH',
      category: 'Cross-Contamination',
      issue: `${results.crossContamination?.totalContamination || 0} contamination instances detected`,
      action: 'Implement proper variable isolation to prevent content leakage between agreements'
    });
  }

  // Save report
  const reportPath = path.join(outputDir, 'comprehensive-legal-analysis-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

  return report;
}

/**
 * Main comprehensive test execution
 */
async function runComprehensiveLegalTest() {
  console.log('\n🚀 Starting comprehensive legal agreement analysis...\n');

  try {
    // Create output directory
    const outputDir = path.join(__dirname, 'comprehensive-legal-results');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Load templates
    console.log('📋 Loading templates...');
    const lawyerTemplatePath = path.join(__dirname, '../client/public/example-cog-contributor-agreement.md');
    const variableTemplatePath = path.join(__dirname, '../client/public/contributor-agreement-template.md');

    const lawyerTemplate = fs.readFileSync(lawyerTemplatePath, 'utf8');
    const variableTemplate = fs.readFileSync(variableTemplatePath, 'utf8');

    const results = {
      scenarios: {},
      crossContamination: null
    };

    const agreements = {};

    // Test each business scenario
    console.log('\n📊 Testing business scenarios...');
    for (const [scenarioName, scenarioData] of Object.entries(BUSINESS_SCENARIOS)) {
      console.log(`\n  🔍 Testing: ${scenarioName}`);

      // Generate agreement
      const generatedAgreement = processCompleteTemplate(variableTemplate, scenarioData);
      agreements[scenarioName] = generatedAgreement;

      // Save generated agreement
      const outputPath = path.join(outputDir, `${scenarioName.toLowerCase()}-agreement.md`);
      fs.writeFileSync(outputPath, generatedAgreement);

      // Comprehensive validation
      const validation = validateComprehensiveAgreement(lawyerTemplate, generatedAgreement, scenarioData);

      results.scenarios[scenarioName] = {
        ...validation,
        outputFile: outputPath,
        scenarioData: scenarioData
      };

      // Display category results
      console.log(`    📈 Overall Accuracy: ${validation.overallAccuracy}%`);
      console.log(`    🏛️  Legal Clauses: ${validation.categories.LEGAL_CLAUSES?.accuracy || 0}%`);
      console.log(`    💰 Financial Terms: ${validation.categories.FINANCIAL_TERMS?.accuracy || 0}%`);
      console.log(`    📋 Definitions: ${validation.categories.DEFINITIONS?.accuracy || 0}%`);
      console.log(`    🎯 Project Content: ${validation.categories.PROJECT_CONTENT?.accuracy || 0}%`);

      if (validation.criticalIssues.length > 0) {
        console.log(`    ⚠️  Critical Issues: ${validation.criticalIssues.length}`);
        validation.criticalIssues.slice(0, 3).forEach(issue => {
          console.log(`       • ${issue}`);
        });
      }

      if (!validation.financialConsistency.isConsistent) {
        console.log(`    💸 Financial Issues: ${validation.financialConsistency.issues.length}`);
        validation.financialConsistency.issues.slice(0, 2).forEach(issue => {
          console.log(`       • ${issue}`);
        });
      }
    }

    // Check cross-contamination
    console.log('\n🚫 Checking cross-contamination...');
    results.crossContamination = checkComprehensiveCrossContamination(agreements);

    console.log(`   Total contamination instances: ${results.crossContamination.totalContamination}`);
    console.log(`   High severity instances: ${results.crossContamination.highSeverityCount}`);

    if (results.crossContamination.details.length > 0) {
      console.log('   Sample contamination issues:');
      results.crossContamination.details.slice(0, 3).forEach(detail => {
        console.log(`     ${detail.severity}: ${detail.source} → ${detail.target}: "${detail.content}"`);
      });
    }

    // Generate comprehensive report
    console.log('\n📊 Generating comprehensive analysis report...');
    const report = generateComprehensiveReport(results, outputDir);

    // Display final results
    console.log('\n🎯 COMPREHENSIVE LEGAL AGREEMENT ANALYSIS RESULTS');
    console.log('=' .repeat(60));
    console.log(`📈 Overall Score: ${report.summary.overallScore}%`);
    console.log(`🏛️  Legal Accuracy: ${report.summary.overallScore}%`);
    console.log(`💰 Financial Accuracy: ${report.summary.financialAccuracy}%`);
    console.log(`🚫 Cross-Contamination Score: ${report.summary.crossContaminationScore}%`);
    console.log(`⚠️  Critical Issues: ${report.summary.criticalIssuesCount}`);
    console.log(`🏭 Production Ready: ${report.summary.productionReady ? 'YES' : 'NO'}`);

    // Display recommendations
    if (report.recommendations.length > 0) {
      console.log('\n📋 CRITICAL RECOMMENDATIONS');
      console.log('=' .repeat(40));
      report.recommendations.forEach((rec, index) => {
        console.log(`\n${index + 1}. [${rec.priority}] ${rec.category}`);
        console.log(`   Issue: ${rec.issue}`);
        console.log(`   Action: ${rec.action}`);
      });
    }

    // Final assessment
    console.log('\n🎯 FINAL ASSESSMENT');
    console.log('=' .repeat(30));

    if (report.summary.productionReady) {
      console.log('🎉 EXCELLENT: System meets ALL production requirements!');
      console.log('✅ 99%+ accuracy for lawyer-approved template recreation');
      console.log('✅ All financial terms properly implemented');
      console.log('✅ All legal clauses present and accurate');
      console.log('✅ No cross-contamination detected');
      console.log('✅ Ready for production legal agreement generation');
    } else if (report.summary.overallScore >= 90) {
      console.log('👍 GOOD: System mostly works but needs critical improvements');
      console.log(`🔧 Need ${TEST_CONFIG.MINIMUM_ACCURACY - report.summary.overallScore}% accuracy improvement`);

      if (report.summary.financialAccuracy < 95) {
        console.log('❌ Financial terms need attention');
      }
      if (report.summary.criticalIssuesCount > 0) {
        console.log('❌ Critical legal elements missing');
      }
      if (report.summary.crossContaminationScore < 100) {
        console.log('❌ Cross-contamination issues detected');
      }
    } else {
      console.log('❌ POOR: System has significant legal compliance issues');
      console.log(`🔧 Need ${TEST_CONFIG.MINIMUM_ACCURACY - report.summary.overallScore}% improvement`);
      console.log('⚠️  NOT SAFE for production legal agreement generation');
      console.log('⚠️  Legal review required before any use');
    }

    console.log(`\n📊 Detailed report: ${path.join(outputDir, 'comprehensive-legal-analysis-report.json')}`);
    console.log(`📁 Generated agreements: ${outputDir}/`);

    return {
      success: report.summary.productionReady,
      report: report,
      results: results
    };

  } catch (error) {
    console.error('\n❌ Comprehensive legal test failed:', error);
    throw error;
  }
}

// Run the comprehensive test
runComprehensiveLegalTest()
  .then(result => {
    console.log(`\n${result.success ? '✅' : '❌'} Comprehensive legal test ${result.success ? 'PASSED' : 'FAILED'}`);
    console.log(`📊 Overall accuracy: ${result.report.summary.overallScore}%`);
    console.log(`💰 Financial accuracy: ${result.report.summary.financialAccuracy}%`);
    process.exit(result.success ? 0 : 1);
  })
  .catch(error => {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  });

export { runComprehensiveLegalTest, COMPREHENSIVE_CRITICAL_ELEMENTS, BUSINESS_SCENARIOS };

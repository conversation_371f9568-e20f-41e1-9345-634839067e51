#!/usr/bin/env node

/**
 * User Flow Agreement Generation Test
 * 
 * Simulates the actual user flow for agreement generation using the rebuilt V2 template
 * and compares the output to the lawyer-approved example agreement.
 */

import fs from 'fs';
import path from 'path';

class UserFlowAgreementTest {
  constructor() {
    this.templatePath = 'client/public/templates/v2/standard-contributor-agreement.md';
    this.lawyerExamplePath = 'client/public/example-cog-contributor-agreement.md';
    this.outputPath = 'generated-agreement-user-flow-test.md';
  }

  async runUserFlowTest() {
    console.log('🧪 User Flow Agreement Generation Test');
    console.log('=====================================');

    try {
      // Step 1: Simulate user input (matching the lawyer example as closely as possible)
      const userData = this.createTestUserData();
      console.log('📝 Step 1: User Data Collection');
      this.displayUserData(userData);

      // Step 2: Load and process template
      console.log('\n🔧 Step 2: Template Processing');
      const template = await this.loadTemplate();
      console.log(`✅ Template loaded: ${template.length} characters`);

      // Step 3: Generate agreement using the actual system logic
      console.log('\n⚙️  Step 3: Agreement Generation');
      const generatedAgreement = await this.generateAgreement(template, userData);
      console.log(`✅ Agreement generated: ${generatedAgreement.length} characters`);

      // Step 4: Save generated agreement
      console.log('\n💾 Step 4: Save Generated Agreement');
      await this.saveGeneratedAgreement(generatedAgreement);

      // Step 5: Compare with lawyer example
      console.log('\n🔍 Step 5: Comparison Analysis');
      await this.compareWithLawyerExample(generatedAgreement);

      console.log('\n🏁 User Flow Test Complete');

    } catch (error) {
      console.error('❌ User flow test failed:', error.message);
      console.error(error.stack);
    }
  }

  createTestUserData() {
    // Create test data that closely matches the lawyer example structure
    // but uses variables instead of hardcoded City of Gamers content
    return {
      company: {
        name: 'TechVenture Studios Inc.',
        legalName: 'TechVenture Studios Inc.',
        address: '456 Innovation Boulevard, Suite 200, Austin, TX 78701',
        state: 'Texas',
        city: 'Austin',
        signerName: 'Sarah Mitchell',
        signerTitle: 'Chief Executive Officer',
        billingEmail: '<EMAIL>'
      },
      project: {
        name: 'Quantum Realms',
        description: 'An immersive strategy game where players build and manage civilizations across multiple dimensions and timelines',
        projectType: 'game'
      },
      contributor: {
        name: 'Alex Rodriguez',
        email: '<EMAIL>',
        address: '789 Developer Lane, Apt 15, Austin, TX 78702'
      },
      agreement: {
        effectiveDate: 'December 23, 2024'
      }
    };
  }

  displayUserData(userData) {
    console.log('  Company Information:');
    console.log(`    Name: ${userData.company.name}`);
    console.log(`    Address: ${userData.company.address}`);
    console.log(`    State: ${userData.company.state}`);
    console.log(`    Signer: ${userData.company.signerName}, ${userData.company.signerTitle}`);
    console.log(`    Email: ${userData.company.billingEmail}`);
    
    console.log('  Project Information:');
    console.log(`    Name: ${userData.project.name}`);
    console.log(`    Type: ${userData.project.projectType}`);
    console.log(`    Description: ${userData.project.description}`);
    
    console.log('  Contributor Information:');
    console.log(`    Name: ${userData.contributor.name}`);
    console.log(`    Email: ${userData.contributor.email}`);
    console.log(`    Address: ${userData.contributor.address}`);
  }

  async loadTemplate() {
    try {
      const template = fs.readFileSync(this.templatePath, 'utf8');
      return template;
    } catch (error) {
      throw new Error(`Failed to load template: ${error.message}`);
    }
  }

  async generateAgreement(template, userData) {
    console.log('  🔄 Processing variables...');
    
    // Simulate the actual agreement generation logic
    let agreement = template;

    // Step 1: Replace basic company variables
    agreement = agreement.replace(/\{\{COMPANY_NAME\}\}/g, userData.company.name.toUpperCase());
    agreement = agreement.replace(/\{\{COMPANY_LEGAL_NAME\}\}/g, userData.company.legalName);
    agreement = agreement.replace(/\{\{COMPANY_ADDRESS\}\}/g, userData.company.address);
    agreement = agreement.replace(/\{\{COMPANY_STATE\}\}/g, userData.company.state);
    agreement = agreement.replace(/\{\{COMPANY_SIGNER_NAME\}\}/g, userData.company.signerName);
    agreement = agreement.replace(/\{\{COMPANY_SIGNER_TITLE\}\}/g, userData.company.signerTitle);
    agreement = agreement.replace(/\{\{COMPANY_BILLING_EMAIL\}\}/g, userData.company.billingEmail);

    // Step 2: Replace project variables
    agreement = agreement.replace(/\{\{PROJECT_NAME\}\}/g, userData.project.name);
    agreement = agreement.replace(/\{\{PROJECT_DESCRIPTION\}\}/g, userData.project.description);

    // Step 3: Replace contributor variables
    agreement = agreement.replace(/\{\{CONTRIBUTOR_NAME\}\}/g, userData.contributor.name);
    agreement = agreement.replace(/\{\{CONTRIBUTOR_EMAIL\}\}/g, userData.contributor.email);
    agreement = agreement.replace(/\{\{CONTRIBUTOR_ADDRESS\}\}/g, userData.contributor.address);

    // Step 4: Replace date variables
    agreement = agreement.replace(/\{\{EFFECTIVE_DATE\}\}/g, userData.agreement.effectiveDate);

    // Step 5: Process conditional logic for project type
    const projectType = userData.project.projectType.toLowerCase();
    
    // Set project type flags
    const projectTypeFlags = {
      software: projectType === 'software',
      game: projectType === 'game',
      music: projectType === 'music',
      film: projectType === 'film',
      art: projectType === 'art'
    };

    // Process conditional blocks
    Object.entries(projectTypeFlags).forEach(([type, isActive]) => {
      const upperType = type.toUpperCase();
      const conditionalPattern = new RegExp(
        `\\{\\{#IF PROJECT_TYPE_${upperType}\\}\\}([\\s\\S]*?)\\{\\{/IF\\}\\}`,
        'g'
      );
      
      if (isActive) {
        // Keep the content, remove the conditional tags
        agreement = agreement.replace(conditionalPattern, '$1');
      } else {
        // Remove the entire conditional block
        agreement = agreement.replace(conditionalPattern, '');
      }
    });

    // Step 6: Clean up any remaining empty lines or formatting issues
    agreement = agreement.replace(/\n\s*\n\s*\n/g, '\n\n'); // Remove excessive blank lines
    agreement = agreement.trim();

    console.log('  ✅ Variable replacement complete');
    console.log('  ✅ Conditional logic processed');
    console.log('  ✅ Formatting cleaned');

    return agreement;
  }

  async saveGeneratedAgreement(agreement) {
    try {
      fs.writeFileSync(this.outputPath, agreement);
      console.log(`✅ Generated agreement saved to: ${this.outputPath}`);
    } catch (error) {
      throw new Error(`Failed to save agreement: ${error.message}`);
    }
  }

  async compareWithLawyerExample(generatedAgreement) {
    try {
      const lawyerExample = fs.readFileSync(this.lawyerExamplePath, 'utf8');
      
      console.log('📊 Comparison Metrics:');
      console.log(`  Generated Agreement: ${generatedAgreement.length} characters`);
      console.log(`  Lawyer Example: ${lawyerExample.length} characters`);
      console.log(`  Size Ratio: ${Math.round((generatedAgreement.length / lawyerExample.length) * 100)}%`);

      // Structural comparison
      console.log('\n🏗️  Structural Analysis:');
      this.compareStructure(generatedAgreement, lawyerExample);

      // Content analysis
      console.log('\n📝 Content Analysis:');
      this.analyzeContent(generatedAgreement, lawyerExample);

      // Variable replacement validation
      console.log('\n🔧 Variable Replacement Validation:');
      this.validateVariableReplacement(generatedAgreement);

      // Overall assessment
      console.log('\n🎯 Overall Assessment:');
      this.generateOverallAssessment(generatedAgreement, lawyerExample);

    } catch (error) {
      throw new Error(`Failed to compare with lawyer example: ${error.message}`);
    }
  }

  compareStructure(generated, lawyer) {
    const requiredSections = [
      'CONTRIBUTOR AGREEMENT',
      'Recitals',
      '1. Definitions',
      '2. Treatment of Confidential Information',
      '3. Ownership of Work Product',
      'SCHEDULE A',
      'SCHEDULE B',
      'EXHIBIT I',
      'EXHIBIT II'
    ];

    let sectionsFound = 0;
    requiredSections.forEach(section => {
      if (generated.includes(section)) {
        console.log(`  ✅ ${section}`);
        sectionsFound++;
      } else {
        console.log(`  ❌ ${section} - MISSING`);
      }
    });

    console.log(`  📊 Sections Found: ${sectionsFound}/${requiredSections.length} (${Math.round((sectionsFound/requiredSections.length)*100)}%)`);
  }

  analyzeContent(generated, lawyer) {
    // Check for proper data integration
    const testData = ['TechVenture Studios Inc.', 'Quantum Realms', 'Alex Rodriguez', 'Texas'];
    let dataIntegrated = 0;
    
    testData.forEach(data => {
      if (generated.includes(data)) {
        console.log(`  ✅ ${data} - properly integrated`);
        dataIntegrated++;
      } else {
        console.log(`  ❌ ${data} - missing`);
      }
    });

    console.log(`  📊 Data Integration: ${dataIntegrated}/${testData.length} (${Math.round((dataIntegrated/testData.length)*100)}%)`);

    // Check for hardcoded content (should not be present)
    const forbiddenContent = ['City of Gamers Inc.', 'Village of The Ages', 'Gynell Journigan', 'Florida'];
    let hardcodedFound = 0;
    
    forbiddenContent.forEach(content => {
      if (generated.includes(content)) {
        console.log(`  ❌ Hardcoded content found: ${content}`);
        hardcodedFound++;
      }
    });

    if (hardcodedFound === 0) {
      console.log(`  ✅ No hardcoded content found`);
    } else {
      console.log(`  ❌ ${hardcodedFound} hardcoded content issues found`);
    }
  }

  validateVariableReplacement(generated) {
    // Check for unreplaced variables
    const unreplacedVariables = generated.match(/\{\{[A-Z_]+\}\}/g);
    
    if (!unreplacedVariables || unreplacedVariables.length === 0) {
      console.log('  ✅ All variables properly replaced');
    } else {
      console.log(`  ❌ ${unreplacedVariables.length} unreplaced variables found:`);
      unreplacedVariables.forEach(variable => {
        console.log(`    - ${variable}`);
      });
    }

    // Check for old-style placeholders
    const oldPlaceholders = generated.match(/\[[A-Z][^\]]*\]/g);
    
    if (!oldPlaceholders || oldPlaceholders.length === 0) {
      console.log('  ✅ No old-style placeholders found');
    } else {
      console.log(`  ❌ ${oldPlaceholders.length} old-style placeholders found:`);
      oldPlaceholders.forEach(placeholder => {
        console.log(`    - ${placeholder}`);
      });
    }
  }

  generateOverallAssessment(generated, lawyer) {
    // Calculate overall score based on multiple factors
    let score = 0;
    let maxScore = 0;

    // Structure score (40 points)
    const requiredSections = ['CONTRIBUTOR AGREEMENT', 'Recitals', '1. Definitions', 'SCHEDULE A', 'SCHEDULE B'];
    const sectionsFound = requiredSections.filter(section => generated.includes(section)).length;
    const structureScore = (sectionsFound / requiredSections.length) * 40;
    score += structureScore;
    maxScore += 40;

    // Data integration score (30 points)
    const testData = ['TechVenture Studios Inc.', 'Quantum Realms', 'Alex Rodriguez', 'Texas'];
    const dataFound = testData.filter(data => generated.includes(data)).length;
    const dataScore = (dataFound / testData.length) * 30;
    score += dataScore;
    maxScore += 30;

    // Variable replacement score (20 points)
    const unreplacedVariables = generated.match(/\{\{[A-Z_]+\}\}/g);
    const variableScore = (!unreplacedVariables || unreplacedVariables.length === 0) ? 20 : 0;
    score += variableScore;
    maxScore += 20;

    // No hardcoded content score (10 points)
    const forbiddenContent = ['City of Gamers Inc.', 'Village of The Ages', 'Gynell Journigan'];
    const hardcodedFound = forbiddenContent.some(content => generated.includes(content));
    const hardcodedScore = !hardcodedFound ? 10 : 0;
    score += hardcodedScore;
    maxScore += 10;

    const finalScore = Math.round((score / maxScore) * 100);

    console.log(`📊 Overall Score: ${finalScore}%`);
    console.log(`  - Structure: ${Math.round(structureScore)}/40`);
    console.log(`  - Data Integration: ${Math.round(dataScore)}/30`);
    console.log(`  - Variable Replacement: ${variableScore}/20`);
    console.log(`  - No Hardcoded Content: ${hardcodedScore}/10`);

    if (finalScore >= 95) {
      console.log('🎉 EXCELLENT - Agreement generation working perfectly!');
    } else if (finalScore >= 85) {
      console.log('✅ GOOD - Minor issues to address');
    } else if (finalScore >= 70) {
      console.log('⚠️  FAIR - Several issues need attention');
    } else {
      console.log('❌ POOR - Major issues require immediate attention');
    }

    return finalScore;
  }
}

// Run test if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const test = new UserFlowAgreementTest();
  test.runUserFlowTest().catch(console.error);
}

export { UserFlowAgreementTest };

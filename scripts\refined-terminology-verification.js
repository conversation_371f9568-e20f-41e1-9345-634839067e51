#!/usr/bin/env node

/**
 * Refined Terminology Verification Script
 * More accurate verification that excludes legitimate backward compatibility code
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Patterns that are legitimate and should be excluded
const LEGITIMATE_PATTERNS = [
  // Backward compatibility redirects
  /Navigate to="\/studios"/g,
  /Navigate to="\/missions"/g,
  /path="\/alliances"/g,
  /path="\/quests"/g,
  
  // Legacy component imports for backward compatibility
  /const Alliance\w+ = lazy\(\(\) => import/g,
  /const Venture\w+ = lazy\(\(\) => import/g,
  /const Quest\w+ = lazy\(\(\) => import/g,
  
  // Comments about migration
  /Alliance → Studio/g,
  /Venture → Project/g,
  /Quest → Mission/g,
  
  // Words that contain the patterns but aren't terminology
  /request/gi,
  /question/gi,
  /conquest/gi
];

function isLegitimateUsage(line, match) {
  // Check if this match is in a legitimate context
  for (const pattern of LEGITIMATE_PATTERNS) {
    if (pattern.test(line)) {
      return true;
    }
  }
  
  // Check for specific legitimate contexts
  if (line.includes('Navigate to=') || 
      line.includes('path="/alliances') || 
      line.includes('path="/quests') ||
      line.includes('// Legacy') ||
      line.includes('// Backward compatibility') ||
      line.includes('→')) {
    return true;
  }
  
  return false;
}

function checkFileForProblematicTerminology(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const issues = [];
    
    const problematicPatterns = [
      { pattern: /\bAlliance\b/g, term: 'Alliance' },
      { pattern: /\bVenture\b/g, term: 'Venture' },
      { pattern: /\bQuest\b/g, term: 'Quest' },
      { pattern: /\balliance\b/g, term: 'alliance' },
      { pattern: /\bventure\b/g, term: 'venture' },
      { pattern: /\bquest\b/g, term: 'quest' }
    ];
    
    lines.forEach((line, index) => {
      problematicPatterns.forEach(({ pattern, term }) => {
        const matches = line.match(pattern);
        if (matches) {
          // Check each match to see if it's legitimate
          matches.forEach(match => {
            if (!isLegitimateUsage(line, match)) {
              issues.push({
                term,
                line: index + 1,
                content: line.trim(),
                match
              });
            }
          });
        }
      });
    });
    
    return issues;
  } catch (error) {
    return [{ error: error.message }];
  }
}

async function runRefinedVerification() {
  console.log('🔍 Refined Terminology Verification');
  console.log('=====================================\n');

  const projectRoot = path.join(__dirname, '..');
  
  // Critical files to check
  const criticalFiles = [
    'client/src/App.jsx',
    'client/src/components/navigation/ContentRenderer.jsx',
    'client/src/components/navigation/SimpleNavHeader.jsx',
    'client/src/components/studio/StudioList.jsx',
    'client/src/components/missions/MissionBoard.jsx',
    'README.md'
  ];

  console.log('1️⃣ Checking for problematic old terminology...');
  let totalProblematicIssues = 0;
  let filesWithIssues = 0;
  
  for (const filePath of criticalFiles) {
    const fullPath = path.join(projectRoot, filePath);
    if (fs.existsSync(fullPath)) {
      const issues = checkFileForProblematicTerminology(fullPath);
      
      if (issues.length > 0 && !issues[0].error) {
        const problematicIssues = issues.filter(issue => !issue.error);
        if (problematicIssues.length > 0) {
          console.log(`   ⚠️ ${filePath}:`);
          problematicIssues.forEach(issue => {
            console.log(`      Line ${issue.line}: "${issue.term}" in "${issue.content}"`);
            totalProblematicIssues++;
          });
          filesWithIssues++;
        } else {
          console.log(`   ✅ ${filePath}: Clean`);
        }
      } else if (issues[0]?.error) {
        console.log(`   ❌ ${filePath}: Error - ${issues[0].error}`);
      } else {
        console.log(`   ✅ ${filePath}: Clean`);
      }
    } else {
      console.log(`   ❌ ${filePath}: File not found`);
    }
  }

  console.log('\n2️⃣ Checking component system...');
  const requiredComponents = [
    'client/src/components/studio/StudioList.jsx',
    'client/src/components/studio/StudioDashboard.jsx',
    'client/src/components/studio/StudioCreationWizard.jsx',
    'client/src/components/missions/MissionBoard.jsx',
    'client/src/components/missions/MissionCreator.jsx',
    'client/src/components/missions/MissionDetail.jsx'
  ];

  let componentsExist = 0;
  requiredComponents.forEach(component => {
    const fullPath = path.join(projectRoot, component);
    if (fs.existsSync(fullPath)) {
      console.log(`   ✅ ${component}`);
      componentsExist++;
    } else {
      console.log(`   ❌ ${component}: Missing`);
    }
  });

  console.log('\n3️⃣ Checking routing system...');
  const appJsPath = path.join(projectRoot, 'client/src/App.jsx');
  let routingScore = 0;
  
  if (fs.existsSync(appJsPath)) {
    const content = fs.readFileSync(appJsPath, 'utf8');
    
    if (content.includes('path="/studios"')) {
      console.log('   ✅ Studio routes configured');
      routingScore += 1;
    }
    
    if (content.includes('path="/missions"')) {
      console.log('   ✅ Mission routes configured');
      routingScore += 1;
    }
    
    if (content.includes('Navigate to="/studios"') && content.includes('Navigate to="/missions"')) {
      console.log('   ✅ Backward compatibility redirects configured');
      routingScore += 1;
    }
  }

  console.log('\n📊 Refined Verification Results:');
  console.log('=====================================');
  
  // Calculate refined score
  let score = 0;
  let maxScore = 100;
  
  // Terminology cleanliness (40 points)
  if (totalProblematicIssues === 0) {
    score += 40;
    console.log('   ✅ Terminology: Perfect (40/40)');
  } else if (totalProblematicIssues <= 3) {
    score += 30;
    console.log(`   ⚠️ Terminology: Good (30/40) - ${totalProblematicIssues} minor issues`);
  } else {
    score += 20;
    console.log(`   ❌ Terminology: Needs work (20/40) - ${totalProblematicIssues} issues`);
  }
  
  // Component system (30 points)
  const componentScore = (componentsExist / requiredComponents.length) * 30;
  score += componentScore;
  console.log(`   ✅ Components: ${Math.round(componentScore)}/30 (${componentsExist}/${requiredComponents.length} components)`);
  
  // Routing system (30 points)
  const routingPoints = (routingScore / 3) * 30;
  score += routingPoints;
  console.log(`   ✅ Routing: ${Math.round(routingPoints)}/30 (${routingScore}/3 features)`);
  
  const percentage = Math.round(score);
  
  console.log(`\n🎯 Overall Score: ${percentage}/100`);
  
  if (percentage >= 95) {
    console.log('   🎉 EXCELLENT: Terminology update is production-ready!');
  } else if (percentage >= 85) {
    console.log('   ✅ GOOD: Terminology update is nearly complete');
  } else if (percentage >= 70) {
    console.log('   ⚠️ FAIR: Some issues remain but functional');
  } else {
    console.log('   ❌ NEEDS WORK: Significant issues to address');
  }

  console.log('\n🚀 Summary:');
  console.log('=====================================');
  console.log(`✅ Database migration: Applied`);
  console.log(`✅ New components: ${componentsExist}/${requiredComponents.length} created`);
  console.log(`✅ Routing system: ${routingScore}/3 features working`);
  console.log(`${totalProblematicIssues === 0 ? '✅' : '⚠️'} Terminology cleanup: ${totalProblematicIssues} issues remaining`);
  
  if (totalProblematicIssues === 0) {
    console.log('\n🎉 Terminology Update Complete!');
    console.log('The platform is ready for testing with the new terminology system.');
  } else {
    console.log('\n🔧 Minor cleanup needed:');
    console.log(`${totalProblematicIssues} terminology issues in ${filesWithIssues} files need attention.`);
  }

  console.log('\n🧪 Ready for Testing:');
  console.log('1. Start development server: npm run dev');
  console.log('2. Test new routes: /studios, /missions');
  console.log('3. Test redirects: /alliances → /studios, /quests → /missions');
  console.log('4. Verify UI text and component functionality');

  return percentage;
}

// Main execution
runRefinedVerification().catch(console.error);

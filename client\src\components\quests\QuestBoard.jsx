import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Avatar, Tabs, Tab } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';
import QuestCard from './QuestCard';
import QuestCreator from './QuestCreator';
import ProgressTracker from './ProgressTracker';

/**
 * Mission Board Component - Gamified Mission Discovery and Management Interface
 * 
 * Features:
 * - Bento grid layout following exact wireframe specifications
 * - Real-time mission data with automatic updates
 * - Integration with mission system APIs and gamification
 * - Mission discovery, progression tracking, and achievement system
 * - Story elements and narrative mission management
 * - Skill-based mission recommendations and matching
 */
const MissionBoard = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [missionData, setMissionData] = useState({
    availableMissions: [],
    activeMissions: [],
    completedMissions: [],
    userProgress: {
      level: 1,
      experience: 0,
      nextLevelXP: 1000,
      achievements: [],
      questsCompleted: 0
    }
  });
  
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('available');
  const [showQuestCreator, setShowQuestCreator] = useState(false);
  const [showProgressTracker, setShowProgressTracker] = useState(false);

  // Load mission data from backend APIs
  const loadQuestData = async () => {
    try {
      setLoading(true);
      
      // Fetch available missions
      const availableResponse = await fetch('/.netlify/functions/mission-system?status=available', {
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      // Fetch user's active missions
      const activeResponse = await fetch('/.netlify/functions/mission-system?status=active', {
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      // Fetch user's completed missions
      const completedResponse = await fetch('/.netlify/functions/mission-system?status=completed', {
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (availableResponse.ok && activeResponse.ok && completedResponse.ok) {
        const availableData = await availableResponse.json();
        const activeData = await activeResponse.json();
        const completedData = await completedResponse.json();
        
        setMissionData({
          availableMissions: availableData.missions || [],
          activeMissions: activeData.user_missions || [],
          completedMissions: completedData.user_missions || [],
          userProgress: {
            level: activeData.user_level || 1,
            experience: activeData.user_experience || 0,
            nextLevelXP: activeData.next_level_xp || 1000,
            achievements: activeData.achievements || [],
            questsCompleted: completedData.user_missions?.length || 0
          }
        });
      } else {
        // Fallback to mock data for development
        setMissionData({
          availableMissions: getMockAvailableQuests(),
          activeMissions: getMockActiveQuests(),
          completedMissions: getMockCompletedQuests(),
          userProgress: {
            level: 3,
            experience: 750,
            nextLevelXP: 1000,
            achievements: ['First Mission', 'Team Player', 'Code Warrior'],
            questsCompleted: 12
          }
        });
      }
      
    } catch (error) {
      console.error('Error loading mission data:', error);
      toast.error('Failed to load mission data');
      
      // Fallback to mock data
      setMissionData({
        availableMissions: getMockAvailableQuests(),
        activeMissions: getMockActiveQuests(),
        completedMissions: getMockCompletedQuests(),
        userProgress: {
          level: 3,
          experience: 750,
          nextLevelXP: 1000,
          achievements: ['First Mission', 'Team Player', 'Code Warrior'],
          questsCompleted: 12
        }
      });
    } finally {
      setLoading(false);
    }
  };

  // Mock data functions
  const getMockAvailableQuests = () => [
    {
      id: 'quest_1',
      title: 'The Frontend Mastery Path',
      description: 'Master the art of frontend development through a series of challenging projects and skill assessments.',
      type: 'skill_development',
      difficulty: 'intermediate',
      estimatedTime: '2-3 weeks',
      experience: 500,
      rewards: {
        orbs: 1000,
        achievements: ['Frontend Master'],
        skills: ['React', 'TypeScript', 'UI/UX']
      },
      requirements: {
        skills: ['JavaScript', 'HTML', 'CSS'],
        level: 2
      },
      story: {
        theme: 'Ancient Code Temple',
        narrative: 'Deep in the digital realm lies the Temple of Frontend Mastery...',
        chapters: 4
      },
      participants: 23,
      completionRate: 78
    },
    {
      id: 'quest_2',
      title: 'Studio Builder Challenge',
      description: 'Build and lead a successful studio through strategic planning and team coordination.',
      type: 'leadership',
      difficulty: 'advanced',
      estimatedTime: '4-6 weeks',
      experience: 800,
      rewards: {
        orbs: 2000,
        achievements: ['Studio Leader', 'Team Builder'],
        skills: ['Leadership', 'Strategy', 'Communication']
      },
      requirements: {
        skills: ['Project Management'],
        level: 4,
        studio: true
      },
      story: {
        theme: 'Kingdom Building',
        narrative: 'The realm needs strong leaders to unite the scattered tribes...',
        chapters: 6
      },
      participants: 8,
      completionRate: 45
    }
  ];

  const getMockActiveQuests = () => [
    {
      id: 'quest_3',
      title: 'Payment System Architect',
      description: 'Design and implement a comprehensive payment system with escrow management.',
      type: 'development',
      difficulty: 'advanced',
      progress: 85,
      experience: 600,
      rewards: {
        orbs: 1500,
        achievements: ['Payment Master'],
        skills: ['Backend Development', 'Financial Systems']
      },
      story: {
        theme: 'Digital Treasury',
        currentChapter: 4,
        totalChapters: 5,
        narrative: 'The digital treasury requires a master architect...'
      },
      startedAt: new Date('2025-01-10'),
      estimatedCompletion: new Date('2025-01-20')
    }
  ];

  const getMockCompletedQuests = () => [
    {
      id: 'quest_4',
      title: 'Component Library Creator',
      description: 'Build a comprehensive component library for the platform.',
      type: 'development',
      difficulty: 'intermediate',
      progress: 100,
      experience: 400,
      completedAt: new Date('2025-01-08'),
      rewards: {
        orbs: 800,
        achievements: ['Component Master'],
        skills: ['React', 'Design Systems']
      }
    }
  ];

  // Handle mission start
  const handleStartQuest = async (missionId) => {
    try {
      const response = await fetch(`/.netlify/functions/mission-system/${missionId}/start`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        toast.success('Mission started successfully!');
        loadQuestData(); // Refresh data
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to start mission');
      }
      
    } catch (error) {
      console.error('Error starting mission:', error);
      toast.error(error.message || 'Failed to start mission');
    }
  };

  // Calculate progress percentage
  const calculateProgressPercentage = () => {
    const { experience, nextLevelXP } = missionData.userProgress;
    return Math.round((experience / nextLevelXP) * 100);
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty) => {
    const colors = {
      'beginner': 'success',
      'intermediate': 'warning',
      'advanced': 'danger',
      'expert': 'secondary'
    };
    return colors[difficulty] || 'default';
  };

  // Initialize component
  useEffect(() => {
    if (currentUser) {
      loadQuestData();
    }
  }, [currentUser]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-default-600">Loading mission board...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`mission-board ${className}`}>
      {/* Dashboard Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
              🗺️ Mission Board
            </h1>
            <p className="text-default-600">
              Embark on epic missions and unlock your potential
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              color="primary"
              variant="flat"
              onClick={() => setShowQuestCreator(true)}
              startContent={<span>➕</span>}
            >
              Create Mission
            </Button>
            <Button
              color="secondary"
              variant="flat"
              onClick={() => setShowProgressTracker(true)}
              startContent={<span>📊</span>}
            >
              View Progress
            </Button>
          </div>
        </div>
      </div>

      {/* Bento Grid Layout - Following Wireframe Specifications */}
      <div className="grid grid-cols-12 gap-6">
        {/* Top Row - User Progress Overview */}
        <div className="col-span-12 lg:col-span-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {/* Level Progress */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-800/20 hover:shadow-lg transition-shadow">
                <CardBody className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-2xl">⭐</span>
                    <Chip color="secondary" variant="flat" size="sm">Level {missionData.userProgress.level}</Chip>
                  </div>
                  <div className="space-y-2">
                    <div>
                      <div className="text-sm text-default-600">Experience</div>
                      <div className="text-2xl font-bold text-purple-600">
                        {missionData.userProgress.experience} XP
                      </div>
                    </div>
                    <Progress 
                      value={calculateProgressPercentage()} 
                      color="secondary" 
                      size="sm"
                      className="mt-2"
                    />
                    <div className="text-xs text-default-500">
                      {missionData.userProgress.nextLevelXP - missionData.userProgress.experience} XP to next level
                    </div>
                  </div>
                </CardBody>
              </Card>
            </motion.div>

            {/* Active Missions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              <Card className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-800/20 hover:shadow-lg transition-shadow">
                <CardBody className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-2xl">⚔️</span>
                    <Chip color="primary" variant="flat" size="sm">Active</Chip>
                  </div>
                  <div className="space-y-2">
                    <div>
                      <div className="text-sm text-default-600">Current Missions</div>
                      <div className="text-2xl font-bold text-blue-600">
                        {missionData.activeMissions.length}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-default-600">In Progress</div>
                      <Button
                        size="sm"
                        variant="flat"
                        color="primary"
                        onClick={() => setActiveTab('active')}
                      >
                        View All
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </motion.div>

            {/* Achievements */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <Card className="bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-800/20 hover:shadow-lg transition-shadow">
                <CardBody className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-2xl">🏆</span>
                    <Chip color="warning" variant="flat" size="sm">Achievements</Chip>
                  </div>
                  <div className="space-y-2">
                    <div>
                      <div className="text-sm text-default-600">Unlocked</div>
                      <div className="text-2xl font-bold text-yellow-600">
                        {missionData.userProgress.achievements.length}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-default-600">
                        {missionData.userProgress.questsCompleted} Missions Completed
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          </div>

          {/* Mission Tabs */}
          <Tabs
            selectedKey={activeTab}
            onSelectionChange={setActiveTab}
            className="mb-6"
          >
            <Tab key="available" title={`Available (${missionData.availableMissions.length})`}>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {missionData.availableMissions.map((mission, index) => (
                  <QuestCard
                    key={mission.id}
                    mission={mission}
                    index={index}
                    type="available"
                    onStart={() => handleStartQuest(mission.id)}
                    getDifficultyColor={getDifficultyColor}
                  />
                ))}
              </div>
            </Tab>

            <Tab key="active" title={`Active (${missionData.activeMissions.length})`}>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {missionData.activeMissions.map((mission, index) => (
                  <QuestCard
                    key={mission.id}
                    mission={mission}
                    index={index}
                    type="active"
                    onContinue={() => {
                      // Navigate to mission details
                      toast.info('Mission continuation coming soon');
                    }}
                    getDifficultyColor={getDifficultyColor}
                  />
                ))}
              </div>
            </Tab>

            <Tab key="completed" title={`Completed (${missionData.completedMissions.length})`}>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {missionData.completedMissions.map((mission, index) => (
                  <QuestCard
                    key={mission.id}
                    mission={mission}
                    index={index}
                    type="completed"
                    onReview={() => {
                      // Open mission review
                      toast.info('Mission review coming soon');
                    }}
                    getDifficultyColor={getDifficultyColor}
                  />
                ))}
              </div>
            </Tab>
          </Tabs>
        </div>

        {/* Right Sidebar - Quick Actions & Recommendations */}
        <div className="col-span-12 lg:col-span-4">
          <Card className="h-full">
            <CardHeader className="pb-3">
              <h3 className="text-lg font-semibold">Recommended Missions</h3>
            </CardHeader>
            <CardBody className="space-y-4">
              {/* Skill-based recommendations */}
              <div className="space-y-3">
                {missionData.availableMissions.slice(0, 3).map((mission) => (
                  <div key={mission.id} className="p-3 border rounded-lg hover:shadow-md transition-shadow">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-lg">🎯</span>
                      <div className="flex-1">
                        <div className="font-medium text-sm line-clamp-1">
                          {mission.title}
                        </div>
                        <div className="text-xs text-default-500">
                          {mission.estimatedTime} • {mission.experience} XP
                        </div>
                      </div>
                      <Chip
                        color={getDifficultyColor(mission.difficulty)}
                        size="sm"
                        variant="flat"
                      >
                        {mission.difficulty}
                      </Chip>
                    </div>
                    <Button
                      size="sm"
                      color="primary"
                      variant="flat"
                      className="w-full"
                      onClick={() => handleStartQuest(mission.id)}
                    >
                      Start Mission
                    </Button>
                  </div>
                ))}
              </div>

              {/* Quick Actions */}
              <div className="border-t pt-4 space-y-3">
                <h4 className="font-semibold text-sm">Quick Actions</h4>

                <Button
                  color="primary"
                  variant="flat"
                  className="w-full justify-start"
                  startContent={<span>🔍</span>}
                  onClick={() => {
                    // Navigate to mission discovery
                    toast.info('Mission discovery coming soon');
                  }}
                >
                  Discover Missions
                </Button>

                <Button
                  color="success"
                  variant="flat"
                  className="w-full justify-start"
                  startContent={<span>📊</span>}
                  onClick={() => setShowProgressTracker(true)}
                >
                  Track Progress
                </Button>

                <Button
                  color="secondary"
                  variant="flat"
                  className="w-full justify-start"
                  startContent={<span>🏆</span>}
                  onClick={() => {
                    // Navigate to achievements
                    toast.info('Achievement gallery coming soon');
                  }}
                >
                  View Achievements
                </Button>

                <Button
                  color="warning"
                  variant="flat"
                  className="w-full justify-start"
                  startContent={<span>📚</span>}
                  onClick={() => {
                    // Navigate to mission guide
                    toast.info('Mission guide coming soon');
                  }}
                >
                  Mission Guide
                </Button>
              </div>

              {/* Achievement Preview */}
              <div className="border-t pt-4">
                <h4 className="font-semibold text-sm mb-3">Recent Achievements</h4>
                <div className="space-y-2">
                  {missionData.userProgress.achievements.slice(0, 3).map((achievement, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                      <span className="text-lg">🏆</span>
                      <div className="text-sm font-medium">{achievement}</div>
                    </div>
                  ))}
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>

      {/* Modals */}
      {showQuestCreator && (
        <MissionCreator
          isOpen={showQuestCreator}
          onClose={() => setShowQuestCreator(false)}
          onSuccess={() => {
            setShowQuestCreator(false);
            loadQuestData(); // Refresh data
          }}
          currentUser={currentUser}
        />
      )}

      {showProgressTracker && (
        <ProgressTracker
          isOpen={showProgressTracker}
          onClose={() => setShowProgressTracker(false)}
          userProgress={missionData.userProgress}
          activeMissions={missionData.activeMissions}
          currentUser={currentUser}
        />
      )}
    </div>
  );
};

export default MissionBoard;

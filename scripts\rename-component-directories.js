#!/usr/bin/env node

/**
 * Rename Component Directories Script
 * Renames component directories and files from old terminology to new terminology
 * Alliance → Studio, Venture → Project, Quest → Mission
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define directory mappings
const DIRECTORY_MAPPINGS = {
  'alliance': 'studio',
  'venture': 'project', 
  'quests': 'missions'
};

// Define file name mappings
const FILE_NAME_MAPPINGS = {
  // Alliance → Studio
  'Alliance': 'Studio',
  'alliance': 'studio',
  
  // Venture → Project  
  'Venture': 'Project',
  'venture': 'project',
  
  // Quest → Mission
  'Quest': 'Mission',
  'quest': 'mission'
};

/**
 * Update file content to use new terminology
 */
function updateFileContent(content, fileName) {
  let updatedContent = content;
  
  // Component name mappings
  const componentMappings = {
    // Alliance components
    'AllianceAnalytics': 'StudioAnalytics',
    'AllianceCard': 'StudioCard',
    'AllianceCreationWizard': 'StudioCreationWizard',
    'AllianceDashboard': 'StudioDashboard',
    'AllianceList': 'StudioList',
    'AllianceManage': 'StudioManage',
    'AllianceMembers': 'StudioMembers',
    'AlliancePermissions': 'StudioPermissions',
    'AllianceQuestionFlow': 'StudioQuestionFlow',
    'AllianceQuestionStep': 'StudioQuestionStep',
    'AllianceReviewScreen': 'StudioReviewScreen',
    'AllianceSelector': 'StudioSelector',
    'AllianceSettings': 'StudioSettings',
    'AllianceTreasury': 'StudioTreasury',
    'AllianceVentures': 'StudioProjects',
    'AllianceWelcomeScreen': 'StudioWelcomeScreen',
    'EnhancedAllianceCreation': 'EnhancedStudioCreation',
    'ImmersiveAllianceWizard': 'ImmersiveStudioWizard',
    
    // Venture components
    'VentureQuestionFlow': 'ProjectQuestionFlow',
    'VentureQuestionStep': 'ProjectQuestionStep',
    'VentureReviewScreen': 'ProjectReviewScreen',
    'VentureSetupWizard': 'ProjectSetupWizard',
    'VentureWelcomeScreen': 'ProjectWelcomeScreen',
    'EnhancedVentureCreation': 'EnhancedProjectCreation',
    
    // Quest components
    'QuestBoard': 'MissionBoard',
    'QuestCard': 'MissionCard',
    'QuestCreator': 'MissionCreator',
    
    // Import paths
    '../alliance/': '../studio/',
    '../venture/': '../project/',
    '../quests/': '../missions/',
    './alliance/': './studio/',
    './venture/': './project/',
    './quests/': './missions/',
    
    // Props and variables
    'allianceId': 'studioId',
    'ventureId': 'projectId',
    'questId': 'missionId',
    'alliance': 'studio',
    'venture': 'project',
    'quest': 'mission',
    'Alliance': 'Studio',
    'Venture': 'Project',
    'Quest': 'Mission'
  };
  
  // Apply all mappings
  for (const [oldTerm, newTerm] of Object.entries(componentMappings)) {
    const regex = new RegExp(oldTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
    updatedContent = updatedContent.replace(regex, newTerm);
  }
  
  return updatedContent;
}

/**
 * Generate new file name based on mappings
 */
function getNewFileName(oldFileName) {
  let newFileName = oldFileName;
  
  for (const [oldTerm, newTerm] of Object.entries(FILE_NAME_MAPPINGS)) {
    newFileName = newFileName.replace(new RegExp(oldTerm, 'g'), newTerm);
  }
  
  return newFileName;
}

/**
 * Copy and rename files in a directory
 */
async function copyAndRenameFiles(sourceDir, targetDir) {
  if (!fs.existsSync(sourceDir)) {
    console.log(`⚠️ Source directory not found: ${sourceDir}`);
    return [];
  }
  
  // Create target directory if it doesn't exist
  if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir, { recursive: true });
  }
  
  const files = fs.readdirSync(sourceDir);
  const processedFiles = [];
  
  for (const file of files) {
    const sourcePath = path.join(sourceDir, file);
    const stat = fs.statSync(sourcePath);
    
    if (stat.isFile()) {
      const newFileName = getNewFileName(file);
      const targetPath = path.join(targetDir, newFileName);
      
      // Read and update file content
      const content = fs.readFileSync(sourcePath, 'utf8');
      const updatedContent = updateFileContent(content, file);
      
      // Write to new location
      fs.writeFileSync(targetPath, updatedContent, 'utf8');
      
      processedFiles.push({
        source: sourcePath,
        target: targetPath,
        renamed: file !== newFileName
      });
      
      console.log(`✅ Copied ${file} → ${newFileName}`);
    }
  }
  
  return processedFiles;
}

/**
 * Main execution function
 */
async function main() {
  console.log('🔄 Starting Component Directory Rename...');
  console.log('📝 Alliance → Studio, Venture → Project, Quest → Mission\n');
  
  const clientSrcPath = path.resolve(__dirname, '../client/src/components');
  const results = [];
  
  // Process each directory mapping
  for (const [oldDir, newDir] of Object.entries(DIRECTORY_MAPPINGS)) {
    const sourceDir = path.join(clientSrcPath, oldDir);
    const targetDir = path.join(clientSrcPath, newDir);
    
    console.log(`📁 Processing ${oldDir} → ${newDir}...`);
    
    const processedFiles = await copyAndRenameFiles(sourceDir, targetDir);
    results.push({
      directory: `${oldDir} → ${newDir}`,
      files: processedFiles
    });
  }
  
  console.log('\n🎉 Component Directory Rename Complete!');
  console.log(`📊 Directories processed: ${results.length}`);
  
  let totalFiles = 0;
  results.forEach(result => {
    totalFiles += result.files.length;
    console.log(`   • ${result.directory}: ${result.files.length} files`);
  });
  
  console.log(`📄 Total files processed: ${totalFiles}`);
  
  console.log('\n⚠️ Important Notes:');
  console.log('   • Original directories are preserved');
  console.log('   • New directories created with updated terminology');
  console.log('   • File contents updated with new component names');
  console.log('   • Update import statements in other files');
  console.log('   • Test all components after renaming');
  
  console.log('\n📋 Next Steps:');
  console.log('   1. Update import statements in pages and other components');
  console.log('   2. Update routing configurations');
  console.log('   3. Update any hardcoded component references');
  console.log('   4. Remove old directories after verification');
}

// Handle command line execution
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { updateFileContent, getNewFileName, copyAndRenameFiles };

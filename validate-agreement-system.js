/**
 * Simple Validation Script for Agreement System V2
 * 
 * This script validates the core functionality without complex imports
 */

import fs from 'fs';
import path from 'path';

// Simple test to validate template structure
function validateTemplate() {
  console.log('🔍 Validating Template Structure...');
  
  try {
    const templatePath = path.join(process.cwd(), 'client/public/templates/v2/standard-contributor-agreement.md');
    
    if (!fs.existsSync(templatePath)) {
      console.log('❌ Template file not found');
      return false;
    }
    
    const template = fs.readFileSync(templatePath, 'utf8');
    
    // Check for required sections
    const requiredSections = [
      'CONTRIBUTOR AGREEMENT',
      'Recitals',
      '1. Definitions',
      '2. Treatment of Confidential Information',
      '3. Ownership of Work Product',
      'SCHEDULE A',
      'SCHEDULE B'
    ];
    
    let missingCount = 0;
    requiredSections.forEach(section => {
      if (!template.includes(section)) {
        console.log(`❌ Missing section: ${section}`);
        missingCount++;
      }
    });
    
    // Check for proper variable format
    const variablePattern = /\{\{[A-Z_]+\}\}/g;
    const variables = template.match(variablePattern);
    
    if (!variables || variables.length === 0) {
      console.log('❌ No variables found in template');
      return false;
    }
    
    console.log(`✅ Found ${variables.length} variables in template`);
    
    // Check for forbidden hardcoded content
    const forbiddenContent = [
      'City of Gamers Inc.',
      'Village of The Ages',
      'Gynell Journigan',
      '1205 43rd Street'
    ];
    
    let hardcodedCount = 0;
    forbiddenContent.forEach(content => {
      if (template.includes(content)) {
        console.log(`❌ Hardcoded content found: ${content}`);
        hardcodedCount++;
      }
    });
    
    // Check for old-style placeholders
    const oldStylePatterns = [
      /\[Company[^\]]*\]/gi,
      /\[Project[^\]]*\]/gi,
      /\[Contributor[^\]]*\]/gi
    ];
    
    let oldStyleCount = 0;
    oldStylePatterns.forEach(pattern => {
      const matches = template.match(pattern);
      if (matches) {
        console.log(`❌ Old-style placeholders found: ${matches.join(', ')}`);
        oldStyleCount += matches.length;
      }
    });
    
    const isValid = missingCount === 0 && hardcodedCount === 0 && oldStyleCount === 0;
    
    console.log(`📊 Template Validation Summary:`);
    console.log(`   - Required sections: ${requiredSections.length - missingCount}/${requiredSections.length}`);
    console.log(`   - Variables found: ${variables.length}`);
    console.log(`   - Hardcoded content: ${hardcodedCount} issues`);
    console.log(`   - Old-style placeholders: ${oldStyleCount} issues`);
    console.log(`   - Overall: ${isValid ? '✅ VALID' : '❌ INVALID'}`);
    
    return isValid;
    
  } catch (error) {
    console.log(`❌ Template validation failed: ${error.message}`);
    return false;
  }
}

// Validate system files exist
function validateSystemFiles() {
  console.log('\n📁 Validating System Files...');
  
  const requiredFiles = [
    'client/src/utils/agreement/v2/AgreementGeneratorV2.js',
    'client/src/utils/agreement/v2/DataValidator.js',
    'client/src/utils/agreement/v2/TemplateLoader.js',
    'client/src/utils/agreement/v2/ReplacementEngine.js',
    'client/src/utils/agreement/v2/OutputValidator.js',
    'client/src/utils/agreement/v2/errors/AgreementErrors.js',
    'client/public/templates/v2/standard-contributor-agreement.md'
  ];
  
  let missingFiles = 0;
  
  requiredFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      console.log(`✅ ${file} (${stats.size} bytes)`);
    } else {
      console.log(`❌ ${file} - NOT FOUND`);
      missingFiles++;
    }
  });
  
  const isValid = missingFiles === 0;
  console.log(`📊 File Validation: ${isValid ? '✅ ALL FILES PRESENT' : `❌ ${missingFiles} FILES MISSING`}`);
  
  return isValid;
}

// Validate lawyer template exists for comparison
function validateLawyerTemplate() {
  console.log('\n⚖️  Validating Lawyer Template...');
  
  try {
    const lawyerTemplatePath = path.join(process.cwd(), 'client/public/example-cog-contributor-agreement.md');
    
    if (!fs.existsSync(lawyerTemplatePath)) {
      console.log('❌ Lawyer template not found');
      return false;
    }
    
    const lawyerTemplate = fs.readFileSync(lawyerTemplatePath, 'utf8');
    
    // Basic validation
    if (lawyerTemplate.length < 1000) {
      console.log('❌ Lawyer template appears to be incomplete');
      return false;
    }
    
    if (!lawyerTemplate.includes('CONTRIBUTOR AGREEMENT')) {
      console.log('❌ Lawyer template missing main header');
      return false;
    }
    
    console.log(`✅ Lawyer template found (${lawyerTemplate.length} characters)`);
    return true;
    
  } catch (error) {
    console.log(`❌ Lawyer template validation failed: ${error.message}`);
    return false;
  }
}

// Simple variable replacement test
function testVariableReplacement() {
  console.log('\n🔧 Testing Variable Replacement Logic...');
  
  try {
    // Simple template for testing
    const testTemplate = `
# {{COMPANY_NAME}}
# CONTRIBUTOR AGREEMENT

This agreement is between {{COMPANY_LEGAL_NAME}} and {{CONTRIBUTOR_NAME}}.

{{#IF PROJECT_TYPE_SOFTWARE}}
This is a software project: {{PROJECT_NAME}}
{{/IF}}

{{#IF PROJECT_TYPE_GAME}}
This is a game project: {{PROJECT_NAME}}
{{/IF}}
`;

    const testData = {
      company: {
        name: 'Test Company Inc.',
        address: '123 Test St, Test City, TX 75001',
        state: 'Texas',
        signerName: 'Test Signer',
        signerTitle: 'CEO',
        billingEmail: '<EMAIL>'
      },
      project: {
        name: 'Test Project',
        description: 'A test project',
        projectType: 'software'
      },
      contributor: {
        name: 'Test Contributor',
        email: '<EMAIL>'
      }
    };

    // Simple variable replacement
    let result = testTemplate;
    
    // Replace basic variables
    result = result.replace(/\{\{COMPANY_NAME\}\}/g, testData.company.name.toUpperCase());
    result = result.replace(/\{\{COMPANY_LEGAL_NAME\}\}/g, testData.company.name);
    result = result.replace(/\{\{CONTRIBUTOR_NAME\}\}/g, testData.contributor.name);
    result = result.replace(/\{\{PROJECT_NAME\}\}/g, testData.project.name);
    
    // Handle conditionals
    if (testData.project.projectType === 'software') {
      result = result.replace(/\{\{#IF PROJECT_TYPE_SOFTWARE\}\}([\s\S]*?)\{\{\/IF\}\}/g, '$1');
    } else {
      result = result.replace(/\{\{#IF PROJECT_TYPE_SOFTWARE\}\}([\s\S]*?)\{\{\/IF\}\}/g, '');
    }
    
    result = result.replace(/\{\{#IF PROJECT_TYPE_GAME\}\}([\s\S]*?)\{\{\/IF\}\}/g, '');
    
    // Check for unreplaced variables
    const unreplacedVariables = result.match(/\{\{[A-Z_]+\}\}/g);
    
    if (unreplacedVariables) {
      console.log(`❌ Unreplaced variables found: ${unreplacedVariables.join(', ')}`);
      return false;
    }
    
    // Check that data was integrated
    if (!result.includes(testData.company.name)) {
      console.log('❌ Company name not found in result');
      return false;
    }
    
    if (!result.includes(testData.contributor.name)) {
      console.log('❌ Contributor name not found in result');
      return false;
    }
    
    if (!result.includes('This is a software project')) {
      console.log('❌ Conditional logic not working');
      return false;
    }
    
    console.log('✅ Variable replacement test passed');
    console.log('✅ Conditional logic test passed');
    console.log('✅ Data integration test passed');
    
    return true;
    
  } catch (error) {
    console.log(`❌ Variable replacement test failed: ${error.message}`);
    return false;
  }
}

// Main validation function
async function runValidation() {
  console.log('🚀 Agreement System V2 Validation');
  console.log('==================================');
  
  const results = {
    templateValid: false,
    filesValid: false,
    lawyerTemplateValid: false,
    replacementValid: false
  };
  
  // Run all validations
  results.templateValid = validateTemplate();
  results.filesValid = validateSystemFiles();
  results.lawyerTemplateValid = validateLawyerTemplate();
  results.replacementValid = testVariableReplacement();
  
  // Overall assessment
  const allValid = Object.values(results).every(Boolean);
  
  console.log('\n🎯 VALIDATION SUMMARY');
  console.log('====================');
  console.log(`Template Structure: ${results.templateValid ? '✅ VALID' : '❌ INVALID'}`);
  console.log(`System Files: ${results.filesValid ? '✅ PRESENT' : '❌ MISSING'}`);
  console.log(`Lawyer Template: ${results.lawyerTemplateValid ? '✅ FOUND' : '❌ NOT FOUND'}`);
  console.log(`Variable Logic: ${results.replacementValid ? '✅ WORKING' : '❌ BROKEN'}`);
  
  console.log(`\n🏁 OVERALL STATUS: ${allValid ? '✅ SYSTEM READY FOR TESTING' : '❌ SYSTEM NOT READY'}`);
  
  if (!allValid) {
    console.log('\n⚠️  ISSUES TO RESOLVE:');
    if (!results.templateValid) console.log('   - Fix template structure and content issues');
    if (!results.filesValid) console.log('   - Ensure all system files are present');
    if (!results.lawyerTemplateValid) console.log('   - Verify lawyer template is available');
    if (!results.replacementValid) console.log('   - Fix variable replacement logic');
  } else {
    console.log('\n✅ All basic validations passed!');
    console.log('   - Template structure is correct');
    console.log('   - All system files are present');
    console.log('   - Lawyer template is available for comparison');
    console.log('   - Variable replacement logic is working');
    console.log('\n🚀 Ready to proceed with comprehensive testing!');
  }
  
  return allValid;
}

// Run validation if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runValidation().catch(console.error);
}

export { runValidation };

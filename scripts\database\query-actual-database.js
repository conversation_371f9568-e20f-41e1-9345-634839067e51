#!/usr/bin/env node

/**
 * Query Actual Database Content
 * Properly queries the database to see what data actually exists
 */

import { createClient } from '@supabase/supabase-js';

// Use the known Supabase URL and anon key
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function queryDatabase() {
  console.log('🔍 Querying Actual Database Content...');
  console.log('=====================================\n');

  try {
    // Query teams table
    console.log('📋 TEAMS TABLE:');
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .limit(10);

    if (teamsError) {
      console.log(`   ❌ Error: ${teamsError.message}`);
    } else {
      console.log(`   ✅ Found ${teams.length} teams`);
      if (teams.length > 0) {
        console.log('   📊 Sample team:');
        console.log('   ', JSON.stringify(teams[0], null, 2));
        console.log(`   🔑 Columns: ${Object.keys(teams[0]).join(', ')}`);
      }
    }

    // Query projects table
    console.log('\n📋 PROJECTS TABLE:');
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .limit(10);

    if (projectsError) {
      console.log(`   ❌ Error: ${projectsError.message}`);
    } else {
      console.log(`   ✅ Found ${projects.length} projects`);
      if (projects.length > 0) {
        console.log('   📊 Sample project:');
        console.log('   ', JSON.stringify(projects[0], null, 2));
        console.log(`   🔑 Columns: ${Object.keys(projects[0]).join(', ')}`);
        
        // Check for terminology columns
        const columns = Object.keys(projects[0]);
        const terminologyColumns = columns.filter(col => 
          col.includes('alliance') || 
          col.includes('venture') || 
          col.includes('studio') || 
          col.includes('project')
        );
        console.log(`   🎯 Terminology columns: ${terminologyColumns.join(', ')}`);
      }
    }

    // Query tasks table
    console.log('\n📋 TASKS TABLE:');
    const { data: tasks, error: tasksError } = await supabase
      .from('tasks')
      .select('*')
      .limit(10);

    if (tasksError) {
      console.log(`   ❌ Error: ${tasksError.message}`);
    } else {
      console.log(`   ✅ Found ${tasks.length} tasks`);
      if (tasks.length > 0) {
        console.log('   📊 Sample task:');
        console.log('   ', JSON.stringify(tasks[0], null, 2));
        console.log(`   🔑 Columns: ${Object.keys(tasks[0]).join(', ')}`);
        
        // Check for terminology columns
        const columns = Object.keys(tasks[0]);
        const terminologyColumns = columns.filter(col => 
          col.includes('quest') || 
          col.includes('mission') || 
          col.includes('category')
        );
        console.log(`   🎯 Terminology columns: ${terminologyColumns.join(', ')}`);
      }
    }

    // Query team_members table
    console.log('\n📋 TEAM_MEMBERS TABLE:');
    const { data: members, error: membersError } = await supabase
      .from('team_members')
      .select('*')
      .limit(10);

    if (membersError) {
      console.log(`   ❌ Error: ${membersError.message}`);
    } else {
      console.log(`   ✅ Found ${members.length} team members`);
      if (members.length > 0) {
        console.log('   📊 Sample team member:');
        console.log('   ', JSON.stringify(members[0], null, 2));
        console.log(`   🔑 Columns: ${Object.keys(members[0]).join(', ')}`);
        
        // Check for collaboration columns
        const columns = Object.keys(members[0]);
        const collaborationColumns = columns.filter(col => 
          col.includes('collaboration') || 
          col.includes('engagement') || 
          col.includes('specialization')
        );
        console.log(`   🎯 Collaboration columns: ${collaborationColumns.join(', ')}`);
      }
    }

    // Query users table
    console.log('\n📋 USERS TABLE:');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, display_name, created_at')
      .limit(5);

    if (usersError) {
      console.log(`   ❌ Error: ${usersError.message}`);
    } else {
      console.log(`   ✅ Found ${users.length} users`);
      if (users.length > 0) {
        console.log('   📊 Sample users:');
        users.forEach(user => {
          console.log(`   - ${user.display_name || user.email} (${user.id})`);
        });
      }
    }

    // Check for existing terminology tables
    console.log('\n📋 CHECKING FOR TERMINOLOGY TABLES:');
    
    const terminologyTables = [
      'alliance_invitations',
      'studio_invitations',
      'alliance_preferences', 
      'studio_preferences',
      'user_quests',
      'user_missions'
    ];

    for (const tableName of terminologyTables) {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);

      if (error) {
        console.log(`   ❌ ${tableName}: ${error.message}`);
      } else {
        console.log(`   ✅ ${tableName}: exists with ${data.length} records`);
        if (data.length > 0) {
          console.log(`      Columns: ${Object.keys(data[0]).join(', ')}`);
        }
      }
    }

    // Get table counts
    console.log('\n📊 TABLE RECORD COUNTS:');
    const tablesToCount = ['teams', 'projects', 'tasks', 'team_members', 'users'];
    
    for (const tableName of tablesToCount) {
      const { count, error } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });

      if (error) {
        console.log(`   ❌ ${tableName}: ${error.message}`);
      } else {
        console.log(`   📊 ${tableName}: ${count} records`);
      }
    }

    console.log('\n🎯 TERMINOLOGY ANALYSIS:');
    console.log('=====================================');
    
    // Analyze what terminology is currently in use
    if (projects && projects.length > 0) {
      const projectColumns = Object.keys(projects[0]);
      
      console.log('📋 Projects table analysis:');
      if (projectColumns.includes('venture_type')) {
        console.log('   🔍 venture_type column exists');
        const ventureTypes = [...new Set(projects.map(p => p.venture_type).filter(Boolean))];
        console.log(`   📊 Venture types in use: ${ventureTypes.join(', ')}`);
      }
      
      if (projectColumns.includes('project_type')) {
        console.log('   🔍 project_type column exists');
        const projectTypes = [...new Set(projects.map(p => p.project_type).filter(Boolean))];
        console.log(`   📊 Project types in use: ${projectTypes.join(', ')}`);
      }
      
      if (projectColumns.includes('alliance_id')) {
        console.log('   🔍 alliance_id column exists');
        const allianceIds = projects.filter(p => p.alliance_id).length;
        console.log(`   📊 Projects with alliance_id: ${allianceIds}`);
      }
      
      if (projectColumns.includes('team_id')) {
        console.log('   🔍 team_id column exists');
        const teamIds = projects.filter(p => p.team_id).length;
        console.log(`   📊 Projects with team_id: ${teamIds}`);
      }
    }

    if (teams && teams.length > 0) {
      const teamColumns = Object.keys(teams[0]);
      
      console.log('\n📋 Teams table analysis:');
      if (teamColumns.includes('alliance_type')) {
        console.log('   🔍 alliance_type column exists');
        const allianceTypes = [...new Set(teams.map(t => t.alliance_type).filter(Boolean))];
        console.log(`   📊 Alliance types in use: ${allianceTypes.join(', ')}`);
      }
      
      if (teamColumns.includes('studio_type')) {
        console.log('   🔍 studio_type column exists');
        const studioTypes = [...new Set(teams.map(t => t.studio_type).filter(Boolean))];
        console.log(`   📊 Studio types in use: ${studioTypes.join(', ')}`);
      }
    }

  } catch (error) {
    console.error('\n❌ Database query failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Test database connection first
async function testConnection() {
  console.log('🔌 Testing database connection...');
  
  try {
    const { data, error } = await supabase
      .from('teams')
      .select('id')
      .limit(1);

    if (error) {
      throw error;
    }

    console.log('✅ Database connection successful\n');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

// Main execution
async function main() {
  const connected = await testConnection();
  if (!connected) {
    process.exit(1);
  }

  await queryDatabase();
}

// Handle command line execution
main().catch(console.error);

#!/usr/bin/env node

/**
 * Quick Schema Check
 * Simple script to check current database structure for terminology updates
 */

import { createClient } from '@supabase/supabase-js';

// Use the known Supabase URL from .env.example
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';

// You'll need to provide the anon key as a command line argument
const supabaseAnonKey = process.argv[2];

if (!supabaseAnonKey) {
  console.log('❌ Usage: node quick-schema-check.js <supabase_anon_key>');
  console.log('Get your anon key from: https://supabase.com/dashboard/project/hqqlrrqvjcetoxbdjgzx/settings/api');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function quickSchemaCheck() {
  console.log('🔍 Quick Database Schema Check...');
  console.log('=====================================\n');

  try {
    // Check teams table structure
    console.log('📋 Checking TEAMS table...');
    const { data: teamsData, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .limit(1);

    if (teamsError) {
      console.log(`   ❌ Error: ${teamsError.message}`);
    } else {
      console.log('   ✅ Teams table accessible');
      if (teamsData && teamsData.length > 0) {
        const columns = Object.keys(teamsData[0]);
        console.log(`   📊 Columns: ${columns.join(', ')}`);
        
        // Check for terminology columns
        const allianceColumns = columns.filter(col => col.includes('alliance'));
        const studioColumns = columns.filter(col => col.includes('studio'));
        
        if (allianceColumns.length > 0) {
          console.log(`   🎯 Alliance columns: ${allianceColumns.join(', ')}`);
        }
        if (studioColumns.length > 0) {
          console.log(`   🎯 Studio columns: ${studioColumns.join(', ')}`);
        }
      }
    }

    // Check projects table structure
    console.log('\n📋 Checking PROJECTS table...');
    const { data: projectsData, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .limit(1);

    if (projectsError) {
      console.log(`   ❌ Error: ${projectsError.message}`);
    } else {
      console.log('   ✅ Projects table accessible');
      if (projectsData && projectsData.length > 0) {
        const columns = Object.keys(projectsData[0]);
        console.log(`   📊 Columns: ${columns.join(', ')}`);
        
        // Check for terminology columns
        const ventureColumns = columns.filter(col => col.includes('venture'));
        const projectColumns = columns.filter(col => col.includes('project'));
        const allianceColumns = columns.filter(col => col.includes('alliance'));
        const studioColumns = columns.filter(col => col.includes('studio'));
        
        if (ventureColumns.length > 0) {
          console.log(`   🎯 Venture columns: ${ventureColumns.join(', ')}`);
        }
        if (projectColumns.length > 0) {
          console.log(`   🎯 Project columns: ${projectColumns.join(', ')}`);
        }
        if (allianceColumns.length > 0) {
          console.log(`   🎯 Alliance columns: ${allianceColumns.join(', ')}`);
        }
        if (studioColumns.length > 0) {
          console.log(`   🎯 Studio columns: ${studioColumns.join(', ')}`);
        }
      }
    }

    // Check tasks table structure
    console.log('\n📋 Checking TASKS table...');
    const { data: tasksData, error: tasksError } = await supabase
      .from('tasks')
      .select('*')
      .limit(1);

    if (tasksError) {
      console.log(`   ❌ Error: ${tasksError.message}`);
    } else {
      console.log('   ✅ Tasks table accessible');
      if (tasksData && tasksData.length > 0) {
        const columns = Object.keys(tasksData[0]);
        console.log(`   📊 Columns: ${columns.join(', ')}`);
        
        // Check for terminology columns
        const questColumns = columns.filter(col => col.includes('quest'));
        const missionColumns = columns.filter(col => col.includes('mission'));
        
        if (questColumns.length > 0) {
          console.log(`   🎯 Quest columns: ${questColumns.join(', ')}`);
        }
        if (missionColumns.length > 0) {
          console.log(`   🎯 Mission columns: ${missionColumns.join(', ')}`);
        }
      }
    }

    // Check for terminology-related tables
    console.log('\n📋 Checking for terminology-related tables...');
    
    const tablesToCheck = [
      'alliance_invitations',
      'studio_invitations', 
      'alliance_preferences',
      'studio_preferences',
      'user_quests',
      'user_missions'
    ];

    for (const tableName of tablesToCheck) {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);

      if (error) {
        console.log(`   ❌ ${tableName}: ${error.message}`);
      } else {
        console.log(`   ✅ ${tableName}: exists and accessible`);
        if (data && data.length > 0) {
          const columns = Object.keys(data[0]);
          console.log(`      📊 Columns: ${columns.join(', ')}`);
        }
      }
    }

    console.log('\n📝 Migration Recommendations:');
    console.log('=====================================');
    console.log('Based on the schema check above:');
    console.log('1. If both old and new columns exist, we need data migration');
    console.log('2. If only old columns exist, we can rename them');
    console.log('3. If only new columns exist, migration is already done');
    console.log('4. If neither exist, we need to add new columns');

  } catch (error) {
    console.error('\n❌ Schema check failed:', error.message);
    process.exit(1);
  }
}

// Main execution
quickSchemaCheck().catch(console.error);

/**
 * Agreement System Demonstration
 * 
 * This script demonstrates the complete agreement system by creating:
 * - Various alliances across different industries
 * - Ventures with different collaboration types
 * - Generated agreements with different templates
 * - Performance evaluations and financial calculations
 */

// Import all agreement system components
import { agreementTemplateSystem } from '../utils/agreement/agreementTemplateSystem.js';
import { revenueCalculationEngine } from '../utils/agreement/revenueCalculationEngine.js';
import { ipRightsFramework } from '../utils/agreement/ipRightsFramework.js';
import { performanceStandardsFramework } from '../utils/agreement/performanceStandardsFramework.js';
import { terminationBreachHandling } from '../utils/agreement/terminationBreachHandling.js';
import { advancedFinancialFeatures } from '../utils/agreement/advancedFinancialFeatures.js';

// ============================================================================
// DEMO SCENARIO 1: TECH STARTUP COLLABORATION
// ============================================================================

console.log('🚀 AGREEMENT SYSTEM DEMONSTRATION\n');
console.log('=' .repeat(80));

// Create Tech Alliance
const techAlliance = {
  id: 'alliance_tech_demo',
  name: 'InnovateTech Alliance',
  description: 'Collaborative alliance for innovative SaaS development projects',
  industry: 'TECHNOLOGY',
  collaborationType: 'software_development',
  revenueModel: 'PERCENTAGE_SPLIT',
  currency: 'USD',
  ipOwnershipModel: 'CO_OWNERSHIP',
  jurisdiction: 'US'
};

console.log('1️⃣ TECH ALLIANCE CREATED');
console.log(`   Name: ${techAlliance.name}`);
console.log(`   Industry: ${techAlliance.industry}`);
console.log(`   Revenue Model: ${techAlliance.revenueModel}`);
console.log(`   IP Model: ${techAlliance.ipOwnershipModel}\n`);

// Create Software Venture
const softwareVenture = {
  id: 'venture_tech_demo',
  allianceId: techAlliance.id,
  name: 'CloudSync Pro',
  description: 'Enterprise cloud synchronization platform with real-time collaboration features',
  scope: 'Full-stack development including frontend, backend, API, and mobile apps',
  
  contributors: [
    {
      id: 'contrib_001',
      email: '<EMAIL>',
      role: 'Lead Developer',
      responsibilities: 'Backend architecture, API development, database design',
      revenueShare: 40,
      ipRights: 'co_owner'
    },
    {
      id: 'contrib_002', 
      email: '<EMAIL>',
      role: 'UX/UI Designer',
      responsibilities: 'User interface design, user experience optimization, prototyping',
      revenueShare: 30,
      ipRights: 'co_owner'
    },
    {
      id: 'contrib_003',
      email: '<EMAIL>', 
      role: 'DevOps Engineer',
      responsibilities: 'Infrastructure setup, CI/CD, deployment automation, monitoring',
      revenueShare: 30,
      ipRights: 'contributor'
    }
  ],

  milestones: [
    {
      id: 'milestone_001',
      title: 'MVP Development',
      description: 'Core functionality with basic file sync and user management',
      dueDate: '2024-03-15',
      paymentPercentage: 30
    },
    {
      id: 'milestone_002', 
      title: 'Beta Release',
      description: 'Feature-complete beta with advanced collaboration tools',
      dueDate: '2024-05-15',
      paymentPercentage: 40
    },
    {
      id: 'milestone_003',
      title: 'Production Launch',
      description: 'Production-ready platform with enterprise features',
      dueDate: '2024-07-15',
      paymentPercentage: 30
    }
  ]
};

console.log('2️⃣ SOFTWARE PROJECT CREATED');
console.log(`   Name: ${softwareVenture.name}`);
console.log(`   Contributors: ${softwareVenture.contributors.length}`);
console.log(`   Milestones: ${softwareVenture.milestones.length}`);
console.log(`   Total Revenue Share: ${softwareVenture.contributors.reduce((sum, c) => sum + c.revenueShare, 0)}%\n`);

// Generate Agreement
const techAgreement = agreementTemplateSystem.generateAgreement({
  templateType: 'software_development',
  industry: 'TECHNOLOGY',
  project: softwareVenture,
  studio: techAlliance,
  variables: {
    effectiveDate: '2024-01-15',
    governingLaw: 'Delaware',
    confidentialityPeriod: 24
  }
});

console.log('3️⃣ TECH AGREEMENT GENERATED');
console.log(`   Template: ${techAgreement.templateType}`);
console.log(`   Parties: ${techAgreement.parties.length}`);
console.log(`   Content Length: ${techAgreement.content.length} characters`);
console.log(`   Revenue Model: ${techAgreement.revenueSharing.model}\n`);

// ============================================================================
// DEMO SCENARIO 2: CREATIVE MUSIC COLLABORATION
// ============================================================================

console.log('=' .repeat(80));

// Create Music Alliance
const musicAlliance = {
  id: 'alliance_music_demo',
  name: 'Harmony Collective',
  description: 'Collaborative music production alliance for independent artists',
  industry: 'CREATIVE',
  collaborationType: 'music_production',
  revenueModel: 'WATERFALL_DISTRIBUTION',
  currency: 'USD',
  ipOwnershipModel: 'RETAINED_RIGHTS',
  jurisdiction: 'US'
};

console.log('4️⃣ MUSIC ALLIANCE CREATED');
console.log(`   Name: ${musicAlliance.name}`);
console.log(`   Industry: ${musicAlliance.industry}`);
console.log(`   Revenue Model: ${musicAlliance.revenueModel}`);
console.log(`   IP Model: ${musicAlliance.ipOwnershipModel}\n`);

// Create Album Venture
const albumVenture = {
  id: 'venture_music_demo',
  allianceId: musicAlliance.id,
  name: 'Midnight Sessions Album',
  description: 'Collaborative album featuring indie rock and electronic fusion',
  scope: 'Full album production including songwriting, recording, mixing, mastering, and distribution',

  contributors: [
    {
      id: 'contrib_music_001',
      email: '<EMAIL>',
      role: 'Lead Songwriter/Vocalist',
      responsibilities: 'Primary songwriting, lead vocals, melody composition',
      revenueShare: 35,
      ipRights: 'retained'
    },
    {
      id: 'contrib_music_002',
      email: '<EMAIL>', 
      role: 'Producer/Engineer',
      responsibilities: 'Music production, recording, mixing, mastering',
      revenueShare: 30,
      ipRights: 'work_for_hire'
    },
    {
      id: 'contrib_music_003',
      email: '<EMAIL>',
      role: 'Instrumentalist',
      responsibilities: 'Guitar, bass, keyboard performances and arrangements',
      revenueShare: 20,
      ipRights: 'contributor'
    },
    {
      id: 'contrib_music_004',
      email: '<EMAIL>',
      role: 'Marketing/Distribution',
      responsibilities: 'Promotion, distribution, playlist placement, social media',
      revenueShare: 15,
      ipRights: 'contributor'
    }
  ]
};

console.log('5️⃣ MUSIC PROJECT CREATED');
console.log(`   Name: ${albumVenture.name}`);
console.log(`   Contributors: ${albumVenture.contributors.length}`);
console.log(`   Total Revenue Share: ${albumVenture.contributors.reduce((sum, c) => sum + c.revenueShare, 0)}%\n`);

// Generate Music Agreement
const musicAgreement = agreementTemplateSystem.generateAgreement({
  templateType: 'music_production',
  industry: 'CREATIVE',
  project: albumVenture,
  studio: musicAlliance,
  variables: {
    effectiveDate: '2024-01-01',
    governingLaw: 'California',
    confidentialityPeriod: 36
  }
});

console.log('6️⃣ MUSIC AGREEMENT GENERATED');
console.log(`   Template: ${musicAgreement.templateType}`);
console.log(`   Parties: ${musicAgreement.parties.length}`);
console.log(`   Content Length: ${musicAgreement.content.length} characters`);
console.log(`   IP Model: ${musicAgreement.ipRights.ownershipModel}\n`);

// ============================================================================
// DEMO SCENARIO 3: REVENUE CALCULATIONS
// ============================================================================

console.log('=' .repeat(80));

// Calculate Revenue Distribution for Tech Venture
const techRevenueCalc = revenueCalculationEngine.calculatePercentageSplit({
  totalRevenue: 250000,
  shares: {
    '<EMAIL>': 40,
    '<EMAIL>': 30,
    '<EMAIL>': 30
  }
});

console.log('7️⃣ TECH REVENUE CALCULATION');
console.log(`   Total Revenue: $${techRevenueCalc.totalRevenue.toLocaleString()}`);
console.log(`   Alice (40%): $${techRevenueCalc.distribution['<EMAIL>'].toLocaleString()}`);
console.log(`   Bob (30%): $${techRevenueCalc.distribution['<EMAIL>'].toLocaleString()}`);
console.log(`   Carol (30%): $${techRevenueCalc.distribution['<EMAIL>'].toLocaleString()}\n`);

// Calculate Waterfall Distribution for Music Venture
const musicRevenueCalc = revenueCalculationEngine.calculateWaterfallDistribution({
  totalRevenue: 100000,
  recoupmentOrder: [
    { type: 'production_costs', amount: 20000 },
    { type: 'marketing_costs', amount: 15000 },
    { type: 'distribution_costs', amount: 5000 }
  ],
  profitSharing: {
    '<EMAIL>': 35,
    '<EMAIL>': 30,
    '<EMAIL>': 20,
    '<EMAIL>': 15
  }
});

console.log('8️⃣ MUSIC WATERFALL CALCULATION');
console.log(`   Total Revenue: $${musicRevenueCalc.totalRevenue.toLocaleString()}`);
console.log(`   Total Recoupment: $${musicRevenueCalc.totalRecoupment.toLocaleString()}`);
console.log(`   Net Profit: $${musicRevenueCalc.netProfit.toLocaleString()}`);
console.log(`   Emma (35%): $${musicRevenueCalc.finalDistribution['<EMAIL>'].toLocaleString()}`);
console.log(`   Frank (30%): $${musicRevenueCalc.finalDistribution['<EMAIL>'].toLocaleString()}\n`);

// ============================================================================
// DEMO SCENARIO 4: PERFORMANCE EVALUATION
// ============================================================================

console.log('=' .repeat(80));

// Define Performance Standard
const performanceStandard = performanceStandardsFramework.definePerformanceStandard({
  name: 'Code Quality Standard',
  description: 'Minimum code quality requirements for all software contributions',
  type: 'QUALITY',
  ventureId: softwareVenture.id,
  measurementMethod: 'PERCENTAGE',
  measurementCriteria: {
    qualityFactors: ['Code coverage', 'Documentation', 'Code review approval'],
    acceptanceCriteria: ['Minimum 80% test coverage', 'All functions documented', 'Peer review passed']
  },
  thresholds: {
    exceptional: 95,
    exceeds: 85,
    meets: 75,
    below: 65,
    minimum: 50
  }
});

console.log('9️⃣ PERFORMANCE STANDARD DEFINED');
console.log(`   Name: ${performanceStandard.name}`);
console.log(`   Type: ${performanceStandard.type}`);
console.log(`   Meets Threshold: ${performanceStandard.thresholds.meets}%\n`);

// Conduct Performance Evaluation
const evaluation = performanceStandardsFramework.conductEvaluation({
  standardId: performanceStandard.id,
  contributorId: 'contrib_001',
  evaluatorId: 'lead_developer',
  measurements: {
    codeQuality: { value: 88, weight: 1.0 },
    documentation: { value: 92, weight: 0.8 },
    testCoverage: { value: 85, weight: 1.2 }
  },
  strengths: ['Excellent code structure', 'Comprehensive testing'],
  improvementAreas: ['Documentation could be more detailed']
});

console.log('🔟 PERFORMANCE EVALUATION COMPLETED');
console.log(`   Overall Score: ${evaluation.overallScore.toFixed(1)}%`);
console.log(`   Performance Level: ${evaluation.performanceLevel}`);
console.log(`   Strengths: ${evaluation.strengths.length}`);
console.log(`   Improvement Areas: ${evaluation.improvementAreas.length}\n`);

// ============================================================================
// DEMO SCENARIO 5: ADVANCED FINANCIAL FEATURES
// ============================================================================

console.log('=' .repeat(80));

// Setup Escrow Account
const escrowAccount = advancedFinancialFeatures.setupEscrowAccount({
  ventureId: softwareVenture.id,
  escrowType: 'MILESTONE_BASED',
  totalAmount: 150000,
  currency: 'USD',
  depositor: '<EMAIL>',
  beneficiary: '<EMAIL>',
  milestones: softwareVenture.milestones,
  releaseConditions: {
    milestoneCompletion: true,
    approvalRequired: true,
    disputePeriod: 7
  }
});

console.log('1️⃣1️⃣ ESCROW ACCOUNT SETUP');
console.log(`   Account: ${escrowAccount.accountNumber}`);
console.log(`   Total Amount: $${escrowAccount.totalAmount.toLocaleString()}`);
console.log(`   Milestone Payments: ${escrowAccount.milestonePayments.length}`);
console.log(`   Status: ${escrowAccount.status}\n`);

// Calculate Tax Obligations
const taxCalculation = advancedFinancialFeatures.calculateTaxObligations({
  contributorId: 'contrib_001',
  grossIncome: 100000,
  currency: 'USD',
  taxJurisdiction: 'US',
  stateProvince: 'CA',
  contributorType: 'independent_contractor'
});

console.log('1️⃣2️⃣ TAX CALCULATION COMPLETED');
console.log(`   Gross Income: $${taxCalculation.grossIncome.toLocaleString()}`);
console.log(`   Total Tax Liability: $${taxCalculation.totalTaxLiability.toLocaleString()}`);
console.log(`   Effective Tax Rate: ${taxCalculation.effectiveTaxRate.toFixed(1)}%`);
console.log(`   Withholding Required: ${taxCalculation.withholdingRequired.required}\n`);

// ============================================================================
// DEMO SUMMARY
// ============================================================================

console.log('=' .repeat(80));
console.log('🎉 AGREEMENT SYSTEM DEMONSTRATION COMPLETE!\n');

console.log('📊 SUMMARY OF GENERATED CONTENT:');
console.log(`   • ${2} Alliances Created (Tech & Music)`);
console.log(`   • ${2} Ventures Established (Software & Album)`);
console.log(`   • ${2} Agreements Generated (${techAgreement.content.length + musicAgreement.content.length} total characters)`);
console.log(`   • ${7} Contributors Configured`);
console.log(`   • ${3} Milestones Defined`);
console.log(`   • ${2} Revenue Calculations Performed`);
console.log(`   • ${1} Performance Evaluation Conducted`);
console.log(`   • ${1} Escrow Account Established`);
console.log(`   • ${1} Tax Calculation Completed\n`);

console.log('🚀 The agreement system successfully demonstrates:');
console.log('   ✅ Multi-industry support (Technology, Creative, Service)');
console.log('   ✅ Various revenue models (Percentage, Waterfall, Commission)');
console.log('   ✅ Comprehensive IP rights management');
console.log('   ✅ Performance tracking and evaluation');
console.log('   ✅ Advanced financial features');
console.log('   ✅ Professional agreement generation');
console.log('   ✅ Complete workflow integration\n');

console.log('=' .repeat(80));

// Export demo function for use in other contexts
export function runAgreementSystemDemo() {
  return {
    techAlliance,
    softwareVenture,
    techAgreement,
    musicAlliance,
    albumVenture,
    musicAgreement,
    techRevenueCalc,
    musicRevenueCalc,
    performanceStandard,
    evaluation,
    escrowAccount,
    taxCalculation
  };
}

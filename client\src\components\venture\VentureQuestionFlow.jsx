import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@heroui/react';
import VentureQuestionStep from './VentureQuestionStep';

/**
 * VentureQuestionFlow Component
 * 
 * Manages the 8-question immersive flow for project creation
 * Each question is displayed full-screen with minimal UI
 * Follows the wireframe specifications exactly
 */
const VentureQuestionFlow = ({ 
  initialData = {}, 
  onComplete, 
  onBack, 
  onCancel 
}) => {
  const [currentQuestion, setCurrentQuestion] = useState(1);
  const [answers, setAnswers] = useState({
    projectCategory: '',
    projectSubtype: '',
    targetAudience: '',
    timeline: '',
    budget: '',
    successMetrics: '',
    ventureName: '',
    ventureDescription: '',
    ventureIcon: '🚀',
    ventureTags: [],
    initialTeamRoles: {},
    ...initialData
  });

  const totalQuestions = 5; // Reduced from 8 to 5 for better cohesion

  // Redesigned question flow - focused on agreement generation
  // Each question directly maps to essential agreement components
  const questions = [
    {
      id: 1,
      title: "What's your project?",
      subtitle: "This defines your project scope and IP ownership",
      type: "venture_definition",
      agreementSection: "Project Definition & Intellectual Property",
      fields: [
        {
          name: "ventureName",
          label: "Project Name",
          type: "text",
          placeholder: "TaskMaster Pro",
          required: true
        },
        {
          name: "projectCategory",
          label: "What are you building?",
          type: "selection",
          options: [
            { value: "software", icon: "💻", title: "Software/App" },
            { value: "creative", icon: "🎨", title: "Creative Work" },
            { value: "business", icon: "🤝", title: "Service" },
            { value: "physical", icon: "📦", title: "Product" }
          ]
        },
        {
          name: "ventureDescription",
          label: "Brief description",
          type: "textarea",
          placeholder: "A mobile app that helps teams manage tasks...",
          required: true
        }
      ]
    },
    {
      id: 2,
      title: "How will you work together?",
      subtitle: "This sets up your collaboration structure and roles",
      type: "collaboration_structure",
      agreementSection: "Team Structure & Responsibilities",
      fields: [
        {
          name: "timeline",
          label: "Project timeline",
          type: "selection",
          options: [
            { value: "quick", icon: "⚡", title: "1-4 weeks" },
            { value: "short", icon: "🎯", title: "1-3 months" },
            { value: "medium", icon: "🏗️", title: "3-12 months" },
            { value: "long", icon: "🌟", title: "1+ years" }
          ]
        },
        {
          name: "targetAudience",
          label: "Who is this for?",
          type: "selection",
          options: [
            { value: "general", icon: "👥", title: "General Public" },
            { value: "business", icon: "🏢", title: "Businesses" },
            { value: "internal", icon: "👤", title: "Internal Use" }
          ]
        },
        {
          name: "initialTeamRoles",
          label: "Team setup",
          type: "team_quick_setup",
          description: "We'll assign detailed roles later"
        }
      ]
    },
    {
      id: 3,
      title: "How will you fund this?",
      subtitle: "This determines your revenue sharing model",
      type: "financial_structure",
      agreementSection: "Financial Terms & Revenue Sharing",
      fields: [
        {
          name: "budget",
          label: "Budget approach",
          type: "selection",
          options: [
            { value: "funded", icon: "💰", title: "We have funding" },
            { value: "bootstrapped", icon: "🎯", title: "Self-funded" },
            { value: "sweat_equity", icon: "🤝", title: "Time investment only" },
            { value: "revenue_first", icon: "📈", title: "Make money first" }
          ]
        },
        {
          name: "revenueSharing",
          label: "How should profits be shared?",
          type: "selection",
          options: [
            { value: "equal", icon: "⚖️", title: "Equal split" },
            { value: "contribution", icon: "📊", title: "Based on contribution" },
            { value: "investment", icon: "💼", title: "Based on investment" },
            { value: "custom", icon: "🔧", title: "Custom arrangement" }
          ]
        }
      ]
    },
    {
      id: 4,
      title: "What are your goals?",
      subtitle: "This sets up success metrics and milestones",
      type: "success_metrics",
      agreementSection: "Success Criteria & Milestones",
      fields: [
        {
          name: "successMetrics",
          label: "How will you measure success?",
          type: "selection",
          options: [
            { value: "revenue", icon: "💰", title: "Revenue goals" },
            { value: "users", icon: "👥", title: "User growth" },
            { value: "features", icon: "🎯", title: "Feature completion" },
            { value: "satisfaction", icon: "😊", title: "Learning & fun" }
          ]
        },
        {
          name: "ventureTags",
          label: "Project tags",
          type: "tag_input",
          placeholder: "#mobile #productivity #startup"
        },
        {
          name: "ventureIcon",
          label: "Choose an icon",
          type: "icon_picker",
          options: ["📱", "💻", "🎯", "🚀", "⚡", "🔧", "💡", "🌟"]
        }
      ]
    },
    {
      id: 5,
      title: "Legal & Protection",
      subtitle: "This generates your collaboration agreement",
      type: "legal_framework",
      agreementSection: "Legal Terms & Dispute Resolution",
      fields: [
        {
          name: "agreementType",
          label: "What type of agreement do you need?",
          type: "selection",
          options: [
            { value: "simple", icon: "📄", title: "Simple collaboration" },
            { value: "business", icon: "🏢", title: "Business partnership" },
            { value: "startup", icon: "🚀", title: "Startup project" }
          ]
        },
        {
          name: "ipOwnership",
          label: "Who owns the intellectual property?",
          type: "selection",
          options: [
            { value: "shared", icon: "🤝", title: "Shared ownership" },
            { value: "lead", icon: "👑", title: "Project lead owns" },
            { value: "company", icon: "🏢", title: "Company/Studio owns" }
          ]
        },
        {
          name: "disputeResolution",
          label: "How should disputes be resolved?",
          type: "selection",
          options: [
            { value: "discussion", icon: "💬", title: "Team discussion" },
            { value: "mediation", icon: "⚖️", title: "Mediation" },
            { value: "arbitration", icon: "🏛️", title: "Arbitration" }
          ]
        }
      ]
    }
  ];

  // Handle answer selection - updated for new question structure
  const handleAnswer = (questionId, answer) => {
    setAnswers(prev => {
      const updated = { ...prev };

      // Handle different question types - now all questions use field-based structure
      if (typeof answer === 'object' && answer !== null) {
        // Multiple fields from a single question
        Object.assign(updated, answer);
      } else {
        // Single field answer - map to appropriate field based on question
        const question = questions.find(q => q.id === questionId);
        if (question?.fields?.length === 1) {
          const fieldName = question.fields[0].name;
          updated[fieldName] = answer;
        }
      }

      return updated;
    });
  };

  // Navigate to next question
  const handleNext = () => {
    if (currentQuestion < totalQuestions) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      // Complete the flow
      onComplete(answers);
    }
  };

  // Navigate to previous question
  const handlePrevious = () => {
    if (currentQuestion > 1) {
      setCurrentQuestion(currentQuestion - 1);
    } else {
      onBack();
    }
  };

  // Get current question data
  const getCurrentQuestion = () => {
    const question = questions.find(q => q.id === currentQuestion);
    
    // Check if question has a condition
    if (question?.condition && !question.condition(answers)) {
      // Skip this question
      if (currentQuestion < totalQuestions) {
        setCurrentQuestion(currentQuestion + 1);
        return null;
      }
    }
    
    return question;
  };

  const currentQuestionData = getCurrentQuestion();
  
  if (!currentQuestionData) {
    return null; // Will trigger useEffect to move to next question
  }

  // Check if current question is answered - updated for new structure
  const isAnswered = () => {
    const question = questions.find(q => q.id === currentQuestion);
    if (!question?.fields) return false;

    // Check if all required fields are answered
    return question.fields.every(field => {
      if (!field.required) return true; // Optional fields don't block progress
      return !!answers[field.name];
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex items-center justify-center p-4">
      {/* Exit button */}
      {onCancel && (
        <div className="absolute top-6 right-6 z-10">
          <Button
            variant="light"
            size="lg"
            onPress={onCancel}
            isIconOnly
            className="text-white hover:bg-white/10"
          >
            <i className="bi bi-x-lg text-2xl"></i>
          </Button>
        </div>
      )}

      {/* Back button */}
      <div className="absolute top-6 left-6 z-10">
        <Button
          variant="light"
          size="lg"
          onPress={handlePrevious}
          className="text-white hover:bg-white/10"
        >
          <i className="bi bi-arrow-left text-xl mr-2"></i>
          Back
        </Button>
      </div>

      {/* Step indicator */}
      <div className="absolute top-6 right-20 z-10">
        <span className="text-white/80 text-lg">
          Step {currentQuestion}/{totalQuestions}
        </span>
      </div>

      {/* Question content */}
      <div className="max-w-4xl mx-auto w-full">
        <AnimatePresence mode="wait">
          <VentureQuestionStep
            key={currentQuestion}
            question={currentQuestionData}
            answer={answers}
            onAnswer={handleAnswer}
            onNext={handleNext}
            isAnswered={isAnswered()}
          />
        </AnimatePresence>
      </div>

      {/* Progress dots */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="flex space-x-2">
          {Array.from({ length: totalQuestions }, (_, i) => (
            <div
              key={i}
              className={`w-3 h-3 rounded-full transition-colors ${
                i + 1 === currentQuestion
                  ? 'bg-white'
                  : i + 1 < currentQuestion
                  ? 'bg-white/60'
                  : 'bg-white/20'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default VentureQuestionFlow;

/**
 * Comprehensive End-to-End Agreement Testing Framework
 * 
 * This test suite validates:
 * 1. 1:1 recreation of lawyer-approved template with 99% minimum accuracy
 * 2. Variable-based agreement generation for various business types
 * 3. Cross-contamination prevention between different projects
 * 4. Comprehensive reporting and accuracy tracking
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import the agreement generation systems
import { NewAgreementGenerator } from '../client/src/utils/agreement/newAgreementGenerator.js';
import { agreementTemplateSystem } from '../client/src/utils/agreement/agreementTemplateSystem.js';

console.log('🎯 COMPREHENSIVE AGREEMENT E2E TESTING FRAMEWORK');
console.log('=' .repeat(70));
console.log('Testing both 1:1 template recreation and variable-based generation');
console.log('Target: 99% minimum accuracy for lawyer-approved template recreation');
console.log('=' .repeat(70));

/**
 * Test Configuration
 */
const TEST_CONFIG = {
  // Accuracy thresholds
  MINIMUM_1TO1_ACCURACY: 99,
  MINIMUM_VARIABLE_ACCURACY: 95,
  ZERO_CONTAMINATION_REQUIRED: true,
  
  // Output paths
  OUTPUT_DIR: path.join(__dirname, 'agreement-test-results'),
  REPORT_FILE: 'comprehensive-agreement-report.json',
  
  // Test scenarios
  ENABLE_1TO1_RECREATION: true,
  ENABLE_VARIABLE_TESTING: true,
  ENABLE_CONTAMINATION_TESTING: true,
  ENABLE_BUSINESS_SCENARIOS: true
};

/**
 * Test Data: Various Business Scenarios
 */
const TEST_SCENARIOS = {
  // VOTA - Village of The Ages (exact data from lawyer-approved template)
  VOTA_EXACT: {
    name: 'Village of The Ages',
    description: 'A village simulation game where players guide communities through historical progressions and manage resource-based challenges',
    project_type: 'game',
    company_name: 'City of Gamers Inc.',
    address: '1205 43rd Street, Suite B, Orlando, Florida 32839',
    contact_email: '<EMAIL>',
    city: 'Orlando',
    state: 'Florida',
    zip: '32839',
    county: 'Orange',
    legal_entity_type: 'corporation',
    incorporation_state: 'Florida',
    features: 'The game features dynamic challenges based on resource scarcity and natural disasters, creating an engaging gameplay loop.',
    coreFeatures: `1. **Village Building & Management**
   - Resource gathering and management
   - Building placement and construction
   - Population growth and management
   - Cultural evolution systems

2. **Historical Progression**
   - Players guide their community through multiple historical eras
   - Technology tree advancement
   - Cultural and social development
   - Architectural evolution

3. **Resource Management**
   - Dynamic weather systems affecting resources
   - Natural disasters and seasonal challenges
   - Trading systems with neighboring villages
   - Resource scarcity mechanics

4. **Interface Requirements**
   - Intuitive building placement system
   - Resource management dashboard
   - Population statistics and management panels
   - Technology and progression trackers`,
    technicalRequirements: `- Platform: PC (Steam, Epic Games Store)
- Engine: Unreal Engine 5
- Minimum Specs: Standard hardware requirements for the target platforms
- Art Style: Stylized, readable visuals with distinctive aesthetics
- Audio: Atmospheric soundtrack with appropriate sound design
- Version Control: Git-based source control with proper branching strategy`,
    roadmapPhases: `**Phase 1: Core Gameplay Development (Months 1-2)**
- Basic village layout and building system
- Core resource gathering mechanics
- Initial AI for villagers
- Basic UI framework
- First playable prototype with one historical era

**Phase 2: Feature Expansion (Months 2-3)**
- Additional historical eras
- Enhanced resource management systems
- Weather and disaster systems
- Trading mechanics
- Technology progression system

**Phase 3: Polish and Enhancement (Month 4)**
- UI refinement
- Performance optimization
- Additional content (buildings, resources, etc.)
- Balancing and gameplay tuning
- Audio implementation`,
    milestones: [
      {
        title: 'Core Gameplay Development',
        description: 'Basic village layout and building system',
        dueDate: 'Months 1-2'
      },
      {
        title: 'Resource Management System',
        description: 'Implementation of resource scarcity mechanics',
        dueDate: 'Months 3-4'
      },
      {
        title: 'Historical Progression Features',
        description: 'Time-based progression and historical events, architectural evolution through eras',
        dueDate: 'Months 5-6'
      }
    ]
  },

  // Software Development Project
  SOFTWARE_PROJECT: {
    name: 'TaskFlow Pro',
    description: 'A comprehensive project management platform designed for remote teams',
    project_type: 'software',
    company_name: 'Productivity Solutions LLC',
    address: '456 Innovation Drive, Austin, TX 78701',
    contact_email: '<EMAIL>',
    city: 'Austin',
    state: 'Texas',
    zip: '78701',
    county: 'Travis',
    legal_entity_type: 'llc',
    incorporation_state: 'Texas',
    features: 'Advanced collaboration tools with real-time synchronization and AI-powered insights.',
    coreFeatures: `1. **Task Management**
   - Kanban boards and Gantt charts
   - Priority-based task assignment
   - Deadline tracking and notifications
   - Progress visualization

2. **Team Collaboration**
   - Real-time chat and video calls
   - File sharing and version control
   - Comment threads on tasks
   - Team performance analytics

3. **AI Integration**
   - Smart task recommendations
   - Automated progress reporting
   - Predictive timeline adjustments
   - Resource optimization suggestions`,
    technicalRequirements: `- Platform: Web-based (React/Node.js)
- Database: PostgreSQL with Redis caching
- AI/ML: TensorFlow integration
- Hosting: AWS cloud infrastructure
- Mobile: React Native apps for iOS/Android`,
    roadmapPhases: `**Phase 1: Core Platform (Months 1-3)**
- User authentication and workspace setup
- Basic task management functionality
- Team invitation and role management
- File upload and basic collaboration

**Phase 2: Advanced Features (Months 4-6)**
- Real-time collaboration tools
- Advanced reporting and analytics
- Mobile app development
- Third-party integrations

**Phase 3: AI Enhancement (Months 7-8)**
- AI-powered task recommendations
- Predictive analytics implementation
- Performance optimization
- Beta testing and refinement`,
    milestones: [
      {
        title: 'MVP Launch',
        description: 'Core task management and team collaboration features',
        dueDate: 'Month 3'
      },
      {
        title: 'Mobile Apps',
        description: 'iOS and Android applications with full feature parity',
        dueDate: 'Month 6'
      },
      {
        title: 'AI Integration',
        description: 'Smart recommendations and predictive analytics',
        dueDate: 'Month 8'
      }
    ]
  }
};

/**
 * Main Test Runner Class
 */
class ComprehensiveAgreementTester {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      tests: {},
      summary: {},
      recommendations: []
    };
    
    // Ensure output directory exists
    if (!fs.existsSync(TEST_CONFIG.OUTPUT_DIR)) {
      fs.mkdirSync(TEST_CONFIG.OUTPUT_DIR, { recursive: true });
    }
  }

  /**
   * Run all comprehensive tests
   */
  async runAllTests() {
    console.log('\n🚀 Starting comprehensive agreement testing...\n');
    
    try {
      // Test 1: 1:1 Template Recreation
      if (TEST_CONFIG.ENABLE_1TO1_RECREATION) {
        await this.test1to1Recreation();
      }
      
      // Test 2: Variable-Based Generation
      if (TEST_CONFIG.ENABLE_VARIABLE_TESTING) {
        await this.testVariableGeneration();
      }
      
      // Test 3: Cross-Contamination Prevention
      if (TEST_CONFIG.ENABLE_CONTAMINATION_TESTING) {
        await this.testCrossContamination();
      }
      
      // Test 4: Multiple Business Scenarios
      if (TEST_CONFIG.ENABLE_BUSINESS_SCENARIOS) {
        await this.testBusinessScenarios();
      }
      
      // Generate comprehensive report
      await this.generateComprehensiveReport();
      
      console.log('\n✅ All tests completed successfully!');
      return this.results;
      
    } catch (error) {
      console.error('\n❌ Test suite failed:', error);
      this.results.error = error.message;
      throw error;
    }
  }

  /**
   * Test 1: 1:1 Template Recreation
   * Validates that the platform can recreate the exact lawyer-approved template
   */
  async test1to1Recreation() {
    console.log('📋 TEST 1: 1:1 Template Recreation');
    console.log('=' .repeat(50));

    try {
      // Load the lawyer-approved template
      const lawyerTemplatePath = path.join(__dirname, '../client/public/example-cog-contributor-agreement.md');
      const lawyerTemplate = fs.readFileSync(lawyerTemplatePath, 'utf8');

      // Extract the exact VOTA data from the lawyer template
      const votaData = this.extractVOTADataFromTemplate(lawyerTemplate);

      // Generate agreement using the current system
      const generator = new NewAgreementGenerator();
      const templatePath = path.join(__dirname, '../client/public/contributor-agreement-template.md');
      const templateText = fs.readFileSync(templatePath, 'utf8');

      const testOptions = {
        contributors: [{
          id: 'test_contributor',
          email: '[_________________________]',
          name: '[_________________________]',
          address: '_____________________'
        }],
        currentUser: {
          id: 'test_contributor',
          email: '[_________________________]',
          user_metadata: {
            full_name: '[_________________________]'
          }
        },
        fullName: '[_________________________]',
        agreementDate: new Date('2024-01-15')
      };

      const generatedAgreement = await generator.generateAgreement(
        templateText,
        TEST_SCENARIOS.VOTA_EXACT,
        testOptions
      );

      // Save the generated agreement
      const outputPath = path.join(TEST_CONFIG.OUTPUT_DIR, '1to1-recreation-output.md');
      fs.writeFileSync(outputPath, generatedAgreement);

      // Compare with lawyer template
      const accuracy = this.calculateTemplateAccuracy(lawyerTemplate, generatedAgreement);

      this.results.tests['1to1_recreation'] = {
        passed: accuracy >= TEST_CONFIG.MINIMUM_1TO1_ACCURACY,
        accuracy: accuracy,
        threshold: TEST_CONFIG.MINIMUM_1TO1_ACCURACY,
        outputFile: outputPath,
        details: this.getAccuracyDetails(lawyerTemplate, generatedAgreement)
      };

      console.log(`📊 1:1 Recreation Accuracy: ${accuracy}%`);
      console.log(`🎯 Required Threshold: ${TEST_CONFIG.MINIMUM_1TO1_ACCURACY}%`);
      console.log(`${accuracy >= TEST_CONFIG.MINIMUM_1TO1_ACCURACY ? '✅' : '❌'} Test ${accuracy >= TEST_CONFIG.MINIMUM_1TO1_ACCURACY ? 'PASSED' : 'FAILED'}`);

    } catch (error) {
      console.error('❌ 1:1 Recreation test failed:', error);
      this.results.tests['1to1_recreation'] = {
        passed: false,
        error: error.message
      };
    }
  }

  /**
   * Test 2: Variable-Based Generation
   * Tests the new variable-based system for proper substitution
   */
  async testVariableGeneration() {
    console.log('\n📋 TEST 2: Variable-Based Generation');
    console.log('=' .repeat(50));

    try {
      const generator = new NewAgreementGenerator();
      const templatePath = path.join(__dirname, '../client/public/contributor-agreement-template.md');
      const templateText = fs.readFileSync(templatePath, 'utf8');

      const testOptions = {
        contributors: [{
          id: 'test_contributor',
          email: '<EMAIL>',
          name: 'Test Contributor',
          address: '123 Test Street, Test City, TS 12345'
        }],
        currentUser: {
          id: 'test_contributor',
          email: '<EMAIL>',
          user_metadata: {
            full_name: 'Test Contributor'
          }
        },
        fullName: 'Test Contributor',
        agreementDate: new Date('2024-01-15')
      };

      // Test with multiple scenarios
      const variableResults = {};

      for (const [scenarioName, scenarioData] of Object.entries(TEST_SCENARIOS)) {
        console.log(`\n  Testing scenario: ${scenarioName}`);

        const agreement = await generator.generateAgreement(
          templateText,
          scenarioData,
          testOptions
        );

        // Save the agreement
        const outputPath = path.join(TEST_CONFIG.OUTPUT_DIR, `variable-${scenarioName.toLowerCase()}.md`);
        fs.writeFileSync(outputPath, agreement);

        // Validate variable substitution
        const variableValidation = this.validateVariableSubstitution(agreement, scenarioData);

        variableResults[scenarioName] = {
          outputFile: outputPath,
          validation: variableValidation,
          passed: variableValidation.accuracy >= TEST_CONFIG.MINIMUM_VARIABLE_ACCURACY
        };

        console.log(`    📊 Variable accuracy: ${variableValidation.accuracy}%`);
        console.log(`    ${variableValidation.accuracy >= TEST_CONFIG.MINIMUM_VARIABLE_ACCURACY ? '✅' : '❌'} ${variableValidation.accuracy >= TEST_CONFIG.MINIMUM_VARIABLE_ACCURACY ? 'PASSED' : 'FAILED'}`);
      }

      this.results.tests['variable_generation'] = variableResults;

    } catch (error) {
      console.error('❌ Variable generation test failed:', error);
      this.results.tests['variable_generation'] = {
        passed: false,
        error: error.message
      };
    }
  }

  /**
   * Test 3: Cross-Contamination Prevention
   * Ensures no project-specific content leaks between agreements
   */
  async testCrossContamination() {
    console.log('\n📋 TEST 3: Cross-Contamination Prevention');
    console.log('=' .repeat(50));

    try {
      const generator = new NewAgreementGenerator();
      const templatePath = path.join(__dirname, '../client/public/contributor-agreement-template.md');
      const templateText = fs.readFileSync(templatePath, 'utf8');

      const testOptions = {
        contributors: [{
          id: 'test_contributor',
          email: '<EMAIL>',
          name: 'Test Contributor',
          address: '123 Test Street'
        }],
        currentUser: {
          id: 'test_contributor',
          email: '<EMAIL>',
          user_metadata: {
            full_name: 'Test Contributor'
          }
        },
        fullName: 'Test Contributor',
        agreementDate: new Date('2024-01-15')
      };

      // Generate agreements for all scenarios
      const agreements = {};
      for (const [scenarioName, scenarioData] of Object.entries(TEST_SCENARIOS)) {
        agreements[scenarioName] = await generator.generateAgreement(
          templateText,
          scenarioData,
          testOptions
        );
      }

      // Check for cross-contamination
      const contaminationResults = this.checkCrossContamination(agreements);

      this.results.tests['cross_contamination'] = {
        passed: contaminationResults.totalContamination === 0,
        contamination: contaminationResults.totalContamination,
        details: contaminationResults.details,
        threshold: 0
      };

      console.log(`🚫 Total contamination instances: ${contaminationResults.totalContamination}`);
      console.log(`${contaminationResults.totalContamination === 0 ? '✅' : '❌'} Cross-contamination test ${contaminationResults.totalContamination === 0 ? 'PASSED' : 'FAILED'}`);

      if (contaminationResults.totalContamination > 0) {
        console.log('\n⚠️  Contamination details:');
        contaminationResults.details.forEach(detail => {
          console.log(`    ${detail.source} → ${detail.target}: "${detail.content}"`);
        });
      }

    } catch (error) {
      console.error('❌ Cross-contamination test failed:', error);
      this.results.tests['cross_contamination'] = {
        passed: false,
        error: error.message
      };
    }
  }

  /**
   * Test 4: Multiple Business Scenarios
   * Tests various business types and industries
   */
  async testBusinessScenarios() {
    console.log('\n📋 TEST 4: Multiple Business Scenarios');
    console.log('=' .repeat(50));

    try {
      // Add more business scenarios for comprehensive testing
      const additionalScenarios = {
        MUSIC_PROJECT: {
          name: 'Harmony Symphony',
          description: 'A collaborative music production project creating original orchestral compositions',
          project_type: 'music',
          company_name: 'Melody Studios Inc.',
          address: '789 Music Row, Nashville, TN 37203',
          contact_email: '<EMAIL>',
          city: 'Nashville',
          state: 'Tennessee',
          zip: '37203',
          county: 'Davidson',
          legal_entity_type: 'corporation',
          incorporation_state: 'Tennessee',
          features: 'Professional orchestral recordings with modern production techniques.',
          coreFeatures: `1. **Composition**
   - Original orchestral arrangements
   - Modern classical fusion elements
   - Thematic musical storytelling

2. **Recording**
   - Professional studio sessions
   - High-quality audio capture
   - Multi-track recording techniques

3. **Production**
   - Audio mixing and mastering
   - Digital enhancement
   - Final distribution preparation`,
          technicalRequirements: `- Studio: Professional recording facility
- Equipment: High-end microphones and mixing boards
- Software: Pro Tools, Logic Pro
- Format: 48kHz/24-bit WAV, MP3 distribution
- Distribution: Streaming platforms and physical media`,
          roadmapPhases: `**Phase 1: Pre-Production (Month 1)**
- Composition finalization
- Musician recruitment
- Studio booking and setup

**Phase 2: Recording (Months 2-3)**
- Orchestra recording sessions
- Individual instrument tracking
- Vocal recording if applicable

**Phase 3: Post-Production (Month 4)**
- Mixing and mastering
- Quality control
- Distribution preparation`,
          milestones: [
            {
              title: 'Composition Complete',
              description: 'All musical arrangements finalized and approved',
              dueDate: 'Month 1'
            },
            {
              title: 'Recording Sessions',
              description: 'All orchestral and individual tracks recorded',
              dueDate: 'Month 3'
            },
            {
              title: 'Final Master',
              description: 'Mixed, mastered, and ready for distribution',
              dueDate: 'Month 4'
            }
          ]
        },

        FILM_PROJECT: {
          name: 'Independent Vision',
          description: 'An independent film production focusing on contemporary social themes',
          project_type: 'film',
          company_name: 'Visionary Films LLC',
          address: '321 Hollywood Blvd, Los Angeles, CA 90028',
          contact_email: '<EMAIL>',
          city: 'Los Angeles',
          state: 'California',
          zip: '90028',
          county: 'Los Angeles',
          legal_entity_type: 'llc',
          incorporation_state: 'California',
          features: 'Character-driven narrative with innovative cinematography and sound design.',
          coreFeatures: `1. **Screenplay Development**
   - Character-driven storytelling
   - Contemporary social themes
   - Innovative narrative structure

2. **Production**
   - Professional cinematography
   - Location shooting
   - Actor direction and performance

3. **Post-Production**
   - Video editing and color grading
   - Sound design and mixing
   - Visual effects integration`,
          technicalRequirements: `- Camera: RED Digital Cinema cameras
- Audio: Professional boom and wireless systems
- Editing: Avid Media Composer, DaVinci Resolve
- Format: 4K production, multiple distribution formats
- Distribution: Film festivals, streaming platforms`,
          roadmapPhases: `**Phase 1: Pre-Production (Months 1-2)**
- Script finalization
- Casting and crew hiring
- Location scouting and permits

**Phase 2: Principal Photography (Months 3-4)**
- On-location filming
- Studio work
- B-roll and pickup shots

**Phase 3: Post-Production (Months 5-6)**
- Editing and assembly
- Color correction and sound mixing
- Final delivery and distribution`,
          milestones: [
            {
              title: 'Pre-Production Complete',
              description: 'All planning, casting, and preparation finished',
              dueDate: 'Month 2'
            },
            {
              title: 'Principal Photography Wrap',
              description: 'All primary filming completed',
              dueDate: 'Month 4'
            },
            {
              title: 'Final Cut',
              description: 'Completed film ready for distribution',
              dueDate: 'Month 6'
            }
          ]
        }
      };

      // Combine with existing scenarios
      const allScenarios = { ...TEST_SCENARIOS, ...additionalScenarios };

      const generator = new NewAgreementGenerator();
      const templatePath = path.join(__dirname, '../client/public/contributor-agreement-template.md');
      const templateText = fs.readFileSync(templatePath, 'utf8');

      const testOptions = {
        contributors: [{
          id: 'test_contributor',
          email: '<EMAIL>',
          name: 'Test Contributor',
          address: '123 Test Street'
        }],
        currentUser: {
          id: 'test_contributor',
          email: '<EMAIL>',
          user_metadata: {
            full_name: 'Test Contributor'
          }
        },
        fullName: 'Test Contributor',
        agreementDate: new Date('2024-01-15')
      };

      const businessResults = {};

      for (const [scenarioName, scenarioData] of Object.entries(allScenarios)) {
        console.log(`\n  Testing business scenario: ${scenarioName}`);

        const agreement = await generator.generateAgreement(
          templateText,
          scenarioData,
          testOptions
        );

        // Save the agreement
        const outputPath = path.join(TEST_CONFIG.OUTPUT_DIR, `business-${scenarioName.toLowerCase()}.md`);
        fs.writeFileSync(outputPath, agreement);

        // Validate business-specific content
        const businessValidation = this.validateBusinessSpecificContent(agreement, scenarioData);

        businessResults[scenarioName] = {
          outputFile: outputPath,
          validation: businessValidation,
          passed: businessValidation.accuracy >= TEST_CONFIG.MINIMUM_VARIABLE_ACCURACY
        };

        console.log(`    📊 Business content accuracy: ${businessValidation.accuracy}%`);
        console.log(`    ${businessValidation.accuracy >= TEST_CONFIG.MINIMUM_VARIABLE_ACCURACY ? '✅' : '❌'} ${businessValidation.accuracy >= TEST_CONFIG.MINIMUM_VARIABLE_ACCURACY ? 'PASSED' : 'FAILED'}`);
      }

      this.results.tests['business_scenarios'] = businessResults;

    } catch (error) {
      console.error('❌ Business scenarios test failed:', error);
      this.results.tests['business_scenarios'] = {
        passed: false,
        error: error.message
      };
    }
  }

  /**
   * Helper: Extract VOTA data from lawyer template
   */
  extractVOTADataFromTemplate(template) {
    // This would extract the exact VOTA data from the lawyer-approved template
    // For now, we'll use the predefined VOTA_EXACT data
    return TEST_SCENARIOS.VOTA_EXACT;
  }

  /**
   * Helper: Calculate template accuracy by comparing generated vs lawyer template
   */
  calculateTemplateAccuracy(lawyerTemplate, generatedAgreement) {
    // Split into lines for comparison
    const lawyerLines = lawyerTemplate.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    const generatedLines = generatedAgreement.split('\n').map(line => line.trim()).filter(line => line.length > 0);

    // Key sections that must match exactly
    const criticalSections = [
      'CITY OF GAMERS INC.',
      'CONTRIBUTOR AGREEMENT',
      'Village of The Ages',
      'village simulation game',
      'historical progressions',
      'resource-based challenges'
    ];

    let matches = 0;
    let total = criticalSections.length;

    for (const section of criticalSections) {
      if (generatedAgreement.includes(section)) {
        matches++;
      }
    }

    // Additional structural comparison
    const structuralMatches = this.compareStructuralElements(lawyerTemplate, generatedAgreement);
    matches += structuralMatches.matches;
    total += structuralMatches.total;

    return Math.round((matches / total) * 100);
  }

  /**
   * Helper: Compare structural elements between templates
   */
  compareStructuralElements(template1, template2) {
    const structuralElements = [
      '## Recitals',
      '## 1. Definitions',
      '## 2. Treatment of Confidential Information',
      '## SCHEDULE A',
      '## EXHIBIT I',
      '## EXHIBIT II',
      'Background IP',
      'Work Product',
      'Confidential Information'
    ];

    let matches = 0;
    for (const element of structuralElements) {
      if (template1.includes(element) && template2.includes(element)) {
        matches++;
      }
    }

    return {
      matches: matches,
      total: structuralElements.length
    };
  }

  /**
   * Helper: Get detailed accuracy breakdown
   */
  getAccuracyDetails(lawyerTemplate, generatedAgreement) {
    const details = {
      criticalSectionsFound: [],
      criticalSectionsMissing: [],
      structuralElementsFound: [],
      structuralElementsMissing: []
    };

    const criticalSections = [
      'CITY OF GAMERS INC.',
      'CONTRIBUTOR AGREEMENT',
      'Village of The Ages',
      'village simulation game',
      'historical progressions',
      'resource-based challenges'
    ];

    for (const section of criticalSections) {
      if (generatedAgreement.includes(section)) {
        details.criticalSectionsFound.push(section);
      } else {
        details.criticalSectionsMissing.push(section);
      }
    }

    return details;
  }

  /**
   * Helper: Validate variable substitution
   */
  validateVariableSubstitution(agreement, scenarioData) {
    const expectedElements = [
      scenarioData.name,
      scenarioData.description,
      scenarioData.company_name,
      scenarioData.address,
      scenarioData.contact_email
    ];

    let found = 0;
    const details = {
      found: [],
      missing: []
    };

    for (const element of expectedElements) {
      if (element && agreement.includes(element)) {
        found++;
        details.found.push(element);
      } else if (element) {
        details.missing.push(element);
      }
    }

    // Check for proper variable replacement (no leftover {{}} patterns)
    const unreplacedVariables = agreement.match(/\{\{[^}]+\}\}/g) || [];

    return {
      accuracy: Math.round((found / expectedElements.filter(e => e).length) * 100),
      elementsFound: found,
      totalElements: expectedElements.filter(e => e).length,
      unreplacedVariables: unreplacedVariables,
      details: details
    };
  }

  /**
   * Helper: Check for cross-contamination between agreements
   */
  checkCrossContamination(agreements) {
    const contaminationDetails = [];
    let totalContamination = 0;

    // Define unique identifiers for each scenario
    const uniqueIdentifiers = {
      VOTA_EXACT: ['Village of The Ages', 'village simulation', 'historical progressions', 'architectural evolution'],
      SOFTWARE_PROJECT: ['TaskFlow Pro', 'project management platform', 'remote teams', 'AI-powered insights'],
      MUSIC_PROJECT: ['Harmony Symphony', 'orchestral compositions', 'Melody Studios', 'Music Row'],
      FILM_PROJECT: ['Independent Vision', 'film production', 'Visionary Films', 'Hollywood Blvd']
    };

    // Check each agreement against others
    for (const [sourceScenario, sourceAgreement] of Object.entries(agreements)) {
      for (const [targetScenario, targetIdentifiers] of Object.entries(uniqueIdentifiers)) {
        if (sourceScenario !== targetScenario) {
          for (const identifier of targetIdentifiers) {
            if (sourceAgreement.includes(identifier)) {
              contaminationDetails.push({
                source: sourceScenario,
                target: targetScenario,
                content: identifier
              });
              totalContamination++;
            }
          }
        }
      }
    }

    return {
      totalContamination: totalContamination,
      details: contaminationDetails
    };
  }

  /**
   * Helper: Validate business-specific content
   */
  validateBusinessSpecificContent(agreement, scenarioData) {
    const businessElements = [
      scenarioData.name,
      scenarioData.description,
      scenarioData.project_type,
      scenarioData.company_name,
      scenarioData.features
    ];

    // Extract key phrases from core features
    const coreFeatureLines = scenarioData.coreFeatures ? scenarioData.coreFeatures.split('\n') : [];
    const keyPhrases = coreFeatureLines
      .filter(line => line.includes('**') || line.includes('-'))
      .map(line => line.replace(/[*\-]/g, '').trim())
      .filter(phrase => phrase.length > 5)
      .slice(0, 5); // Take first 5 key phrases

    const allElements = [...businessElements.filter(e => e), ...keyPhrases];

    let found = 0;
    const details = {
      found: [],
      missing: []
    };

    for (const element of allElements) {
      if (agreement.includes(element)) {
        found++;
        details.found.push(element);
      } else {
        details.missing.push(element);
      }
    }

    return {
      accuracy: Math.round((found / allElements.length) * 100),
      elementsFound: found,
      totalElements: allElements.length,
      details: details
    };
  }

  /**
   * Generate comprehensive report
   */
  async generateComprehensiveReport() {
    console.log('\n📊 GENERATING COMPREHENSIVE REPORT');
    console.log('=' .repeat(50));

    // Calculate overall scores
    const testResults = this.results.tests;
    let overallScore = 0;
    let totalTests = 0;
    let passedTests = 0;

    // Analyze each test category
    for (const [testName, testResult] of Object.entries(testResults)) {
      if (testResult.passed !== undefined) {
        totalTests++;
        if (testResult.passed) {
          passedTests++;
        }
      }
    }

    overallScore = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;

    // Generate summary
    this.results.summary = {
      overallScore: overallScore,
      totalTests: totalTests,
      passedTests: passedTests,
      failedTests: totalTests - passedTests,
      meets1to1Requirement: testResults['1to1_recreation']?.passed || false,
      meetsVariableRequirement: this.checkVariableTestsPassed(testResults['variable_generation']),
      meetsContaminationRequirement: testResults['cross_contamination']?.passed || false,
      productionReady: overallScore >= 95 && (testResults['1to1_recreation']?.accuracy || 0) >= TEST_CONFIG.MINIMUM_1TO1_ACCURACY
    };

    // Generate recommendations
    this.generateRecommendations();

    // Save comprehensive report
    const reportPath = path.join(TEST_CONFIG.OUTPUT_DIR, TEST_CONFIG.REPORT_FILE);
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));

    // Display summary
    console.log(`\n📈 FINAL RESULTS SUMMARY`);
    console.log('=' .repeat(40));
    console.log(`Overall Score: ${overallScore}%`);
    console.log(`Tests Passed: ${passedTests}/${totalTests}`);
    console.log(`1:1 Recreation: ${testResults['1to1_recreation']?.accuracy || 0}% (Required: ${TEST_CONFIG.MINIMUM_1TO1_ACCURACY}%)`);
    console.log(`Cross-Contamination: ${testResults['cross_contamination']?.contamination || 'N/A'} instances`);
    console.log(`Production Ready: ${this.results.summary.productionReady ? 'YES' : 'NO'}`);

    console.log(`\n📄 Comprehensive report saved to: ${reportPath}`);

    return this.results;
  }

  /**
   * Helper: Check if variable tests passed
   */
  checkVariableTestsPassed(variableResults) {
    if (!variableResults || typeof variableResults !== 'object') {
      return false;
    }

    for (const [scenario, result] of Object.entries(variableResults)) {
      if (!result.passed) {
        return false;
      }
    }

    return true;
  }

  /**
   * Generate actionable recommendations
   */
  generateRecommendations() {
    const recommendations = [];
    const testResults = this.results.tests;

    // 1:1 Recreation recommendations
    if (testResults['1to1_recreation'] && !testResults['1to1_recreation'].passed) {
      const accuracy = testResults['1to1_recreation'].accuracy || 0;
      recommendations.push({
        priority: 'HIGH',
        category: '1:1 Template Recreation',
        issue: `Template recreation accuracy is ${accuracy}%, below required ${TEST_CONFIG.MINIMUM_1TO1_ACCURACY}%`,
        recommendation: 'Review template variable mapping and ensure all critical sections are properly substituted',
        actionItems: [
          'Check variable substitution logic in NewAgreementGenerator',
          'Verify template structure matches lawyer-approved format',
          'Test with exact VOTA data from lawyer template'
        ]
      });
    }

    // Variable generation recommendations
    if (testResults['variable_generation'] && !this.checkVariableTestsPassed(testResults['variable_generation'])) {
      recommendations.push({
        priority: 'MEDIUM',
        category: 'Variable Generation',
        issue: 'Variable-based generation failing for some scenarios',
        recommendation: 'Improve variable substitution system to handle all business types',
        actionItems: [
          'Review variable mapping for each project type',
          'Ensure all template variables are properly defined',
          'Add fallback values for missing data'
        ]
      });
    }

    // Cross-contamination recommendations
    if (testResults['cross_contamination'] && !testResults['cross_contamination'].passed) {
      const contamination = testResults['cross_contamination'].contamination || 0;
      recommendations.push({
        priority: 'HIGH',
        category: 'Cross-Contamination',
        issue: `${contamination} instances of cross-contamination detected`,
        recommendation: 'Implement proper variable isolation to prevent content leakage',
        actionItems: [
          'Review agreement generation process for variable isolation',
          'Implement content validation before agreement finalization',
          'Add automated contamination detection to CI/CD pipeline'
        ]
      });
    }

    this.results.recommendations = recommendations;
  }
}

// Export for use in other test files
export { ComprehensiveAgreementTester, TEST_SCENARIOS, TEST_CONFIG };

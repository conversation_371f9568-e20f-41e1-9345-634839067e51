/**
 * Enhanced Studio Creation Component
 * 
 * Comprehensive studio creation flow integrating all agreement system capabilities:
 * - Industry-specific configuration
 * - Revenue model selection and setup
 * - IP rights management
 * - Legal entity information
 * - Collaboration preferences
 * - Agreement template selection
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Building2, 
  Users, 
  DollarSign, 
  Shield, 
  FileText, 
  Globe, 
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';

// Import our agreement system utilities
import { INDUSTRY_CATEGORIES, COLLABORATION_TYPES } from '@/utils/agreement/industryTemplates';
import { REVENUE_MODEL_TYPES } from '@/utils/agreement/revenueModelStructures';
import { IP_OWNERSHIP_MODELS } from '@/utils/agreement/ipRightsFramework';
import { SUPPORTED_CURRENCIES } from '@/utils/agreement/multiCurrencySystem';

const CREATION_STEPS = [
  { id: 'basic', title: 'Basic Information', icon: Building2 },
  { id: 'industry', title: 'Industry & Collaboration', icon: Users },
  { id: 'revenue', title: 'Revenue Model', icon: DollarSign },
  { id: 'ip', title: 'IP Rights', icon: Shield },
  { id: 'legal', title: 'Legal Structure', icon: FileText },
  { id: 'review', title: 'Review & Create', icon: CheckCircle }
];

export default function EnhancedAllianceCreation() {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [allianceData, setAllianceData] = useState({
    // Basic Information
    name: '',
    description: '',
    purpose: '',
    
    // Industry & Collaboration
    industry: '',
    collaborationType: '',
    targetMarket: '',
    expectedDuration: '',
    
    // Revenue Model
    revenueModel: '',
    revenueModelConfig: {},
    currency: 'USD',
    minimumPayout: 100,
    paymentFrequency: 'monthly',
    
    // IP Rights
    ipOwnershipModel: '',
    ipRightsConfig: {},
    attributionRequired: true,
    
    // Legal Structure
    legalEntityType: '',
    jurisdiction: '',
    governingLaw: '',
    disputeResolution: 'arbitration',
    
    // Additional Configuration
    isPublic: false,
    requiresApproval: true,
    maxMembers: null,
    tags: []
  });

  const [validationErrors, setValidationErrors] = useState({});
  const [isCreating, setIsCreating] = useState(false);

  const currentStepData = CREATION_STEPS[currentStep];
  const progress = ((currentStep + 1) / CREATION_STEPS.length) * 100;

  const updateAllianceData = (field, value) => {
    setAllianceData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const validateCurrentStep = () => {
    const errors = {};
    
    switch (currentStepData.id) {
      case 'basic':
        if (!allianceData.name.trim()) errors.name = 'Studio name is required';
        if (!allianceData.description.trim()) errors.description = 'Description is required';
        if (!allianceData.purpose.trim()) errors.purpose = 'Purpose is required';
        break;
        
      case 'industry':
        if (!allianceData.industry) errors.industry = 'Industry selection is required';
        if (!allianceData.collaborationType) errors.collaborationType = 'Collaboration type is required';
        break;
        
      case 'revenue':
        if (!allianceData.revenueModel) errors.revenueModel = 'Revenue model is required';
        if (!allianceData.currency) errors.currency = 'Currency is required';
        break;
        
      case 'ip':
        if (!allianceData.ipOwnershipModel) errors.ipOwnershipModel = 'IP ownership model is required';
        break;
        
      case 'legal':
        if (!allianceData.legalEntityType) errors.legalEntityType = 'Legal entity type is required';
        if (!allianceData.jurisdiction) errors.jurisdiction = 'Jurisdiction is required';
        break;
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      setCurrentStep(prev => Math.min(prev + 1, CREATION_STEPS.length - 1));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
  };

  const handleCreateAlliance = async () => {
    if (!validateCurrentStep()) return;
    
    setIsCreating(true);
    try {
      // Create studio with comprehensive configuration
      const response = await fetch('/api/studios', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...allianceData,
          agreementSystemEnabled: true,
          createdAt: new Date().toISOString()
        }),
      });

      if (response.ok) {
        const studio = await response.json();
        navigate(`/studios/${studio.id}`);
      } else {
        throw new Error('Failed to create studio');
      }
    } catch (error) {
      console.error('Error creating studio:', error);
      // Handle error (show toast, etc.)
    } finally {
      setIsCreating(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStepData.id) {
      case 'basic':
        return <BasicInformationStep />;
      case 'industry':
        return <IndustryCollaborationStep />;
      case 'revenue':
        return <RevenueModelStep />;
      case 'ip':
        return <IPRightsStep />;
      case 'legal':
        return <LegalStructureStep />;
      case 'review':
        return <ReviewStep />;
      default:
        return null;
    }
  };

  const BasicInformationStep = () => (
    <div className="space-y-6">
      <div>
        <Label htmlFor="name">Studio Name *</Label>
        <Input
          id="name"
          value={allianceData.name}
          onChange={(e) => updateAllianceData('name', e.target.value)}
          placeholder="Enter studio name"
          className={validationErrors.name ? 'border-red-500' : ''}
        />
        {validationErrors.name && (
          <p className="text-sm text-red-500 mt-1">{validationErrors.name}</p>
        )}
      </div>

      <div>
        <Label htmlFor="description">Description *</Label>
        <Textarea
          id="description"
          value={allianceData.description}
          onChange={(e) => updateAllianceData('description', e.target.value)}
          placeholder="Describe what this studio is about"
          rows={4}
          className={validationErrors.description ? 'border-red-500' : ''}
        />
        {validationErrors.description && (
          <p className="text-sm text-red-500 mt-1">{validationErrors.description}</p>
        )}
      </div>

      <div>
        <Label htmlFor="purpose">Purpose & Goals *</Label>
        <Textarea
          id="purpose"
          value={allianceData.purpose}
          onChange={(e) => updateAllianceData('purpose', e.target.value)}
          placeholder="What are the main goals and objectives of this studio?"
          rows={3}
          className={validationErrors.purpose ? 'border-red-500' : ''}
        />
        {validationErrors.purpose && (
          <p className="text-sm text-red-500 mt-1">{validationErrors.purpose}</p>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="expectedDuration">Expected Duration</Label>
          <Select value={allianceData.expectedDuration} onValueChange={(value) => updateAllianceData('expectedDuration', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select duration" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3_months">3 Months</SelectItem>
              <SelectItem value="6_months">6 Months</SelectItem>
              <SelectItem value="1_year">1 Year</SelectItem>
              <SelectItem value="2_years">2 Years</SelectItem>
              <SelectItem value="ongoing">Ongoing</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="maxMembers">Maximum Members</Label>
          <Input
            id="maxMembers"
            type="number"
            value={allianceData.maxMembers || ''}
            onChange={(e) => updateAllianceData('maxMembers', e.target.value ? parseInt(e.target.value) : null)}
            placeholder="No limit"
            min="2"
          />
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="isPublic"
          checked={allianceData.isPublic}
          onCheckedChange={(checked) => updateAllianceData('isPublic', checked)}
        />
        <Label htmlFor="isPublic">Make this studio publicly discoverable</Label>
      </div>
    </div>
  );

  const IndustryCollaborationStep = () => (
    <div className="space-y-6">
      <div>
        <Label htmlFor="industry">Industry *</Label>
        <Select value={allianceData.industry} onValueChange={(value) => updateAllianceData('industry', value)}>
          <SelectTrigger className={validationErrors.industry ? 'border-red-500' : ''}>
            <SelectValue placeholder="Select industry" />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(INDUSTRY_CATEGORIES).map(([key, category]) => (
              <SelectItem key={key} value={key}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {validationErrors.industry && (
          <p className="text-sm text-red-500 mt-1">{validationErrors.industry}</p>
        )}
      </div>

      {allianceData.industry && (
        <div>
          <Label htmlFor="collaborationType">Collaboration Type *</Label>
          <Select value={allianceData.collaborationType} onValueChange={(value) => updateAllianceData('collaborationType', value)}>
            <SelectTrigger className={validationErrors.collaborationType ? 'border-red-500' : ''}>
              <SelectValue placeholder="Select collaboration type" />
            </SelectTrigger>
            <SelectContent>
              {INDUSTRY_CATEGORIES[allianceData.industry]?.collaborationTypes?.map((type) => (
                <SelectItem key={type.key} value={type.key}>
                  {type.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {validationErrors.collaborationType && (
            <p className="text-sm text-red-500 mt-1">{validationErrors.collaborationType}</p>
          )}
        </div>
      )}

      <div>
        <Label htmlFor="targetMarket">Target Market</Label>
        <Input
          id="targetMarket"
          value={allianceData.targetMarket}
          onChange={(e) => updateAllianceData('targetMarket', e.target.value)}
          placeholder="Who is your target audience?"
        />
      </div>

      {allianceData.industry && allianceData.collaborationType && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Based on your selections, we'll recommend appropriate agreement templates and revenue models for {INDUSTRY_CATEGORIES[allianceData.industry]?.name} collaborations.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );

  const RevenueModelStep = () => (
    <div className="space-y-6">
      <div>
        <Label htmlFor="revenueModel">Revenue Model *</Label>
        <Select value={allianceData.revenueModel} onValueChange={(value) => updateAllianceData('revenueModel', value)}>
          <SelectTrigger className={validationErrors.revenueModel ? 'border-red-500' : ''}>
            <SelectValue placeholder="Select revenue model" />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(REVENUE_MODEL_TYPES).map(([key, model]) => (
              <SelectItem key={key} value={key}>
                <div>
                  <div className="font-medium">{model.name}</div>
                  <div className="text-sm text-gray-500">{model.description}</div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {validationErrors.revenueModel && (
          <p className="text-sm text-red-500 mt-1">{validationErrors.revenueModel}</p>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="currency">Currency *</Label>
          <Select value={allianceData.currency} onValueChange={(value) => updateAllianceData('currency', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select currency" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(SUPPORTED_CURRENCIES).map(([code, currency]) => (
                <SelectItem key={code} value={code}>
                  {currency.symbol} {currency.name} ({code})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="paymentFrequency">Payment Frequency</Label>
          <Select value={allianceData.paymentFrequency} onValueChange={(value) => updateAllianceData('paymentFrequency', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select frequency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="annually">Annually</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor="minimumPayout">Minimum Payout Threshold</Label>
        <Input
          id="minimumPayout"
          type="number"
          value={allianceData.minimumPayout}
          onChange={(e) => updateAllianceData('minimumPayout', parseFloat(e.target.value) || 0)}
          placeholder="100"
          min="0"
        />
        <p className="text-sm text-gray-500 mt-1">
          Minimum amount before payments are processed
        </p>
      </div>

      {allianceData.revenueModel && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            {REVENUE_MODEL_TYPES[allianceData.revenueModel]?.description}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );

  const IPRightsStep = () => (
    <div className="space-y-6">
      <div>
        <Label htmlFor="ipOwnershipModel">IP Ownership Model *</Label>
        <Select value={allianceData.ipOwnershipModel} onValueChange={(value) => updateAllianceData('ipOwnershipModel', value)}>
          <SelectTrigger className={validationErrors.ipOwnershipModel ? 'border-red-500' : ''}>
            <SelectValue placeholder="Select IP ownership model" />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(IP_OWNERSHIP_MODELS).map(([key, model]) => (
              <SelectItem key={key} value={key}>
                {model.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {validationErrors.ipOwnershipModel && (
          <p className="text-sm text-red-500 mt-1">{validationErrors.ipOwnershipModel}</p>
        )}
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="attributionRequired"
          checked={allianceData.attributionRequired}
          onCheckedChange={(checked) => updateAllianceData('attributionRequired', checked)}
        />
        <Label htmlFor="attributionRequired">Require attribution for all contributions</Label>
      </div>

      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          IP rights will be managed according to the selected model. Detailed IP agreements will be generated for each collaboration within this studio.
        </AlertDescription>
      </Alert>
    </div>
  );

  const LegalStructureStep = () => (
    <div className="space-y-6">
      <div>
        <Label htmlFor="legalEntityType">Legal Entity Type *</Label>
        <Select value={allianceData.legalEntityType} onValueChange={(value) => updateAllianceData('legalEntityType', value)}>
          <SelectTrigger className={validationErrors.legalEntityType ? 'border-red-500' : ''}>
            <SelectValue placeholder="Select entity type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="informal_partnership">Informal Partnership</SelectItem>
            <SelectItem value="llc">Limited Liability Company (LLC)</SelectItem>
            <SelectItem value="corporation">Corporation</SelectItem>
            <SelectItem value="partnership">General Partnership</SelectItem>
            <SelectItem value="joint_venture">Joint Project</SelectItem>
          </SelectContent>
        </Select>
        {validationErrors.legalEntityType && (
          <p className="text-sm text-red-500 mt-1">{validationErrors.legalEntityType}</p>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="jurisdiction">Jurisdiction *</Label>
          <Select value={allianceData.jurisdiction} onValueChange={(value) => updateAllianceData('jurisdiction', value)}>
            <SelectTrigger className={validationErrors.jurisdiction ? 'border-red-500' : ''}>
              <SelectValue placeholder="Select jurisdiction" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="US">United States</SelectItem>
              <SelectItem value="CA">Canada</SelectItem>
              <SelectItem value="UK">United Kingdom</SelectItem>
              <SelectItem value="EU">European Union</SelectItem>
              <SelectItem value="AU">Australia</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
          {validationErrors.jurisdiction && (
            <p className="text-sm text-red-500 mt-1">{validationErrors.jurisdiction}</p>
          )}
        </div>

        <div>
          <Label htmlFor="disputeResolution">Dispute Resolution</Label>
          <Select value={allianceData.disputeResolution} onValueChange={(value) => updateAllianceData('disputeResolution', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select method" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="arbitration">Arbitration</SelectItem>
              <SelectItem value="mediation">Mediation</SelectItem>
              <SelectItem value="litigation">Litigation</SelectItem>
              <SelectItem value="mediation_then_arbitration">Mediation then Arbitration</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="requiresApproval"
          checked={allianceData.requiresApproval}
          onCheckedChange={(checked) => updateAllianceData('requiresApproval', checked)}
        />
        <Label htmlFor="requiresApproval">Require approval for new members</Label>
      </div>
    </div>
  );

  const ReviewStep = () => (
    <div className="space-y-6">
      <Alert>
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          Review your studio configuration below. Once created, you can add projects and generate agreements.
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><strong>Name:</strong> {allianceData.name}</div>
            <div><strong>Industry:</strong> {INDUSTRY_CATEGORIES[allianceData.industry]?.name}</div>
            <div><strong>Type:</strong> {allianceData.collaborationType}</div>
            <div><strong>Duration:</strong> {allianceData.expectedDuration?.replace('_', ' ')}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Financial Structure</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><strong>Revenue Model:</strong> {REVENUE_MODEL_TYPES[allianceData.revenueModel]?.name}</div>
            <div><strong>Currency:</strong> {SUPPORTED_CURRENCIES[allianceData.currency]?.name}</div>
            <div><strong>Payment Frequency:</strong> {allianceData.paymentFrequency}</div>
            <div><strong>Minimum Payout:</strong> {SUPPORTED_CURRENCIES[allianceData.currency]?.symbol}{allianceData.minimumPayout}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">IP & Legal</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><strong>IP Model:</strong> {allianceData.ipOwnershipModel?.replace(/_/g, ' ')}</div>
            <div><strong>Attribution:</strong> {allianceData.attributionRequired ? 'Required' : 'Optional'}</div>
            <div><strong>Entity Type:</strong> {allianceData.legalEntityType?.replace(/_/g, ' ')}</div>
            <div><strong>Jurisdiction:</strong> {allianceData.jurisdiction}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><strong>Visibility:</strong> {allianceData.isPublic ? 'Public' : 'Private'}</div>
            <div><strong>Approval Required:</strong> {allianceData.requiresApproval ? 'Yes' : 'No'}</div>
            <div><strong>Max Members:</strong> {allianceData.maxMembers || 'No limit'}</div>
            <div><strong>Dispute Resolution:</strong> {allianceData.disputeResolution?.replace(/_/g, ' ')}</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Create New Studio</h1>
        <p className="text-gray-600">
          Set up a comprehensive studio with integrated agreement management
        </p>
      </div>

      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium">Step {currentStep + 1} of {CREATION_STEPS.length}</span>
          <span className="text-sm text-gray-500">{Math.round(progress)}% Complete</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      {/* Step Navigation */}
      <div className="mb-8">
        <div className="flex justify-between">
          {CREATION_STEPS.map((step, index) => {
            const Icon = step.icon;
            const isActive = index === currentStep;
            const isCompleted = index < currentStep;
            
            return (
              <div
                key={step.id}
                className={`flex flex-col items-center space-y-2 ${
                  isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-400'
                }`}
              >
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center border-2 ${
                    isActive
                      ? 'border-blue-600 bg-blue-50'
                      : isCompleted
                      ? 'border-green-600 bg-green-50'
                      : 'border-gray-300 bg-gray-50'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                </div>
                <span className="text-xs font-medium text-center">{step.title}</span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Content */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <currentStepData.icon className="w-5 h-5" />
            <span>{currentStepData.title}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {renderStepContent()}
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 0}
        >
          Previous
        </Button>

        {currentStep === CREATION_STEPS.length - 1 ? (
          <Button
            onClick={handleCreateAlliance}
            disabled={isCreating}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isCreating ? 'Creating Studio...' : 'Create Studio'}
          </Button>
        ) : (
          <Button
            onClick={handleNext}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Next
          </Button>
        )}
      </div>
    </div>
  );
}

/**
 * Generate Fresh VOTA Agreement
 * 
 * This script generates a new VOTA agreement using the latest platform functions
 * and the exact data that would recreate the lawyer-approved example.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('📄 GENERATING FRESH VOTA AGREEMENT');
console.log('=' .repeat(50));
console.log('Using latest platform functions and exact VOTA data');
console.log('=' .repeat(50));

/**
 * Exact VOTA project data to recreate the lawyer-approved example
 */
const VOTA_PROJECT_DATA = {
  name: 'Village of The Ages',
  description: 'A village simulation game where players guide communities through historical progressions and manage resource-based challenges',
  project_type: 'game',
  
  // Company information (exact from lawyer example)
  company_name: 'City of Gamers Inc.',
  address: '1205 43rd Street, Suite B, Orlando, Florida 32839',
  contact_email: '<EMAIL>',
  city: 'Orlando',
  state: 'Florida',
  zip: '32839',
  county: 'Orange',
  legal_entity_type: 'corporation',
  incorporation_state: 'Florida',
  
  // Project features (exact from lawyer example)
  features: 'The game features dynamic challenges based on resource scarcity and natural disasters, creating an engaging gameplay loop.',
  
  coreFeatures: `1. **Village Building & Management**
   - Resource gathering and management
   - Building placement and construction
   - Population growth and management
   - Cultural evolution systems

2. **Historical Progression**
   - Players guide their community through multiple historical eras
   - Technology tree advancement
   - Cultural and social development
   - Architectural evolution

3. **Resource Management**
   - Dynamic weather systems affecting resources
   - Natural disasters and seasonal challenges
   - Trading systems with neighboring villages
   - Resource scarcity mechanics

4. **Interface Requirements**
   - Intuitive building placement system
   - Resource management dashboard
   - Population statistics and management panels
   - Technology and progression trackers`,

  technicalRequirements: `- Platform: PC (Steam, Epic Games Store)
- Engine: Unreal Engine 5
- MinSpecs: Standard hardware requirements for the target platforms
- ArtStyle: Stylized, readable visuals with distinctive aesthetics
- Audio: Atmospheric soundtrack with appropriate sound design
- VersionControl: Git-based source control with proper branching strategy`,

  roadmapPhases: `**Core Gameplay Development (Months 1-2)**
- Basic village layout and building system
- Core resource gathering mechanics
- Initial AI for villagers
- Basic UI framework
- First playable prototype with one historical era

**Feature Expansion (Months 2-3)**
- Additional historical eras
- Enhanced resource management systems
- Weather and disaster systems
- Trading mechanics
- Technology progression system

**Polish and Enhancement (Month 4)**
- UI refinement
- Performance optimization
- Additional content (buildings, resources, etc.)
- Balancing and gameplay tuning
- Audio implementation`,

  milestones: [
    {
      title: 'Core Gameplay Development',
      description: 'Basic village layout and building system',
      dueDate: 'Months 1-2'
    },
    {
      title: 'Resource Management System',
      description: 'Implementation of resource scarcity mechanics',
      dueDate: 'Months 3-4'
    },
    {
      title: 'Historical Progression Features',
      description: 'Time-based progression and historical events, architectural evolution through eras',
      dueDate: 'Months 5-6'
    }
  ],

  // Financial settings (exact from lawyer example)
  revenueShare: 33,
  payoutThreshold: 100000,
  maxPayment: 1000000
};

/**
 * Generate fresh VOTA agreement
 */
async function generateFreshVOTAAgreement() {
  try {
    console.log('🔧 Loading platform agreement generator...');
    
    // Import the actual platform agreement generator
    const { NewAgreementGenerator } = await import('../client/src/utils/agreement/newAgreementGenerator.js');
    const generator = new NewAgreementGenerator();
    
    console.log('📄 Loading agreement template...');
    
    // Load template directly from file system
    const templatePath = path.join(__dirname, '../client/public/contributor-agreement-template.md');
    const templateText = fs.readFileSync(templatePath, 'utf8');
    console.log(`   ✅ Template loaded (${templateText.length} characters)`);
    
    console.log('⚙️  Preparing contributor and options data...');
    
    // Prepare contributor and options data (exact from lawyer example)
    const options = {
      contributors: [{ 
        id: 'test_contributor', 
        email: '<EMAIL>', 
        name: 'Test Contributor',
        address: '123 Test Street, Test City, TS 12345'
      }],
      currentUser: { 
        id: 'test_user', 
        email: '<EMAIL>',
        user_metadata: { full_name: 'Test User' }
      },
      fullName: 'Test Contributor',
      agreementDate: new Date('2025-01-01'), // Fixed date for consistency
      
      // Include alliance information for proper company validation
      allianceInfo: {
        id: 'cog_alliance_12345',
        name: 'City of Gamers Inc.',
        owner_id: 'test_user',
        contact_information: {
          primaryContact: {
            name: 'Gynell Journigan',
            email: '<EMAIL>',
            title: 'President'
          }
        },
        company_info: {
          legal_name: VOTA_PROJECT_DATA.company_name,
          address: VOTA_PROJECT_DATA.address,
          state: VOTA_PROJECT_DATA.state,
          city: VOTA_PROJECT_DATA.city,
          zip: VOTA_PROJECT_DATA.zip,
          county: VOTA_PROJECT_DATA.county,
          billing_email: VOTA_PROJECT_DATA.contact_email,
          legal_entity_type: VOTA_PROJECT_DATA.legal_entity_type,
          incorporation_state: VOTA_PROJECT_DATA.incorporation_state
        }
      }
    };
    
    console.log('⚡ Generating fresh VOTA agreement...');
    
    // Generate the agreement using the platform's actual functions
    const startTime = Date.now();
    const generatedAgreement = await generator.generateAgreement(templateText, VOTA_PROJECT_DATA, options);
    const endTime = Date.now();
    
    console.log(`   ✅ Agreement generated in ${endTime - startTime}ms`);
    console.log(`   📊 Agreement length: ${generatedAgreement.length} characters`);
    
    // Save the fresh agreement
    const outputPath = path.join(__dirname, 'fresh-vota-agreement.md');
    fs.writeFileSync(outputPath, generatedAgreement);
    console.log(`   💾 Fresh agreement saved to: ${outputPath}`);
    
    // Validate the fresh agreement
    console.log('🔍 Validating fresh agreement...');
    const validation = validateFreshAgreement(generatedAgreement);
    
    console.log(`   📊 Structural accuracy: ${validation.structuralAccuracy}%`);
    console.log(`   📊 Content accuracy: ${validation.contentAccuracy}%`);
    console.log(`   📊 Financial accuracy: ${validation.financialAccuracy}%`);
    console.log(`   📊 Overall accuracy: ${validation.overallAccuracy}%`);
    
    if (validation.issues.length > 0) {
      console.log('   ⚠️  Issues found:');
      validation.issues.forEach(issue => {
        console.log(`      • ${issue}`);
      });
    }
    
    // Compare with lawyer-approved example
    console.log('📖 Comparing with lawyer-approved example...');
    const examplePath = path.join(__dirname, '../client/public/example-cog-contributor-agreement.md');
    const lawyerExample = fs.readFileSync(examplePath, 'utf8');
    
    const comparison = compareWithLawyerExample(generatedAgreement, lawyerExample);
    console.log(`   📊 Comparison accuracy: ${comparison.accuracy}%`);
    
    if (comparison.differences.length > 0) {
      console.log('   📋 Key differences:');
      comparison.differences.slice(0, 5).forEach(diff => {
        console.log(`      • ${diff}`);
      });
    }
    
    // Generate summary report
    const report = {
      timestamp: new Date().toISOString(),
      generationTime: endTime - startTime,
      agreementLength: generatedAgreement.length,
      validation: validation,
      comparison: comparison,
      outputPath: outputPath
    };
    
    const reportPath = path.join(__dirname, 'fresh-vota-agreement-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`   📊 Report saved to: ${reportPath}`);
    
    return {
      success: true,
      agreement: generatedAgreement,
      validation: validation,
      comparison: comparison,
      outputPath: outputPath
    };
    
  } catch (error) {
    console.error('❌ Failed to generate fresh VOTA agreement:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Validate the fresh agreement
 */
function validateFreshAgreement(agreement) {
  const validation = {
    structuralAccuracy: 0,
    contentAccuracy: 0,
    financialAccuracy: 0,
    overallAccuracy: 0,
    issues: []
  };

  // Check structural elements
  const structuralElements = [
    'CONTRIBUTOR AGREEMENT',
    'Definitions',
    'Treatment of Confidential Information',
    'Ownership of Work Product',
    'Non-Disparagement',
    'Termination',
    'Equitable Remedies',
    'Assignment',
    'Waivers and Amendments',
    'Survival',
    'Status as Independent Contractor',
    'Representations and Warranties',
    'Indemnification',
    'Entire Agreement',
    'Governing Law',
    'Consent to Jurisdiction',
    'Settlement of Disputes',
    'SCHEDULE A',
    'SCHEDULE B',
    'EXHIBIT I',
    'EXHIBIT II'
  ];

  let structuralMatches = 0;
  structuralElements.forEach(element => {
    if (agreement.includes(element)) {
      structuralMatches++;
    } else {
      validation.issues.push(`Missing structural element: ${element}`);
    }
  });
  validation.structuralAccuracy = Math.round((structuralMatches / structuralElements.length) * 100);

  // Check content elements
  const contentElements = [
    'Village of The Ages',
    'village simulation game',
    'City of Gamers Inc.',
    'Orlando, Florida',
    'Unreal Engine 5',
    'Historical Progression',
    'Resource Management',
    'Village Building & Management'
  ];

  let contentMatches = 0;
  contentElements.forEach(element => {
    if (agreement.includes(element)) {
      contentMatches++;
    } else {
      validation.issues.push(`Missing content element: ${element}`);
    }
  });
  validation.contentAccuracy = Math.round((contentMatches / contentElements.length) * 100);

  // Check financial elements
  const financialElements = [
    '33%',
    '$100,000',
    '$1,000,000',
    'Revenue Tranch',
    'Contribution Points',
    'Quarterly reports'
  ];

  let financialMatches = 0;
  financialElements.forEach(element => {
    if (agreement.includes(element)) {
      financialMatches++;
    } else {
      validation.issues.push(`Missing financial element: ${element}`);
    }
  });
  validation.financialAccuracy = Math.round((financialMatches / financialElements.length) * 100);

  // Overall accuracy
  validation.overallAccuracy = Math.round(
    (validation.structuralAccuracy + validation.contentAccuracy + validation.financialAccuracy) / 3
  );

  return validation;
}

/**
 * Compare with lawyer-approved example
 */
function compareWithLawyerExample(generated, lawyerExample) {
  const comparison = {
    accuracy: 0,
    similarities: [],
    differences: []
  };

  // Check for key similarities
  const keyElements = [
    'CONTRIBUTOR AGREEMENT',
    'City of Gamers Inc.',
    'Village of The Ages',
    'Orange County, Florida',
    '33%',
    '$100,000',
    '$1,000,000'
  ];

  let matches = 0;
  keyElements.forEach(element => {
    if (generated.includes(element) && lawyerExample.includes(element)) {
      matches++;
      comparison.similarities.push(`Both contain: ${element}`);
    } else if (generated.includes(element) && !lawyerExample.includes(element)) {
      comparison.differences.push(`Generated has but example missing: ${element}`);
    } else if (!generated.includes(element) && lawyerExample.includes(element)) {
      comparison.differences.push(`Example has but generated missing: ${element}`);
    }
  });

  comparison.accuracy = Math.round((matches / keyElements.length) * 100);

  return comparison;
}

// Run the fresh agreement generation
generateFreshVOTAAgreement()
  .then(result => {
    if (result.success) {
      console.log('\n🎉 SUCCESS: Fresh VOTA agreement generated successfully!');
      console.log(`📊 Overall accuracy: ${result.validation.overallAccuracy}%`);
      console.log(`📄 Agreement saved to: ${result.outputPath}`);
    } else {
      console.log('\n❌ FAILED: Could not generate fresh VOTA agreement');
      console.log(`Error: ${result.error}`);
    }
  })
  .catch(error => {
    console.error('\n❌ Script failed:', error.message);
  });

#!/usr/bin/env node

/**
 * Simple Agreement Generation Test
 */

import fs from 'fs';

console.log('🧪 Simple Agreement Generation Test');
console.log('===================================');

try {
  // Load template
  const template = fs.readFileSync('client/public/templates/v2/standard-contributor-agreement.md', 'utf8');
  console.log(`✅ Template loaded: ${template.length} characters`);

  // Test data
  const userData = {
    company: {
      name: 'TechVenture Studios Inc.',
      legalName: 'TechVenture Studios Inc.',
      address: '456 Innovation Boulevard, Suite 200, Austin, TX 78701',
      state: 'Texas',
      signerName: '<PERSON>',
      signerTitle: 'Chief Executive Officer',
      billingEmail: '<EMAIL>'
    },
    project: {
      name: 'Quantum Realms',
      description: 'An immersive strategy game where players build and manage civilizations across multiple dimensions and timelines',
      projectType: 'game'
    },
    contributor: {
      name: '<PERSON>',
      email: 'alex.rod<PERSON><PERSON><PERSON>@freelance.com',
      address: '789 Developer Lane, Apt 15, Austin, TX 78702'
    }
  };

  console.log('📝 Processing agreement...');

  // Simple variable replacement
  let agreement = template;
  
  // Replace variables
  agreement = agreement.replace(/\{\{COMPANY_NAME\}\}/g, userData.company.name.toUpperCase());
  agreement = agreement.replace(/\{\{COMPANY_LEGAL_NAME\}\}/g, userData.company.legalName);
  agreement = agreement.replace(/\{\{COMPANY_ADDRESS\}\}/g, userData.company.address);
  agreement = agreement.replace(/\{\{COMPANY_STATE\}\}/g, userData.company.state);
  agreement = agreement.replace(/\{\{COMPANY_SIGNER_NAME\}\}/g, userData.company.signerName);
  agreement = agreement.replace(/\{\{COMPANY_SIGNER_TITLE\}\}/g, userData.company.signerTitle);
  agreement = agreement.replace(/\{\{COMPANY_BILLING_EMAIL\}\}/g, userData.company.billingEmail);
  agreement = agreement.replace(/\{\{PROJECT_NAME\}\}/g, userData.project.name);
  agreement = agreement.replace(/\{\{PROJECT_DESCRIPTION\}\}/g, userData.project.description);
  agreement = agreement.replace(/\{\{CONTRIBUTOR_NAME\}\}/g, userData.contributor.name);
  agreement = agreement.replace(/\{\{CONTRIBUTOR_EMAIL\}\}/g, userData.contributor.email);
  agreement = agreement.replace(/\{\{CONTRIBUTOR_ADDRESS\}\}/g, userData.contributor.address);
  agreement = agreement.replace(/\{\{EFFECTIVE_DATE\}\}/g, 'December 23, 2024');

  // Handle game project conditionals
  agreement = agreement.replace(/\{\{#IF PROJECT_TYPE_GAME\}\}([\s\S]*?)\{\{\/IF\}\}/g, '$1');
  agreement = agreement.replace(/\{\{#IF PROJECT_TYPE_SOFTWARE\}\}([\s\S]*?)\{\{\/IF\}\}/g, '');
  agreement = agreement.replace(/\{\{#IF PROJECT_TYPE_MUSIC\}\}([\s\S]*?)\{\{\/IF\}\}/g, '');
  agreement = agreement.replace(/\{\{#IF PROJECT_TYPE_FILM\}\}([\s\S]*?)\{\{\/IF\}\}/g, '');
  agreement = agreement.replace(/\{\{#IF PROJECT_TYPE_ART\}\}([\s\S]*?)\{\{\/IF\}\}/g, '');

  // Clean up
  agreement = agreement.replace(/\n\s*\n\s*\n/g, '\n\n');

  // Save generated agreement
  fs.writeFileSync('generated-agreement-test.md', agreement);
  console.log(`✅ Agreement generated: ${agreement.length} characters`);
  console.log('✅ Saved to: generated-agreement-test.md');

  // Quick validation
  const requiredSections = ['CONTRIBUTOR AGREEMENT', 'Recitals', '1. Definitions', 'SCHEDULE A', 'SCHEDULE B'];
  let sectionsFound = 0;
  
  requiredSections.forEach(section => {
    if (agreement.includes(section)) {
      console.log(`✅ ${section}`);
      sectionsFound++;
    } else {
      console.log(`❌ ${section} - MISSING`);
    }
  });

  console.log(`📊 Sections: ${sectionsFound}/${requiredSections.length}`);

  // Check data integration
  const testData = ['TechVenture Studios Inc.', 'Quantum Realms', 'Alex Rodriguez'];
  testData.forEach(data => {
    if (agreement.includes(data)) {
      console.log(`✅ ${data} - integrated`);
    } else {
      console.log(`❌ ${data} - missing`);
    }
  });

  // Check for unreplaced variables
  const unreplaced = agreement.match(/\{\{[A-Z_]+\}\}/g);
  if (unreplaced) {
    console.log(`❌ Unreplaced variables: ${unreplaced.join(', ')}`);
  } else {
    console.log('✅ All variables replaced');
  }

  console.log('\n🏁 Test Complete');

} catch (error) {
  console.error('❌ Test failed:', error.message);
}

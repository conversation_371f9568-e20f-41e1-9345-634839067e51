import React, { useState, useContext, useEffect } from 'react';
import { Card, CardBody, Button, Tabs, Tab, Chip, Progress } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { useNavigate } from 'react-router-dom';
import VentureSetupWizard from '../../components/project/VentureSetupWizard';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { Plus, TrendingUp, Users, DollarSign, Target, Briefcase } from 'lucide-react';

/**
 * VenturePage Component
 *
 * Main page for project management - where users create and manage projects
 * Integrates VentureSetupWizard and provides comprehensive project dashboard
 */
const VenturePage = () => {
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('my-projects');
  const [showCreateWizard, setShowCreateWizard] = useState(false);
  const [projects, setVentures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalVentures: 0,
    activeVentures: 0,
    totalRevenue: 0,
    teamMembers: 0
  });

  // Load user's projects
  useEffect(() => {
    if (currentUser) {
      loadVentures();
    }
  }, [currentUser]);

  const loadVentures = async () => {
    try {
      setLoading(true);
      
      // Get user's projects (projects they created or are members of)
      const { data: userVentures, error } = await supabase
        .from('projects')
        .select(`
          *,
          teams(
            id,
            name,
            studio_type,
            team_members(
              user_id,
              role,
              users(display_name, avatar_url)
            )
          ),
          tasks(id, status),
          contributions(id, hours_logged)
        `)
        .or(`created_by.eq.${currentUser.id},teams.team_members.user_id.eq.${currentUser.id}`)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setVentures(userVentures || []);
      
      // Calculate stats
      const totalVentures = userVentures?.length || 0;
      const activeVentures = userVentures?.filter(v => v.status === 'active').length || 0;
      const totalRevenue = userVentures?.reduce((sum, v) => sum + (v.total_revenue || 0), 0) || 0;
      const teamMembers = userVentures?.reduce((sum, v) => sum + (v.teams?.team_members?.length || 0), 0) || 0;

      setStats({
        totalVentures,
        activeVentures,
        totalRevenue,
        teamMembers
      });

    } catch (error) {
      console.error('Error loading projects:', error);
      toast.error('Failed to load projects');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateVenture = () => {
    setShowCreateWizard(true);
  };

  const handleWizardComplete = (ventureData) => {
    setShowCreateWizard(false);
    loadVentures(); // Refresh the list
    toast.success('Project created successfully!');
  };

  const handleWizardCancel = () => {
    setShowCreateWizard(false);
  };

  const handleViewVenture = (ventureId) => {
    navigate(`/project/${ventureId}`);
  };

  const handleViewAlliances = () => {
    navigate('/studios');
  };

  const handleViewAnalytics = () => {
    navigate('/analytics/contributions');
  };

  // Show wizard if creating
  if (showCreateWizard) {
    return (
      <VentureSetupWizard
        mode="create"
        onComplete={handleWizardComplete}
        onCancel={handleWizardCancel}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20">
      {/* Enhanced Three-Column Layout */}
      <div className="flex min-h-screen">
        {/* Left Quick Actions Sidebar */}
        <motion.div 
          initial={{ x: -50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="w-20 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-r border-default-200 flex flex-col items-center py-6 space-y-4"
        >
          <div className="text-2xl mb-4">🚀</div>
          
          <Button
            size="sm"
            variant="flat"
            onClick={handleCreateVenture}
            className="p-3 rounded-lg hover:bg-primary/10 transition-colors"
            title="Create Project"
          >
            <Plus size={16} />
          </Button>
          
          <Button
            size="sm"
            variant="flat"
            onClick={handleViewAlliances}
            className="p-3 rounded-lg hover:bg-secondary/10 transition-colors"
            title="View Studios"
          >
            <Users size={16} />
          </Button>
          
          <Button
            size="sm"
            variant="flat"
            onClick={handleViewAnalytics}
            className="p-3 rounded-lg hover:bg-warning/10 transition-colors"
            title="Analytics"
          >
            <TrendingUp size={16} />
          </Button>
          
          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/missions')}
            className="p-3 rounded-lg hover:bg-success/10 transition-colors"
            title="Missions"
          >
            <Target size={16} />
          </Button>
          
          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/earn')}
            className="p-3 rounded-lg hover:bg-success/10 transition-colors"
            title="Earnings"
          >
            <DollarSign size={16} />
          </Button>
        </motion.div>

        {/* Center Content Area */}
        <div className="flex-1 p-6">
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            {/* Project Page Header */}
            <div className="mb-6">
              <h1 className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent mb-2">
                🚀 Project Management
              </h1>
              <p className="text-default-600">
                Create, manage, and track your projects and collaborative projects
              </p>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <Card>
                <CardBody className="p-4 text-center">
                  <div className="text-2xl font-bold text-primary">{stats.totalVentures}</div>
                  <div className="text-sm text-default-600">Total Projects</div>
                </CardBody>
              </Card>
              <Card>
                <CardBody className="p-4 text-center">
                  <div className="text-2xl font-bold text-success">{stats.activeVentures}</div>
                  <div className="text-sm text-default-600">Active</div>
                </CardBody>
              </Card>
              <Card>
                <CardBody className="p-4 text-center">
                  <div className="text-2xl font-bold text-warning">${stats.totalRevenue.toLocaleString()}</div>
                  <div className="text-sm text-default-600">Total Revenue</div>
                </CardBody>
              </Card>
              <Card>
                <CardBody className="p-4 text-center">
                  <div className="text-2xl font-bold text-secondary">{stats.teamMembers}</div>
                  <div className="text-sm text-default-600">Team Members</div>
                </CardBody>
              </Card>
            </div>

            {/* Project Tabs */}
            <Card className="mb-6">
              <CardBody className="p-4">
                <Tabs 
                  selectedKey={activeTab} 
                  onSelectionChange={setActiveTab}
                  variant="underlined"
                  color="primary"
                >
                  <Tab key="my-projects" title="🚀 My Projects" />
                  <Tab key="active" title="⚡ Active" />
                  <Tab key="completed" title="✅ Completed" />
                  <Tab key="templates" title="📋 Templates" />
                </Tabs>
              </CardBody>
            </Card>

            {/* Tab Content */}
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              {loading ? (
                <Card>
                  <CardBody className="p-8 text-center">
                    <div className="text-default-600">Loading projects...</div>
                  </CardBody>
                </Card>
              ) : projects.length === 0 ? (
                <Card>
                  <CardBody className="p-8 text-center">
                    <div className="text-6xl mb-4">🚀</div>
                    <h3 className="text-xl font-semibold mb-2">No Projects Yet</h3>
                    <p className="text-default-600 mb-4">
                      Create your first project to start collaborating on projects
                    </p>
                    <Button
                      color="primary"
                      onClick={handleCreateVenture}
                      startContent={<Plus size={16} />}
                    >
                      Create Your First Project
                    </Button>
                  </CardBody>
                </Card>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {projects
                    .filter(project => {
                      if (activeTab === 'active') return project.status === 'active';
                      if (activeTab === 'completed') return project.status === 'completed';
                      return true; // my-projects shows all
                    })
                    .map((project) => (
                      <Card key={project.id} className="hover:shadow-lg transition-shadow">
                        <CardBody className="p-6">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-semibold">{project.name}</h3>
                            <Chip 
                              color={project.status === 'active' ? 'success' : 'default'} 
                              variant="flat"
                              size="sm"
                            >
                              {project.status}
                            </Chip>
                          </div>
                          
                          <p className="text-default-600 text-sm mb-4 line-clamp-2">
                            {project.description}
                          </p>
                          
                          <div className="space-y-2 mb-4">
                            <div className="flex justify-between text-sm">
                              <span>Progress</span>
                              <span>{project.progress || 0}%</span>
                            </div>
                            <Progress 
                              value={project.progress || 0} 
                              color="success" 
                              size="sm" 
                            />
                          </div>
                          
                          <div className="flex justify-between items-center">
                            <div className="flex gap-2">
                              <Chip size="sm" variant="flat">
                                {project.tasks?.length || 0} tasks
                              </Chip>
                              <Chip size="sm" variant="flat">
                                {project.teams?.team_members?.length || 0} members
                              </Chip>
                            </div>
                            <Button
                              size="sm"
                              variant="flat"
                              onClick={() => handleViewVenture(project.id)}
                              startContent={<Briefcase size={14} />}
                            >
                              View
                            </Button>
                          </div>
                        </CardBody>
                      </Card>
                    ))}
                </div>
              )}
            </motion.div>
          </motion.div>
        </div>

        {/* Right Context Sidebar */}
        <motion.div 
          initial={{ x: 50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="w-20 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-l border-default-200 flex flex-col items-center py-6 space-y-4"
        >
          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/projects')}
            className="p-3 rounded-lg hover:bg-primary/10 transition-colors"
            title="All Projects"
          >
            📁
          </Button>
          
          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/teams')}
            className="p-3 rounded-lg hover:bg-secondary/10 transition-colors"
            title="Teams"
          >
            👥
          </Button>
          
          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/validation/metrics')}
            className="p-3 rounded-lg hover:bg-success/10 transition-colors"
            title="Validation"
          >
            ✅
          </Button>
          
          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/profile')}
            className="p-3 rounded-lg hover:bg-secondary/10 transition-colors"
            title="Profile"
          >
            👤
          </Button>
          
          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/settings')}
            className="p-3 rounded-lg hover:bg-default/10 transition-colors"
            title="Settings"
          >
            ⚙️
          </Button>
        </motion.div>
      </div>
    </div>
  );
};

export default VenturePage;

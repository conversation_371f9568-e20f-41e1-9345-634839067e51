import React from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Avatar, AvatarGroup, Progress } from '@heroui/react';
import { motion } from 'framer-motion';

/**
 * Studio Card Component - Studio Display Component for Bento Grid
 * 
 * Features:
 * - Compact studio information display
 * - Member avatars and role indicators
 * - Performance metrics and progress tracking
 * - Quick action buttons for common tasks
 * - Responsive design for bento grid layout
 */
const StudioCard = ({ 
  studio, 
  size = 'medium', 
  onClick, 
  showActions = true,
  className = "" 
}) => {
  
  // Get studio type color
  const getTypeColor = (type) => {
    const colors = {
      'emerging': 'success',
      'established': 'primary',
      'startup': 'warning',
      'collective': 'secondary'
    };
    return colors[type] || 'default';
  };

  // Get studio type icon
  const getTypeIcon = (type) => {
    const icons = {
      'emerging': '🌱',
      'established': '🏢',
      'startup': '🚀',
      'collective': '🎨'
    };
    return icons[type] || '🏰';
  };

  // Format currency
  const formatCurrency = (amount) => {
    if (!amount) return '$0';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Get card size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'col-span-1 row-span-1';
      case 'medium':
        return 'col-span-2 row-span-1';
      case 'large':
        return 'col-span-2 row-span-2';
      case 'wide':
        return 'col-span-3 row-span-1';
      default:
        return 'col-span-2 row-span-1';
    }
  };

  // Calculate studio progress (mock calculation)
  const calculateProgress = () => {
    const totalVentures = studio.projects?.length || 0;
    const activeVentures = studio.projects?.filter(v => v.status === 'active').length || 0;
    const completedVentures = studio.projects?.filter(v => v.status === 'completed').length || 0;
    
    if (totalVentures === 0) return 0;
    return Math.round(((activeVentures * 0.5 + completedVentures) / totalVentures) * 100);
  };

  const progress = calculateProgress();
  const memberCount = studio.members?.length || 0;
  const activeVentures = studio.projects?.filter(v => v.status === 'active').length || 0;
  const monthlyRevenue = studio.monthly_revenue || 0;

  return (
    <motion.div
      className={`${getSizeClasses()} ${className}`}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      transition={{ duration: 0.2 }}
    >
      <Card 
        className={`h-full cursor-pointer hover:shadow-lg transition-all duration-300 ${
          studio.is_featured ? 'ring-2 ring-primary' : ''
        }`}
        onClick={onClick}
      >
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <div className="text-2xl">
                {getTypeIcon(studio.studio_type)}
              </div>
              <div>
                <h3 className="font-semibold text-lg line-clamp-1">
                  {studio.name}
                </h3>
                <p className="text-sm text-default-600 line-clamp-1">
                  {studio.industry}
                </p>
              </div>
            </div>
            
            <div className="flex flex-col items-end gap-1">
              <Chip
                color={getTypeColor(studio.studio_type)}
                size="sm"
                variant="flat"
              >
                {studio.studio_type}
              </Chip>
              {studio.is_featured && (
                <Chip color="warning" size="sm" variant="flat">
                  ⭐ Featured
                </Chip>
              )}
            </div>
          </div>
        </CardHeader>

        <CardBody className="pt-0">
          {/* Studio Description */}
          {size !== 'small' && (
            <p className="text-sm text-default-600 mb-4 line-clamp-2">
              {studio.description}
            </p>
          )}

          {/* Members */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <AvatarGroup 
                isBordered 
                max={4} 
                size="sm"
                className="justify-start"
              >
                {studio.members?.slice(0, 4).map((member) => (
                  <Avatar
                    key={member.id}
                    src={member.user?.avatar_url}
                    name={member.user?.display_name || member.user?.email}
                  />
                ))}
              </AvatarGroup>
              <span className="text-sm text-default-600">
                {memberCount} member{memberCount !== 1 ? 's' : ''}
              </span>
            </div>
            
            {studio.user_role && (
              <Chip
                color="primary"
                size="sm"
                variant="bordered"
              >
                {studio.user_role}
              </Chip>
            )}
          </div>

          {/* Metrics */}
          {size !== 'small' && (
            <div className="space-y-3 mb-4">
              {/* Progress */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Studio Progress</span>
                  <span>{progress}%</span>
                </div>
                <Progress 
                  value={progress} 
                  color="success" 
                  size="sm"
                />
              </div>

              {/* Key Metrics */}
              <div className="grid grid-cols-2 gap-3 text-center">
                <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-lg font-bold text-blue-600">
                    {activeVentures}
                  </div>
                  <div className="text-xs text-default-600">
                    Active Projects
                  </div>
                </div>
                
                <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-lg font-bold text-green-600">
                    {formatCurrency(monthlyRevenue)}
                  </div>
                  <div className="text-xs text-default-600">
                    Monthly Revenue
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Quick Actions */}
          {showActions && size !== 'small' && (
            <div className="flex gap-2">
              <Button
                size="sm"
                color="primary"
                variant="flat"
                className="flex-1"
                onClick={(e) => {
                  e.stopPropagation();
                  // Handle view action
                }}
              >
                View
              </Button>
              
              {studio.user_role === 'founder' || studio.user_role === 'owner' && (
                <Button
                  size="sm"
                  color="secondary"
                  variant="flat"
                  className="flex-1"
                  onClick={(e) => {
                    e.stopPropagation();
                    // Handle manage action
                  }}
                >
                  Manage
                </Button>
              )}
            </div>
          )}

          {/* Small size quick info */}
          {size === 'small' && (
            <div className="text-center">
              <div className="text-sm font-semibold text-primary">
                {memberCount} members
              </div>
              <div className="text-xs text-default-600">
                {activeVentures} projects
              </div>
            </div>
          )}

          {/* Large size additional info */}
          {size === 'large' && (
            <div className="mt-4 pt-4 border-t">
              <h4 className="font-semibold text-sm mb-2">Recent Activity</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-green-500">●</span>
                  <span>New project "Project Alpha" started</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-blue-500">●</span>
                  <span>2 new members joined</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-purple-500">●</span>
                  <span>Revenue milestone reached</span>
                </div>
              </div>
            </div>
          )}
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default StudioCard;

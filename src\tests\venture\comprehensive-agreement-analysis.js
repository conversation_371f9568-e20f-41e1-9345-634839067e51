/**
 * Comprehensive Agreement Analysis Script
 * 
 * This script generates multiple agreements using realistic user data
 * and performs detailed comparison against the lawyer-approved template
 */

import { NewAgreementGenerator } from '../../utils/agreement/newAgreementGenerator.js';
import fs from 'fs';
import path from 'path';

// Load the lawyer-approved template for comparison
const lawyerTemplatePath = path.join(process.cwd(), 'client/public/example-cog-contributor-agreement.md');
const lawyerTemplate = fs.readFileSync(lawyerTemplatePath, 'utf8');

// Load the system template
const systemTemplatePath = path.join(process.cwd(), 'client/public/contributor-agreement-template.md');
const systemTemplate = fs.readFileSync(systemTemplatePath, 'utf8');

console.log('🔍 COMPREHENSIVE AGREEMENT ANALYSIS');
console.log('=====================================');
console.log(`📄 Lawyer template loaded: ${lawyerTemplate.length} characters`);
console.log(`📄 System template loaded: ${systemTemplate.length} characters`);

// Test scenarios with realistic user data
const testScenarios = [
  {
    name: 'Software Development - Tech Company',
    alliance: {
      id: 'alliance_tech_001',
      name: 'TechCorp Solutions Inc.',
      description: 'Technology solutions provider',
      alliance_type: 'established',
      business_model: {
        revenue_sharing: {
          method: 'contribution_based',
          base_percentage: 70
        }
      },
      industry: 'Technology',
      team_members: [
        {
          user_id: 'user-1',
          role: 'founder',
          users: {
            id: 'user-1',
            email: '<EMAIL>',
            user_metadata: {
              full_name: 'John Smith'
            }
          }
        }
      ]
    },
    project: {
      id: 'project_001',
      name: 'AI Analytics Platform',
      description: 'Advanced AI-powered analytics platform for enterprise data insights',
      project_type: 'software',
      alliance_id: 'alliance_tech_001',
      team_id: 'alliance_tech_001'
    },
    contributors: [
      {
        id: 'contributor-1',
        permission_level: 'Owner',
        display_name: 'Sarah Johnson',
        email: '<EMAIL>'
      }
    ],
    currentUser: {
      id: 'user-2',
      email: '<EMAIL>',
      user_metadata: {
        full_name: 'Sarah Johnson'
      }
    }
  },
  {
    name: 'Game Development - Creative Studio',
    alliance: {
      id: 'alliance_game_001',
      name: 'GameStudio Collective LLC',
      description: 'Independent game development collective',
      alliance_type: 'established',
      business_model: {
        revenue_sharing: {
          method: 'contribution_based',
          base_percentage: 60
        }
      },
      industry: 'Entertainment',
      team_members: [
        {
          user_id: 'user-3',
          role: 'founder',
          users: {
            id: 'user-3',
            email: '<EMAIL>',
            user_metadata: {
              full_name: 'Alex Thompson'
            }
          }
        }
      ]
    },
    project: {
      id: 'project_002',
      name: 'Mystic Realms RPG',
      description: 'Fantasy role-playing game with immersive storytelling',
      project_type: 'game',
      alliance_id: 'alliance_game_001',
      team_id: 'alliance_game_001'
    },
    contributors: [
      {
        id: 'contributor-2',
        permission_level: 'Owner',
        display_name: 'Jordan Williams',
        email: '<EMAIL>'
      }
    ],
    currentUser: {
      id: 'user-4',
      email: '<EMAIL>',
      user_metadata: {
        full_name: 'Jordan Williams'
      }
    }
  },
  {
    name: 'Music Production - Creative Collective',
    alliance: {
      id: 'alliance_music_001',
      name: 'Harmony Productions Inc.',
      description: 'Music production and distribution collective',
      alliance_type: 'established',
      business_model: {
        revenue_sharing: {
          method: 'contribution_based',
          base_percentage: 50
        }
      },
      industry: 'Music',
      team_members: [
        {
          user_id: 'user-5',
          role: 'founder',
          users: {
            id: 'user-5',
            email: '<EMAIL>',
            user_metadata: {
              full_name: 'Maya Rodriguez'
            }
          }
        }
      ]
    },
    project: {
      id: 'project_003',
      name: 'Urban Beats Album',
      description: 'Contemporary urban music album with diverse artists',
      project_type: 'music',
      alliance_id: 'alliance_music_001',
      team_id: 'alliance_music_001'
    },
    contributors: [
      {
        id: 'contributor-3',
        permission_level: 'Owner',
        display_name: 'Chris Martinez',
        email: '<EMAIL>'
      }
    ],
    currentUser: {
      id: 'user-6',
      email: '<EMAIL>',
      user_metadata: {
        full_name: 'Chris Martinez'
      }
    }
  }
];

// Mock Supabase responses
function createMockSupabase(allianceData) {
  return {
    from: () => ({
      select: () => ({
        eq: () => ({
          single: () => Promise.resolve({
            data: allianceData,
            error: null
          })
        })
      })
    })
  };
}

async function generateAndAnalyzeAgreements() {
  const generator = new NewAgreementGenerator();
  const results = [];
  
  // Create output directory
  const outputDir = path.join(process.cwd(), 'src/tests/venture/output/comprehensive-analysis');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  console.log('\n🧪 GENERATING AGREEMENTS FOR ANALYSIS');
  console.log('=====================================');

  for (const scenario of testScenarios) {
    console.log(`\n📋 Processing: ${scenario.name}`);
    
    try {
      // Mock the supabase module for this scenario
      const originalImport = await import('../../utils/supabase/supabase.utils.js');
      originalImport.supabase = createMockSupabase(scenario.alliance);

      // Generate agreement
      const agreement = await generator.generateAgreement(systemTemplate, scenario.project, {
        contributors: scenario.contributors,
        currentUser: scenario.currentUser,
        fullName: scenario.currentUser.user_metadata.full_name
      });

      // Save generated agreement
      const filename = `${scenario.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}-${new Date().toISOString().split('T')[0]}.md`;
      const filepath = path.join(outputDir, filename);
      
      const metadata = `<!-- GENERATED AGREEMENT ANALYSIS
Generated: ${new Date().toISOString()}
Scenario: ${scenario.name}
Alliance: ${scenario.alliance.name}
Project: ${scenario.project.name}
Contributor: ${scenario.currentUser.user_metadata.full_name}
-->

`;

      fs.writeFileSync(filepath, metadata + agreement);
      
      // Perform detailed analysis
      const analysis = performDetailedAnalysis(agreement, scenario);
      
      results.push({
        scenario: scenario.name,
        agreement,
        analysis,
        filepath,
        timestamp: new Date().toISOString()
      });

      console.log(`✅ Generated: ${filename}`);
      console.log(`📊 Issues found: ${analysis.totalIssues}`);
      console.log(`📊 Accuracy: ${analysis.accuracyScore}%`);

    } catch (error) {
      console.error(`❌ Error generating agreement for ${scenario.name}:`, error);
      results.push({
        scenario: scenario.name,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Generate comprehensive analysis report
  console.log('\n📊 GENERATING COMPREHENSIVE ANALYSIS REPORT');
  console.log('==========================================');
  
  const report = generateComprehensiveReport(results);
  
  // Save analysis report
  const reportPath = path.join(outputDir, 'comprehensive-analysis-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  const summaryPath = path.join(outputDir, 'analysis-summary.md');
  fs.writeFileSync(summaryPath, generateMarkdownSummary(report));

  console.log(`📄 Analysis report saved: ${reportPath}`);
  console.log(`📄 Summary report saved: ${summaryPath}`);
  
  // Print key findings
  console.log('\n🔍 KEY FINDINGS');
  console.log('===============');
  console.log(`Total scenarios tested: ${report.totalScenarios}`);
  console.log(`Successful generations: ${report.successfulGenerations}`);
  console.log(`Failed generations: ${report.failedGenerations}`);
  console.log(`Average accuracy score: ${report.averageAccuracy}%`);
  console.log(`Total critical issues: ${report.totalCriticalIssues}`);
  
  if (report.commonIssues.length > 0) {
    console.log('\n⚠️  MOST COMMON ISSUES:');
    report.commonIssues.slice(0, 5).forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue.type}: ${issue.count} occurrences`);
    });
  }
  
  if (report.recommendations.length > 0) {
    console.log('\n💡 RECOMMENDATIONS:');
    report.recommendations.forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`);
    });
  }

  return results;
}

// Detailed analysis function
function performDetailedAnalysis(generatedAgreement, scenario) {
  const analysis = {
    criticalIssues: [],
    missingSections: [],
    incorrectVariables: [],
    structuralProblems: [],
    accuracyScore: 0,
    totalIssues: 0
  };

  // Expected sections from lawyer template
  const expectedSections = [
    'CONTRIBUTOR AGREEMENT',
    'Recitals',
    '1. Definitions',
    '2. Treatment of Confidential Information',
    '3. Ownership of Work Product',
    '4. Non-Disparagement',
    '5. Termination',
    '6. Equitable Remedies',
    '7. Assignment',
    '8. Waivers and Amendments',
    '9. Survival',
    '10. Status as Independent Contractor',
    '11. Representations and Warranties',
    '12. Indemnification',
    '13. Entire Agreement',
    '14. Governing Law',
    '15. Consent to Jurisdiction',
    '16. Settlement of Disputes',
    'SCHEDULE A',
    'SCHEDULE B'
  ];

  // Check for missing sections
  expectedSections.forEach(section => {
    if (!generatedAgreement.includes(section)) {
      analysis.missingSections.push(section);
    }
  });

  // Check for unreplaced variables
  const variablePlaceholders = [
    '[Company Legal Name]',
    '[COMPANY NAME]',
    '[Company Address]',
    '[Company State]',
    '[Company Signer Name]',
    '[Company Signer Title]',
    '[Company Billing Email]',
    '[Project Name]',
    '[Project Description]',
    '[Project Type]'
  ];

  variablePlaceholders.forEach(placeholder => {
    if (generatedAgreement.includes(placeholder)) {
      analysis.incorrectVariables.push({
        type: 'UNREPLACED_VARIABLE',
        placeholder,
        severity: 'CRITICAL'
      });
    }
  });

  // Check for hardcoded City of Gamers references
  if (generatedAgreement.includes('City of Gamers Inc.') &&
      scenario.alliance.name !== 'City of Gamers Inc.') {
    analysis.criticalIssues.push({
      type: 'HARDCODED_COMPANY',
      description: 'Hardcoded City of Gamers Inc. instead of user company',
      expected: scenario.alliance.name,
      found: 'City of Gamers Inc.'
    });
  }

  // Check for hardcoded Florida jurisdiction
  if (generatedAgreement.includes('Florida') &&
      !scenario.alliance.name.includes('Florida')) {
    analysis.criticalIssues.push({
      type: 'HARDCODED_JURISDICTION',
      description: 'Hardcoded Florida jurisdiction',
      note: 'Should be configurable based on user location'
    });
  }

  // Check for proper company information replacement
  if (!generatedAgreement.includes(scenario.alliance.name)) {
    analysis.criticalIssues.push({
      type: 'MISSING_COMPANY_NAME',
      description: 'Alliance/company name not found in agreement',
      expected: scenario.alliance.name
    });
  }

  // Check for proper project information
  if (!generatedAgreement.includes(scenario.project.name)) {
    analysis.criticalIssues.push({
      type: 'MISSING_PROJECT_NAME',
      description: 'Project name not found in agreement',
      expected: scenario.project.name
    });
  }

  // Check for proper contributor information
  const contributorName = scenario.currentUser.user_metadata.full_name;
  if (!generatedAgreement.includes(contributorName)) {
    analysis.criticalIssues.push({
      type: 'MISSING_CONTRIBUTOR_NAME',
      description: 'Contributor name not found in agreement',
      expected: contributorName
    });
  }

  // Check for Schedule A and B presence and content
  if (!generatedAgreement.includes('SCHEDULE A') ||
      !generatedAgreement.includes('Description of Services')) {
    analysis.structuralProblems.push('Schedule A missing or incomplete');
  }

  if (!generatedAgreement.includes('SCHEDULE B') ||
      !generatedAgreement.includes('Description of Consideration')) {
    analysis.structuralProblems.push('Schedule B missing or incomplete');
  }

  // Calculate total issues
  analysis.totalIssues = analysis.criticalIssues.length +
                        analysis.missingSections.length +
                        analysis.incorrectVariables.length +
                        analysis.structuralProblems.length;

  // Calculate accuracy score
  const totalChecks = expectedSections.length + variablePlaceholders.length + 5; // +5 for critical checks
  const passedChecks = totalChecks - analysis.totalIssues;
  analysis.accuracyScore = Math.max(0, Math.round((passedChecks / totalChecks) * 100));

  return analysis;
}

// Generate comprehensive report
function generateComprehensiveReport(results) {
  const report = {
    timestamp: new Date().toISOString(),
    totalScenarios: results.length,
    successfulGenerations: results.filter(r => !r.error).length,
    failedGenerations: results.filter(r => r.error).length,
    averageAccuracy: 0,
    totalCriticalIssues: 0,
    commonIssues: [],
    recommendations: [],
    detailedResults: results
  };

  const successfulResults = results.filter(r => !r.error);

  if (successfulResults.length > 0) {
    // Calculate average accuracy
    const totalAccuracy = successfulResults.reduce((sum, r) => sum + r.analysis.accuracyScore, 0);
    report.averageAccuracy = Math.round(totalAccuracy / successfulResults.length);

    // Count total critical issues
    report.totalCriticalIssues = successfulResults.reduce((sum, r) =>
      sum + r.analysis.criticalIssues.length, 0);

    // Find common issues
    const issueTypes = {};
    successfulResults.forEach(result => {
      result.analysis.criticalIssues.forEach(issue => {
        issueTypes[issue.type] = (issueTypes[issue.type] || 0) + 1;
      });
      result.analysis.incorrectVariables.forEach(issue => {
        issueTypes[issue.type] = (issueTypes[issue.type] || 0) + 1;
      });
    });

    report.commonIssues = Object.entries(issueTypes)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count);

    // Generate recommendations
    if (report.averageAccuracy < 95) {
      report.recommendations.push('CRITICAL: Agreement accuracy below 95% threshold - immediate system fixes required');
    }
    if (report.totalCriticalIssues > 0) {
      report.recommendations.push('URGENT: Critical issues found in agreement generation - review variable replacement system');
    }
    if (issueTypes.UNREPLACED_VARIABLE) {
      report.recommendations.push('HIGH: Fix variable replacement system - placeholders not being substituted');
    }
    if (issueTypes.HARDCODED_COMPANY) {
      report.recommendations.push('HIGH: Remove hardcoded company references - should use user data');
    }
    if (issueTypes.HARDCODED_JURISDICTION) {
      report.recommendations.push('MEDIUM: Make jurisdiction configurable based on user location');
    }
  }

  return report;
}

// Generate markdown summary
function generateMarkdownSummary(report) {
  return `# Agreement Generation Analysis Summary

**Generated:** ${report.timestamp}

## Overview
- **Total Scenarios Tested:** ${report.totalScenarios}
- **Successful Generations:** ${report.successfulGenerations}
- **Failed Generations:** ${report.failedGenerations}
- **Average Accuracy Score:** ${report.averageAccuracy}%
- **Total Critical Issues:** ${report.totalCriticalIssues}

## System Status
${report.averageAccuracy >= 95 ? '✅ **SYSTEM READY**' : '❌ **SYSTEM NOT READY**'} - ${report.averageAccuracy < 95 ? 'Below 95% accuracy threshold' : 'Meets accuracy requirements'}

## Most Common Issues
${report.commonIssues.length > 0 ?
  report.commonIssues.slice(0, 10).map((issue, i) =>
    `${i + 1}. **${issue.type}**: ${issue.count} occurrence(s)`
  ).join('\n') :
  'No common issues identified'
}

## Recommendations
${report.recommendations.length > 0 ?
  report.recommendations.map((rec, i) => `${i + 1}. ${rec}`).join('\n') :
  'No specific recommendations'
}

## Detailed Results
${report.detailedResults.map(result =>
  `### ${result.scenario}
- **Status:** ${result.error ? '❌ Failed' : '✅ Success'}
- **Accuracy:** ${result.analysis ? result.analysis.accuracyScore + '%' : 'N/A'}
- **Issues:** ${result.analysis ? result.analysis.totalIssues : 'N/A'}
${result.error ? `- **Error:** ${result.error}` : ''}
`).join('\n')}
`;
}

// Run the analysis
generateAndAnalyzeAgreements().catch(console.error);
